/vendor/
node_modules/
npm-debug.log
yarn-error.log
/storage/
composer.lock
# Laravel 4 specific
bootstrap/compiled.php
app/storage/

# Laravel 5 & <PERSON><PERSON> specific
public/storage
public/hot

# Laravel 5 & <PERSON><PERSON> specific with changed public path
public_html/storage
public_html/hot

storage/*.key
.env
Homestead.yaml
Homestead.json
/.vagrant
.phpunit.result.cache

.DS_Store
app/.DS_Store
app/Http/.DS_Store
app/Http/Controllers/.DS_Store
