<?php

// use App\Http\Controllers\Admin\HierarchyController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\{
    DashboardController,
    ProfileController,
    MailSettingController,
    UserImportController,
    UserController,
};
use App\Http\Controllers\Admin\Pdf\GenerateChallanPDfController;
use App\Http\Controllers\MasterCreationController\{
    UserCreationController,
    CreateNewUserController,
    CfaLocationMappingController,
    EligibilityVpoController,
    PriceCreationController,
};

use App\Http\Controllers\VendorAllocationChallan\{
    GenarateChallanController,
    MaintenaceController,
    OutletTransferController,
    PlacementController,
    PulloutController,
    ReplacementController
};

use App\Http\Controllers\UploadUtility\{
    CfaWiseInventoryController,
    RetailerMappingController
};

use App\Http\Controllers\ApprovalCenter\{
    ApprovalMaintenanceController,
    ApprovalOutletController,
    ApprovalPlacementController,
    ApprovalPulloutController,
    ApprovalReplacementController
};

use App\Http\Controllers\InventoryManagement\{
    InwordOutwordInventoryController,
    ScrapInventoryController,
    WorkingInventoryController,
};

use App\Http\Controllers\AdminReport\{
    AbTransferReportController,
    AuditReportController,
    MaintenanceReportController,
    MasterMappingReportController,
    PulloutReportController,
    ReplacementReportController
};
use App\Http\Controllers\ApprovalCenter\ApprovalReport\ChiilerPlacedPlacementReportController;
use App\Http\Controllers\ApprovalReport\ApprovalReportController;
use App\Http\Controllers\Reports\ReportsController;

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\ApprovalCenter\ApprovalReport\PlacementReportController;
use App\Http\Controllers\ApprovalCenter\ApprovalReport\MaintenanceReportDownloadController;
use App\Http\Controllers\ApprovalCenter\ApprovalReport\ReplacementReportDownloadController;
use App\Http\Controllers\CheckAssetDeploymentStatus;
use App\Http\Controllers\Reports\UserCreationReport\UserCreationReportDownloadController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::match(['get', 'post'], 'asset/check_asset_status', [CheckAssetDeploymentStatus::class, 'CheckAssetStatus'])->name('asset.check_asset_status');

// Route::get('/', function () {
//     return view('welcome');
// });
Route::get('/', [AuthenticatedSessionController::class, 'create'])
    ->middleware('guest')
    ->name('login');


Route::get('/test-mail', function () {

    $message = "Testing mail";

    \Mail::raw('Hi, welcome!', function ($message) {
        $message->to('<EMAIL>')
            ->subject('Testing mail');
    });

    dd('sent');
});
Route::get('/php_info', function () {
    echo "<pre>";
    phpinfo();
});

// Route::get('/dashboard', function () {
//     // return view('front.dashboard');
// })->middleware(['front'])->name('dashboard');


// require __DIR__.'/front_auth.php';

// Admin routes
// Route::get('/admin/dashboard', function () {
//     return view('dashboard.index');
// })->middleware(['auth'])->name('admin.dashboard');

require __DIR__ . '/auth.php';



// Route::get('users/import', 'App\Http\Controllers\Admin\UserController@userImportPage')->name('admin.users.userImportPage');
Route::middleware('auth')->group(function () {
    Route::namespace('App\Http\Controllers\Admin')->name('admin.')->prefix('admin')
        ->group(function () {
            Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
            Route::resource('roles', 'RoleController');
            Route::resource('permissions', 'PermissionController');
            Route::resource('users', 'UserController');
            Route::get('/user_edit/{user_code}', [UserController::class, 'user_edit_page'])->name('user_edit');
            // routes/web.php (or similar routes file)
            Route::get('user-creation-report-download', [UserCreationReportDownloadController::class, 'downloadReport'])
        ->name('user_creation_report_download');


            Route::post('/update_user_info', [UserController::class, 'UpdateUserInfo'])->name('update_user_info');
            // Route::get('users/import', 'UserController@userImportPage')->name('admin.users.userImportPage');
            // Route::get('users/user_import_page',[UserController::class,'userImportPage'])->name('users.user_import_page');


            Route::resource('posts', 'PostController');

            Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
            Route::put('/profile-update', [ProfileController::class, 'update'])->name('profile.update');
            Route::get('/mail', [MailSettingController::class, 'index'])->name('mail.index');
            Route::put('/mail-update/{mailsetting}', [MailSettingController::class, 'update'])->name('mail.update');

            Route::get('hierarchy/hierarchy_upload', [UserImportController::class, 'index'])->name('hierarchy.hierarchy_upload');
            Route::post('hierarchy/hierarchy_upload_store', [UserImportController::class, 'uploadHierarchyFile'])->name('hierarchy.hierarchy_upload_store');

            Route::get('import/user_import_page', [UserImportController::class, 'userImportPage'])->name('import.user_import_page');
            Route::post('import/user_master_import', [UserImportController::class, 'UploadUserMasterDetails'])->name('import.user_master_import');
            Route::get('import/user_upload_File', [UserImportController::class, 'upload_File'])->name('import.user_uploadFile');
            Route::get('download/generate_delivery_challan/{id}', [GenerateChallanPDfController::class, 'generateChallanPDFDownload'])->name('download.generate_delivery_challan');
            Route::get('download/multiple_challan_pdf', [GenerateChallanPDfController::class, 'generateMultipleRequestChallanPDF'])->name('download.multiple_challan_pdf');
            Route::get('download/generate_chiller_installation_challan_challan/{id}', [GenerateChallanPDfController::class, 'generateChillerInstallationChallanPDFDownload'])->name('download.generate_chiller_installation_challan_challan');
            Route::get('import/batch_upload_history_download', [UserImportController::class, 'BatchUploadHistoryDownload'])->name('import.batch_upload_history_download');
        });

    Route::namespace('App\Http\Controllers\MasterCreationController')->name('master_creation.')->prefix('master_creation')
        ->group(function () {
            Route::get('/user_creation', [UserCreationController::class, 'index'])->name('user_creation');
            Route::get('/create_new_user', [CreateNewUserController::class, 'index'])->name('create_new_user');
            Route::get('/cfa_location_mapping', [CfaLocationMappingController::class, 'index'])->name('cfa_location_mapping');
            Route::get('/eligibility_vpo', [EligibilityVpoController::class, 'index'])->name('eligibility_vpo');
            Route::get('/price_creation', [PriceCreationController::class, 'index'])->name('price_creation');
        });

    Route::namespace('App\Http\Controllers\VendorAllocationController')->name('vendor_allocation.')->prefix('vendor_allocation')
        ->group(function () {
            Route::get('/maintenance_allocation', [MaintenaceController::class, 'index'])->name('maintenance_allocation');
            Route::get('/maintenance_allocation_edit', [MaintenaceController::class, 'edit'])->name('maintenance_allocation_edit');
            Route::post('/allocate_placement_task', [PlacementController::class, 'allocateTaskToVE'])->name('allocate_placement_task');
            Route::post('/allocate_multiple_placement_task', [PlacementController::class, 'submitAllocateMultipleTaskToVE'])->name('allocate_multiple_placement_task');
            Route::get('/outlet_transfer_allocation', [OutletTransferController::class, 'index'])->name('outlet_transfer_allocation');
            Route::get('/outlet_transfer_allocation_edit', [OutletTransferController::class, 'edit'])->name('outlet_transfer_allocation_edit');
            Route::get('/placement_allocation', [PlacementController::class, 'index'])->name('placement_allocation');
            Route::get('/placement_allocation_edit/{id}', [PlacementController::class, 'edit'])->name('placement_allocation_edit');
            Route::get('/assigned_executive_allocation', [PlacementController::class, 'assignedVendorExecutiveAllocation'])->name('assigned_executive_allocation');
            Route::get('/delivery/challan', [GenarateChallanController::class, 'deliveryChallan'])->name('deliveryChallan');
            Route::get('/chiller/installation/challan', [GenarateChallanController::class, 'chillerInstallation'])->name('chillerInstallation');

            Route::get('/pullout_allocation', [PulloutController::class, 'index'])->name('pullout_allocation');
            Route::get('/pullout_allocation_edit', [PulloutController::class, 'edit'])->name('pullout_allocation_edit');
            Route::get('/replacement_allocation', [ReplacementController::class, 'index'])->name('replacement_allocation');
            Route::get('/replacement_allocation_edit', [ReplacementController::class, 'edit'])->name('replacement_allocation_edit');
        });


    Route::namespace('App\Http\Controllers\UploadUtility')->name('upload_utility.')->prefix('upload_utility')
        ->group(function () {
            Route::get('/cfa_wise_inventory', [CfaWiseInventoryController::class, 'index'])->name('cfa_wise_inventory');
            Route::post('/asset_inventory_approval', [CfaWiseInventoryController::class, 'assetInventoryStatusApprovalByAE'])->name('asset_inventory_approval');
            Route::get('/retailer_mapping', [RetailerMappingController::class, 'index'])->name('retailer_mapping');
        });


    Route::namespace('App\Http\Controllers\ApprovalCenter')->name('approval_center.')->prefix('approval_center')
        ->group(function () {
            Route::get('/maintenance', [ApprovalMaintenanceController::class, 'index'])->name('maintenance');
            Route::get('/maintenance_edit', [ApprovalMaintenanceController::class, 'edit'])->name('maintenance_edit');
            Route::get('/maintenance_request_report_download', [MaintenanceReportDownloadController::class, 'MaintenanceRequestReportExport'])->name('maintenance_request_report_download');

            Route::get('/outlet_transfer', [ApprovalOutletController::class, 'index'])->name('outlet_transfer');
            Route::get('/outlet_edit', [ApprovalOutletController::class, 'edit'])->name('outlet_edit');
            Route::get('/placement', [ApprovalPlacementController::class, 'index'])->name('placement');
            Route::post('/update_asset_type', [ApprovalPlacementController::class, 'UpdateRequestAssetType'])->name('update_asset_type');
            Route::get('/placement_request_report_download', [PlacementReportController::class, 'PlacementRequestReportExport'])->name('placement_request_report_download');
            Route::get('/placement_chiller_placed_request_report_download', [ChiilerPlacedPlacementReportController::class, 'PlacementRequestReportExport'])->name('placement_chiller_placed_request_report_download');
            Route::get('/replacement_request_report_download', [ReplacementReportDownloadController::class, 'RePlacementRequestReportExport'])->name('replacement_request_report_download');
            // Route::get('/placement_edit/{id}',[ApprovalPlacementController::class,'edit'])->name('placement_edit');
            Route::get('/placement_edit/{id}', [ApprovalPlacementController::class, 'edit'])->name('placement_edit');

            Route::post('/replacement_approved_reject', [ApprovalPlacementController::class, 'ApprovalReplacementRequestStatusUpdate'])->name('replacement_approved_reject');
            Route::get('/pullout', [ApprovalPulloutController::class, 'index'])->name('pullout');
            Route::get('/pullout_request_report_download', [ApprovalPulloutController::class, 'PulloutRequestReportExport'])->name('pullout_request_report_download');
            Route::get('/pullout_edit', [ApprovalPulloutController::class, 'edit'])->name('pullout_edit');
            Route::get('/replacement', [ApprovalReplacementController::class, 'index'])->name('replacement');
            Route::get('/replacement_edit', [ApprovalReplacementController::class, 'edit'])->name('replacement_edit');
        });

    Route::namespace('App\Http\Controllers\InventoryManagement')->name('inventory_managemant.')->prefix('inventory_managemant')
        ->group(function () {
            Route::get('/inword_outword', [InwordOutwordInventoryController::class, 'index'])->name('inword_outword');
            Route::get('/inword_outword_report_download', [InwordOutwordInventoryController::class, 'InwardOutwardRepoertDownload'])->name('inword_outword_report_download');
            // Route::get('/inword_outword_edit', [InwordOutwordInventoryController::class, 'edit'])->name('inword_outword_edit');
            Route::get('/scrap', [ScrapInventoryController::class, 'index'])->name('scrap');
            Route::get('/scrap_edit', [ScrapInventoryController::class, 'edit'])->name('scrap_edit');
            Route::get('/working_inventory', [WorkingInventoryController::class, 'index'])->name('working_inventory');
            Route::get('/working_inventory_edit', [WorkingInventoryController::class, 'edit'])->name('working_inventory_edit');
        });

    Route::namespace('App\Http\Controllers\AdminReport')->name('admin_report.')->prefix('admin_report')
        ->group(function () {
            Route::get('/ab_transfer_report', [AbTransferReportController::class, 'index'])->name('ab_transfer_report');
            Route::get('/audit_report', [AuditReportController::class, 'index'])->name('audit_report');
            Route::get('/maintenance_report', [MaintenanceReportController::class, 'index'])->name('maintenance_report');
            Route::get('/master_mapping_report', [MasterMappingReportController::class, 'index'])->name('master_mapping_report');
            Route::get('/placement_report', [PlacementReportController::class, 'index'])->name('placement_report');
            Route::get('/pullout_report', [PulloutReportController::class, 'index'])->name('pullout_report');
            Route::get('/replacement_report', [ReplacementReportController::class, 'index'])->name('replacement_report');
        });

    Route::namespace('App\Http\Controllers\ApprovalReport')->name('approval_report.')->prefix('approval_report')
        ->group(function () {
            Route::get('/placement_report', [ApprovalReportController::class, 'approvalPlacementReport'])->name('placement_report');
            Route::get('/replacement_report', [ ApprovalReportController::class, 'approvalReplacementReport'])->name('replacement_report');
            Route::get('/pullout_report', [ApprovalReportController::class, 'approvalPulloutReport'])->name('pullout_report');
            Route::get('/maintenance_report', [ApprovalReportController::class, 'approvalMaintenanceReport'])->name('maintenance_report');
            Route::get('/audit_report', [ApprovalReportController::class, 'approvalAuditReport'])->name('audit_report');
            Route::get('/asset_master_mapping_report', [ApprovalPlacementController::class, 'AssetMappingMaster'])->name('asset_master_mapping_report');
            Route::get('/cfa_wise_inventory_report', [ApprovalReportController::class, 'approvalCfaWiseReport'])->name('cfa_wise_inventory_report');
        });

    Route::namespace('App\Http\Controllers\Reports')->name('report.')->prefix('report')
        ->group(function () {
            Route::get('/placement_report', [ReportsController::class, 'placementReport'])->name('placement_report');
            Route::get('/replacement_report', [ReportsController::class, 'replacementReport'])->name('replacement_report');
            Route::get('/pullout_report', [ReportsController::class, 'pulloutReport'])->name('pullout_report');
            Route::get('/maintenance_report', [ReportsController::class, 'maintenanceReport'])->name('maintenance_report');
            Route::get('/audit_report', [ReportsController::class, 'auditReport'])->name('audit_report');
            Route::get('/asset_master_mapping_report', [ApprovalPlacementController::class, 'AssetMappingMaster'])->name('asset_master_mapping_report');
            Route::get('/asset_wise_inventory_report', [ReportsController::class, 'assetWiseReport'])->name('asset_wise_inventory_report');
            Route::get('/ab_transfer_report', [ReportsController::class, 'abTransferReport'])->name('ab_transfer_report');
        });
});
