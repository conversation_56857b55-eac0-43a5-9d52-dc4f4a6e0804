<?php

use App\Http\Controllers\API\AppForceUpdate\AppForceUpdateController;
use App\Http\Controllers\API\AssetAudit\AssetAuditAPIController;
use App\Http\Controllers\API\AssetAudit\VisitCheckinCheckoutController;
use App\Http\Controllers\API\Assetdetail\AssetDetailAPIController;
use App\Http\Controllers\API\AssetImage\AssetImageUploadController;
use App\Http\Controllers\API\AssetPlacement\AssetPlacementRequestController;
use App\Http\Controllers\API\CommonData\AssetCategoryCommonDataController;
use App\Http\Controllers\API\DeviceDetailController;
use App\Http\Controllers\API\IVY\IVYassetRetailerMaster;
use App\Http\Controllers\API\Maintenance\MaintenanceRequestController;
use App\Http\Controllers\API\User\AuthController;
use App\Http\Controllers\API\User\ForgePasswordController;
use App\Http\Controllers\API\Outlet\OutlletHierarchyAPIController;
use App\Http\Controllers\API\Pullout\PulloutRequestController;
use App\Http\Controllers\API\Replacement\ReplacementRequestController;
use App\Http\Controllers\API\User\VendorAuthController;
use App\Http\Controllers\API\V2\AssetPlacement\AssetPlacementRequestController_V2;
use App\Http\Controllers\API\V2\Maintenance\MaintenanceRequestControllerV2;
use App\Http\Controllers\API\V2\Pullout\PulloutRequestControllerV2;
use App\Http\Controllers\API\V2\Replacement\ReplacementRequestControllerV2;
use App\Http\Controllers\API\V2\Vendor\Placement\VendorPlacementTaskController_V2;
use App\Http\Controllers\API\Vendor\Maintenance\VendorMaintenanceTaskController;
use App\Http\Controllers\API\Vendor\Placement\VendorPlacementTaskController;
use App\Http\Controllers\API\Vendor\Pullout\VendorPulloutTaskController;
use App\Http\Controllers\API\Vendor\Replacement\VendorReplacementTaskController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\ValidateUserId;

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group(['prefix' => 'asset'], function () {
    // Public routes
    Route::get('app_info/appversion_force_update', [AppForceUpdateController::class, 'AssetAppVersionForceUpdate']);
});
Route::group(['prefix' => 'auth'], function () {
    // Public routes
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/createUser', [AuthController::class, 'cretaUser']);
});

Route::middleware('validateUserId')->group(function () {
    Route::get('asset/common-data', [AssetCategoryCommonDataController::class, 'index']);

    Route::group(['prefix' => 'device'], function () {
        Route::post('/device-details/update_device_info', [DeviceDetailController::class, 'updateUserDeviceInfo']);
    });
    Route::group(['prefix' => 'outlet'], function () {
        Route::post('/outlet-list', [OutlletHierarchyAPIController::class, 'getOutletList']);
        Route::post('/outlet-details', [OutlletHierarchyAPIController::class, 'getOutletDetails']);
        Route::post('/update-outlet-details', [OutlletHierarchyAPIController::class, 'updateOutletDetails']);
    });
    Route::group(['prefix' => 'asset_placement_request'], function () {
        Route::post('/create_asset_placement_request', [AssetPlacementRequestController::class, 'createNewAssetPlacement']);
        Route::post('/update_placement_request', [AssetPlacementRequestController::class, 'updateAssetPlacementRequest']);
        Route::post('/get_asset_placement_request_details', [AssetPlacementRequestController::class, 'getAssetPlacementRequestDetails']);
        Route::get('/get_asset_placement_request_list', [AssetPlacementRequestController::class, 'getAsseetPlacementRequestList']);
        Route::get('/update_apr_hierarchy', [AssetPlacementRequestController::class, 'update_apr_hierarchy']);
    });
    Route::group(['prefix' => 'asset_audit'], function () {
        Route::post('/create_asset_audit_request', [AssetAuditAPIController::class, 'assetAuditRequestStore']);
        Route::get('/get_asset_audit_request_list', [AssetAuditAPIController::class, 'getAuditRequestList']);
        Route::post('/get_asset_audit_request_details', [AssetAuditAPIController::class, 'getAuditRequestDetails']);
        Route::post('/outlet_visit_attandance', [VisitCheckinCheckoutController::class, 'CheckinCheckoutAttandanceMark']);
        Route::post('/last_outlet_checkin_checkout', [VisitCheckinCheckoutController::class, 'GetLastCheckInCheckOutDetails']);
    });
    Route::group(['prefix' => 'asset_details'], function () {
        Route::get('/get_outlet_asset_details', [AssetDetailAPIController::class, 'getAssetOutletDetailsList']);
    });
    Route::group(['prefix' => 'asset_image'], function () {
        Route::post('/asset_image_upload', [AssetImageUploadController::class, 'uploadassetImage']);
    });

    Route::group(['prefix' => 'pullout'], function () {
        Route::post('/pullout-create-request', [PulloutRequestController::class, 'pulloutCreate']);
        Route::post('/pullout-request-list', [PulloutRequestController::class, 'pulloutList']);
        Route::post('/pullout-request-dertails', [PulloutRequestController::class, 'pulloutDetails']);
        Route::post('/pullout-asset-list', [PulloutRequestController::class, 'pulloutAssetList']);
        Route::post('/pullout-asset-details', [PulloutRequestController::class, 'pulloutAssetDetails']);
    });
    Route::group(['prefix' => 'maintenance'], function () {
        Route::post('/maintenance_create_request', [MaintenanceRequestController::class, 'storeMaintenanceRequest']);
        Route::post('/get_maintenance_request_list', [MaintenanceRequestController::class, 'GetAssetMaintenanceRequestList']);
        Route::post('/get_maintenance_request_details', [MaintenanceRequestController::class, 'GetAssetMaintenanceRequestListDetails']);
    });

    Route::group(['prefix' => 'replacement'], function () {
        Route::post('/replacement_create_request', [ReplacementRequestController::class, 'createReplacementRequest']);
        Route::post('/get_replacement_request_list', [ReplacementRequestController::class, 'GetAssetReplacementRequestList']);
        Route::post('/get_replacement_request_details', [ReplacementRequestController::class, 'GetAssetReplacementRequestDetails']);
    });
});


Route::group(['prefix' => 'vendor'], function () {
    Route::post('auth/login', [VendorAuthController::class, 'login']);

    // Protected routes
    Route::middleware('auth:api')->group(function () {
        Route::group(['prefix' => 'auth'], function () {
            Route::get('/logout', [VendorAuthController::class, 'logout']);
        });
        Route::group(['prefix' => 'vendor_device_details'], function () {
            Route::post('/update_device_token', [VendorAuthController::class, 'UpdateDeviceFCMToken']);
        });
        Route::group(['prefix' => 'vendor_placement'], function () {
            Route::post('/vendor_placement_task_list', [VendorPlacementTaskController::class, 'VendorPlacementTaskList']);
            Route::post('/submit_placement_task', [VendorPlacementTaskController::class, 'submitPlacementTask']);
            Route::post('/complete_placement_task_details', [VendorPlacementTaskController::class, 'completePlacementTaskDetails']);
            Route::get('/get_vendor_dashboard_details', [VendorPlacementTaskController::class, 'GetVendorDashboardDetails']);
            Route::post('/verify_chiller_serial_number', [VendorPlacementTaskController::class, 'VendorVerifyChillerSerialNumber']);
        });
        Route::group(['prefix' => 'vendor_pullout'], function () {
            Route::post('/vendor_pullout_task_list', [VendorPulloutTaskController::class, 'VendorPulloutTaskList']);
            Route::post('/submit_pullout_task', [VendorPulloutTaskController::class, 'submitVendorPulloutTask']);
            Route::post('/complete_pullout_task_details', [VendorPulloutTaskController::class, 'completePulloutTaskDetails']);
        });
        Route::group(['prefix' => 'maintenance'], function () {
            Route::post('/submit_maintenance_task', [VendorMaintenanceTaskController::class, 'submitVendorMaintenanceTask']);
            Route::post('/vendor_maintenance_task_list', [VendorMaintenanceTaskController::class, 'VendorMaintenanceTaskList']);
            Route::post('/vendor_maintenance_task_details', [VendorMaintenanceTaskController::class, 'VendorMaintenanceTaskDetails']);
        });
        Route::group(['prefix' => 'replacement'], function () {
            Route::post('/submit_replacement_task', [VendorReplacementTaskController::class, 'submitVendorReplacementTask']);
            Route::post('/vendor_replacement_task_list', [VendorReplacementTaskController::class, 'VendorReplacementTaskList']);
            Route::post('/vendor_replacement_task_details', [VendorReplacementTaskController::class, 'VendorReplacementTaskListDetails']);
        });
    });
});

Route::group(['prefix' => 'forget'], function () {
    Route::post('/forget-password', [ForgePasswordController::class, 'index']);
    Route::post('/otp-verify', [ForgePasswordController::class, 'otpVerify']);
    Route::post('/change-password', [ForgePasswordController::class, 'changePassword']);
    // Route::get('/test', 'Admin\ForgePasswordController@test');
});

// Route::middleware(['ivyAuthentication', 'throttle.ivy:1,1440'])->group(function () {
//     Route::middleware('ivyAuthentication')->group(function () {
//         Route::group(['prefix' => 'ivy'], function () {
//             Route::post('/asset/asset_retailer_master', [IVYassetRetailerMaster::class, 'StoredIvyAssetRetailerMaster']);
//         });
//     });
// });

Route::middleware(['ivyAuthentication'])->prefix('ivy')->group(function () {
    Route::post('/asset/asset_retailer_master', [IVYassetRetailerMaster::class, 'StoredIvyAssetRetailerMaster']);
    // Route::post('/asset/asset_retailer_master', [IVYassetRetailerMaster::class, 'StoredIvyAssetRetailerMaster'])
    //     ->middleware('throttle.ivy:1,1440');
    Route::post('/asset/failed_ivy_record', [IVYassetRetailerMaster::class, 'FailedIvyAssetRecordList']);
});

#v2 Version
Route::group(['prefix' => 'V2/vendor'], function () {
    Route::middleware('auth:api')->group(function () {
        Route::group(['prefix' => 'vendor_placement'], function () {
            Route::post('/verify_chiller_serial_number', [VendorPlacementTaskController_V2::class, 'VendorVerifyChillerSerialNumber']);
        });
    });
});

Route::prefix('V2/asset_placement_request')
    ->middleware('validateUserId')
    ->group(function () {
        Route::post('/create_asset_placement_request', [AssetPlacementRequestController_V2::class, 'createNewAssetPlacement']);
    });
Route::prefix('V2/pullout')
    ->middleware('validateUserId')
    ->group(function () {
        Route::post('/pullout-create-request', [PulloutRequestControllerV2::class, 'pulloutCreate']);
});
Route::prefix('V2/maintenance')
    ->middleware('validateUserId')
    ->group(function () {
        Route::post('/maintenance_create_request', [MaintenanceRequestControllerV2::class, 'storeMaintenanceRequest']);
});

Route::prefix('V2/replacement')
    ->middleware('validateUserId')
    ->group(function () {
        Route::post('/replacement_create_request', [ReplacementRequestControllerV2::class, 'createReplacementRequest']);
});
