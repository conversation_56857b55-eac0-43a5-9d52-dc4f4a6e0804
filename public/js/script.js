//slider-login-img-start
const messageElement = document.querySelector('.alert');
if (messageElement) {
    setTimeout(() => {
        messageElement.style.display = 'none';
    }, 5000);
}
// window.onload = function () {
//     // Function to clone and append images
//     function cloneAndAppendImages() {
//         var container = document.getElementById('imageContainer');
//         if (!container) {
//             console.error('No element found with the ID "imageContainer".');
//             return;
//         }
//         var images = container.querySelectorAll('.col');
//         var width = 0;

//         images.forEach(function (image) {
//             width += image.offsetWidth; // Calculate total width of images
//         });

//         // Clone images and append to the container
//         images.forEach(function (image) {
//             var clone = image.cloneNode(true);
//             container.appendChild(clone);
//         });

//         container.style.width = width * 2 + 'px'; // Set container width to fit cloned images
//     }

//     cloneAndAppendImages(); // Call the function initially

//     // Function to reset scroll position when reaching the end
//     function resetScroll() {
//         var container = document.getElementById('imageContainer');
//         if (container) {

//             var scrollPosition = container.scrollLeft;

//             if (scrollPosition >= container.scrollWidth / 2) {
//                 container.scrollLeft = 0; // Reset scroll position to start
//             }
//         } else {
//             console.error('No element found with the ID "imageContainer".');
//         }
//     }
//     // Call the function repeatedly to keep the loop going
//     setInterval(resetScroll, 8); // Check scroll position every 100 milliseconds
// };
//slider-login-img-end
//button onclick highlight-start
$(document).ready(function () {
    var $buttons_on_click = $('.content button, .content a');
    var $buttons = $('.content button[data-status], .content a[data-status]'); // Select buttons and links with data-status attribute
    var $tableLinks = $('table a, .uploadfile-excle a, .modal-header .btn-close');

    var currentURL = window.location.href;

    // Function to apply styles
    function applyStyles() {
        $buttons.removeClass('active').removeAttr('style');
        $tableLinks.removeClass('active').removeAttr('style');
        $(this).addClass('active').css({ 'background': '#01d7b9', 'border-color': 'transparent', 'color': '#fff' });
    }

    // Function to check URL and apply styles accordingly
    function checkURLAndApplyStyles() {
        var status = currentURL.match(/status=([^&]*)/);
        if (status && status[1]) {
            $buttons.filter('[data-status="' + status[1] + '"]').each(applyStyles);
        }
        else {
            $buttons.filter('[data-status="All"]').each(applyStyles);
        }
    }
    checkURLAndApplyStyles();
    // Click event for buttons
    $buttons_on_click.on('click', function () {
        $buttons_on_click.removeClass('active').removeAttr('style');
        $(this).addClass('active').each(applyStyles);
        $tableLinks.removeClass('active').removeAttr('style');
    });
});
//button onclick table row images view in modal
function viewImages(imgElement) {
    document.getElementById('modal_id_img_view').setAttribute('src', imgElement.getAttribute('src'));
}
//button onclick highlight-end
$(document).ready(function () {
    var $wrapper = $('.main-wrapper'); var $slimScrolls = $('.slimscroll'); var $pageWrapper = $('.page-wrapper'); feather.replace(); $(window).resize(function () { if ($('.page-wrapper').length > 0) { var height = $(window).height(); $(".page-wrapper").css("min-height", height); } }); $('body').append('<div class="sidebar-overlay"></div>');
    $(document).on('click', '#mobile_btn', function () {
        $wrapper.toggleClass('slide-nav');
        $('.sidebar').toggleClass('sidebarHide');
        $('.sidebar-overlay').toggleClass('opened');
        $('html').addClass('menu-opened'); $('#task_window').removeClass('opened');
        return false;
    });
    $(".sidebar-overlay").on("click", function () { $('html').removeClass('menu-opened'); $(this).removeClass('opened'); $wrapper.removeClass('slide-nav'); $('.sidebar-overlay').removeClass('opened'); $('#task_window').removeClass('opened'); }); $(document).on("click", ".hideset", function () { $(this).parent().parent().parent().hide(); }); $(document).on("click", ".delete-set", function () { $(this).parent().parent().hide(); }); if ($('.product-slide').length > 0) { $('.product-slide').owlCarousel({ items: 1, margin: 30, dots: false, nav: true, loop: false, responsiveClass: true, responsive: { 0: { items: 1 }, 800: { items: 1 }, 1170: { items: 1 } } }); }
    if ($('.owl-product').length > 0) { var owl = $('.owl-product'); owl.owlCarousel({ margin: 10, dots: false, nav: true, loop: false, touchDrag: false, mouseDrag: false, responsive: { 0: { items: 2 }, 768: { items: 4 }, 1170: { items: 8 } } }); }
    $(document).ready(function () {
        if ($('.datanew').length > 0) {
            var table = $('.datanew').DataTable({
                "bFilter": true,
                "ordering": true,
                "language": {
                    search: ' ',
                    sLengthMenu: '_MENU_',
                    searchPlaceholder: "Search...",
                },
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"custom-pagination"lfr>t<"custom-pagination"ip>',
            });

            // Move the pagination control to a different container
            $('.dataTables_paginate').detach().appendTo('.custom-pagination');
            $('.dataTables_filter').appendTo('#tableSearch');
            $('.dataTables_filter').appendTo('.search-input');
        }
    });

    function readURL(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader(); reader.onload = function (e) { $('#blah').attr('src', e.target.result); }
            reader.readAsDataURL(input.files[0]);
        }
    }
    $("#imgInp").change(function () { readURL(this); }); if ($('.datatable').length > 0) { $('.datatable').DataTable({ "bFilter": false }); }
    setTimeout(function () { $('#global-loader'); setTimeout(function () { $("#global-loader").fadeOut("slow"); }, 100); }, 500); if ($('.datetimepicker').length > 0) { $('.datetimepicker').datetimepicker({ format: 'DD-MM-YYYY', icons: { up: "fas fa-angle-up", down: "fas fa-angle-down", next: 'fas fa-angle-right', previous: 'fas fa-angle-left' } }); }
    if ($('.toggle-password').length > 0) { $(document).on('click', '.toggle-password', function () { $(this).toggleClass("fa-eye fa-eye-slash"); var input = $(".pass-input"); if (input.attr("type") == "password") { input.attr("type", "text"); } else { input.attr("type", "password"); } }); }
    if ($('.toggle-passwords').length > 0) { $(document).on('click', '.toggle-passwords', function () { $(this).toggleClass("fa-eye fa-eye-slash"); var input = $(".pass-inputs"); if (input.attr("type") == "password") { input.attr("type", "text"); } else { input.attr("type", "password"); } }); }
    if ($('.toggle-passworda').length > 0) { $(document).on('click', '.toggle-passworda', function () { $(this).toggleClass("fa-eye fa-eye-slash"); var input = $(".pass-inputs"); if (input.attr("type") == "password") { input.attr("type", "text"); } else { input.attr("type", "password"); } }); }
    // if ($('.select').length > 0) { $('.select').select2({ minimumResultsForSearch: -1, width: '100%' }); }
    if ($('.counter').length > 0) { $('.counter').counterUp({ delay: 20, time: 2000 }); }
    if ($('#timer-countdown').length > 0) { $('#timer-countdown').countdown({ from: 180, to: 0, movingUnit: 1000, timerEnd: undefined, outputPattern: '$day Day $hour : $minute : $second', autostart: true }); }
    if ($('#timer-countup').length > 0) { $('#timer-countup').countdown({ from: 0, to: 180 }); }
    if ($('#timer-countinbetween').length > 0) { $('#timer-countinbetween').countdown({ from: 30, to: 20 }); }
    if ($('#timer-countercallback').length > 0) { $('#timer-countercallback').countdown({ from: 10, to: 0, timerEnd: function () { this.css({ 'text-decoration': 'line-through' }).animate({ 'opacity': .5 }, 500); } }); }
    if ($('#timer-outputpattern').length > 0) { $('#timer-outputpattern').countdown({ outputPattern: '$day Days $hour Hour $minute Min $second Sec..', from: 60 * 60 * 24 * 3 }); }
    if ($('#summernote').length > 0) { $('#summernote').summernote({ height: 300, minHeight: null, maxHeight: null, focus: true }); }
    if ($slimScrolls.length > 0) { $slimScrolls.slimScroll({ height: 'auto', width: '100%', position: 'right', size: '7px', color: '#ccc', wheelStep: 10, touchScrollStep: 100 }); var wHeight = $(window).height() - 60; $slimScrolls.height(wHeight); $('.sidebar .slimScrollDiv').height(wHeight); $(window).resize(function () { var rHeight = $(window).height() - 60; $slimScrolls.height(rHeight); $('.sidebar .slimScrollDiv').height(rHeight); }); }
    var Sidemenu = function () { this.$menuItem = $('#sidebar-menu a'); }; function init() {
        var $this = Sidemenu; $('#sidebar-menu a').on('click', function (e) {
            if ($(this).parent().hasClass('submenu')) { e.preventDefault(); }
            if (!$(this).hasClass('subdrop')) { $('ul', $(this).parents('ul:first')).slideUp(250); $('a', $(this).parents('ul:first')).removeClass('subdrop'); $(this).next('ul').slideDown(350); $(this).addClass('subdrop'); } else if ($(this).hasClass('subdrop')) { $(this).removeClass('subdrop'); $(this).next('ul').slideUp(350); }
            //page refresh on sidebar ul list hide
            window.onload = function () {
                var elements = document.querySelectorAll('.submenu > a');
                for (var i = 0; i < elements.length; i++) {
                    elements[i].classList.remove('subdrop');
                    var ul = elements[i].closest('.submenu').querySelector('ul');
                    ul.style.display = '';
                }
            };

        }); $('#sidebar-menu ul li.submenu a.active').parents('li:last').children('a:first').addClass('active').trigger('click');
    }
    // init(); $(document).on('mouseover', function (e) {
    //     e.stopPropagation(); if ($('body').hasClass('mini-sidebar') && $('#toggle_btn').is(':visible')) {
    //         var targ = $(e.target).closest('.sidebar, .header-left').length; if (targ) { $('body').addClass('expand-menu'); $('.subdrop + ul').slideDown(); } else { $('body').removeClass('expand-menu'); $('.subdrop + ul').slideUp(); }
    //         return false;
    //     }
    //});
    init(); $(document).on('mouseover', function (e) {
        e.stopPropagation(); if ($('body').hasClass('mini-sidebar') && $('#toggle_btn').is(':visible')) {
            var targ = $(e.target).closest('.sidebar, .header-left').length; if (targ) { $('body'); } else {
                $('body').removeClass('expand-menu'); $('.subdrop + ul').slideUp();

            }
            return false;
        }
    });
    // $(document).on('click', '#toggle_btn', function () {
    //     if ($('body').hasClass('mini-sidebar')) { $('body').removeClass('mini-sidebar'); $(this).addClass('active'); $('.subdrop + ul').slideDown(); localStorage.setItem('screenModeNightTokenState', 'night'); setTimeout(function () { $("body").removeClass("mini-sidebar"); $(".header-left").addClass("active"); }, 100); } else { $('body').addClass('mini-sidebar'); $(this).removeClass('active'); $('.subdrop + ul').slideUp(); localStorage.removeItem('screenModeNightTokenState', 'night'); setTimeout(function () { $("body").addClass("mini-sidebar"); $(".header-left").removeClass("active"); }, 100); }
    //     return false;
    // });
    $(document).on('click', '#toggle_btn', function () {
        if ($('body').hasClass('mini-sidebar')) { $('body').removeClass('mini-sidebar'); $(this).addClass('active'); localStorage.setItem('screenModeNightTokenState', 'night'); setTimeout(function () { $("body").removeClass("mini-sidebar"); $(".header-left").addClass("active"); }, 100); } else { $('body').addClass('mini-sidebar'); $(this).removeClass('active'); $('.subdrop + ul').slideUp(); localStorage.removeItem('screenModeNightTokenState', 'night'); setTimeout(function () { $("body").addClass("mini-sidebar"); $(".header-left").removeClass("active"); }, 100); }
        return false;
    });
    if (localStorage.getItem('screenModeNightTokenState') == 'night') { setTimeout(function () { $("body").removeClass("mini-sidebar"); $(".header-left").addClass("active"); }, 100); }
    $('.submenus').on('click', function () { $('body').addClass('sidebarrightmenu'); }); $('#searchdiv').on('click', function () { $('.searchinputs').addClass('show'); }); $('.search-addon span').on('click', function () { $('.searchinputs').removeClass('show'); }); $(document).on('click', '#filter_search', function () { $('#filter_inputs').slideToggle("slow"); }); $(document).on('click', '#filter_search1', function () { $('#filter_inputs1').slideToggle("slow"); }); $(document).on('click', '#filter_search2', function () { $('#filter_inputs2').slideToggle("slow"); }); $(document).on('click', '#filter_search', function () { $('#filter_search').toggleClass("setclose"); }); $(document).on("click", ".productset", function () { $(this).toggleClass("active"); }); $('.inc.button').click(function () { var $this = $(this), $input = $this.prev('input'), $parent = $input.closest('div'), newValue = parseInt($input.val()) + 1; $parent.find('.inc').addClass('a' + newValue); $input.val(newValue); newValue += newValue; }); $('.dec.button').click(function () { var $this = $(this), $input = $this.next('input'), $parent = $input.closest('div'), newValue = parseInt($input.val()) - 1; console.log($parent); $parent.find('.inc').addClass('a' + newValue); $input.val(newValue); newValue += newValue; }); if ($('.custom-file-container').length > 0) {
        var firstUpload = new FileUploadWithPreview('myFirstImage')
        var secondUpload = new FileUploadWithPreview('mySecondImage')
    }
    $('.counters').each(function () { var $this = $(this), countTo = $this.attr('data-count'); $({ countNum: $this.text() }).animate({ countNum: countTo }, { duration: 2000, easing: 'linear', step: function () { $this.text(Math.floor(this.countNum)); }, complete: function () { $this.text(this.countNum); } }); }); if ($('.toggle-password').length > 0) { $(document).on('click', '.toggle-password', function () { $(this).toggleClass("fa-eye fa-eye"); var input = $(".pass-input"); if (input.attr("type") == "text") { input.attr("type", "text"); } else { input.attr("type", "password"); } }); }
    if ($('.win-maximize').length > 0) { $('.win-maximize').on('click', function (e) { if (!document.fullscreenElement) { document.documentElement.requestFullscreen(); } else { if (document.exitFullscreen) { document.exitFullscreen(); } } }) }
    $(document).on('click', '#check_all', function () { $('.checkmail').click(); return false; }); if ($('.checkmail').length > 0) { $('.checkmail').each(function () { $(this).on('click', function () { if ($(this).closest('tr').hasClass('checked')) { $(this).closest('tr').removeClass('checked'); } else { $(this).closest('tr').addClass('checked'); } }); }); }
    if ($('.popover-list').length > 0) {
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) { return new bootstrap.Popover(popoverTriggerEl) })
    }
    if ($('.clipboard').length > 0) { var clipboard = new Clipboard('.btn'); }
    var chatAppTarget = $('.chat-window'); (function () {
        if ($(window).width() > 991)
            chatAppTarget.removeClass('chat-slide'); $(document).on("click", ".chat-window .chat-users-list a.media", function () {
                if ($(window).width() <= 991) { chatAppTarget.addClass('chat-slide'); }
                return false;
            }); $(document).on("click", "#back_user_list", function () {
                if ($(window).width() <= 991) { chatAppTarget.removeClass('chat-slide'); }
                return false;
            });
    })();

    $(document).on('click', '.mail-important', function () { $(this).find('i.fa').toggleClass('fa-star').toggleClass('fa-star-o'); });
    var selectAllItems = "#select-all";
    var checkboxItem = ":checkbox";
    $(selectAllItems).click(function () {
        if (this.checked) {
            $(checkboxItem).each(function () {
                if (!this.disabled) {
                    // this.checked = true;
                    this.checked = $(selectAllItems).prop('checked');
                }

            });
        } else {
            $(checkboxItem).each(function () { this.checked = false; });
        }
    });
    if ($('[data-bs-toggle="tooltip"]').length > 0) {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) { return new bootstrap.Tooltip(tooltipTriggerEl) })
    }

    //$('#sidebar').css({ marginLeft: "0px" });

    // if ($(window).width() < 992 && $(windows).width > 350) {
    //     $('#sidebar').css({ marginLeft: "0px" });

    //     // Toggle sidebar when mobile button is clicked
    //     $('#mobile_btn').on("click", function () {
    //         $('#sidebar').toggle();
    //     });
    // } else {
    //     $('#sidebar').css({ marginLeft: "570px" });
    // }


    $('.open-layout').on("click", function (s) {
        s.preventDefault();
        $('.sidebar-layout').addClass('show-layout');
        $('.sidebar-settings').removeClass('show-settings');
    });
    $('.btn-closed').on("click", function (s) {
        s.preventDefault();
        $('.sidebar-layout').removeClass('show-layout');

    });
    $('.open-settings').on("click", function (s) {
        s.preventDefault();
        $('.sidebar-settings').addClass('show-settings');
        $('.sidebar-layout').removeClass('show-layout');
    }); $('.btn-closed').on("click", function (s) {
        s.preventDefault();
        $('.sidebar-settings').removeClass('show-settings');
    }); $('.open-siderbar').on("click", function (s) {
        s.preventDefault();
        $('.siderbar-view').addClass('show-sidebar');
    });
    $('.btn-closed').on("click", function (s) {
        s.preventDefault();
        $('.siderbar-view').removeClass('show-sidebar');
    }); if ($('.toggle-switch').length > 0) {
        const toggleSwitch = document.querySelector('.toggle-switch input[type="checkbox"]');
        const currentTheme = localStorage.getItem('theme');
        var app = document.getElementsByTagName("BODY")[0];
        if (currentTheme) {
            app.setAttribute('data-theme', currentTheme);
            if (currentTheme === 'dark') { toggleSwitch.checked = true; }
        }
        function switchTheme(e) {
            if (e.target.checked) { app.setAttribute('data-theme', 'dark'); localStorage.setItem('theme', 'dark'); }
            else { app.setAttribute('data-theme', 'light'); localStorage.setItem('theme', 'light'); }
        }
        toggleSwitch.addEventListener('change', switchTheme, false);
    }
    if (window.location.hash == "#LightMode") { localStorage.setItem('theme', 'dark'); }
    else { if (window.location.hash == "#DarkMode") { localStorage.setItem('theme', 'light'); } }
    $('ul.tabs li').click(function () {
        var $this = $(this);
        var $theTab = $(this).attr('id');
        console.log($theTab);
        if ($this.hasClass('active')) { } else {
            $this.closest('.tabs_wrapper').find('ul.tabs li, .tabs_container .tab_content').removeClass('active');
            $('.tabs_container .tab_content[data-tab="' + $theTab + '"], ul.tabs li[id="' + $theTab + '"]').addClass('active');
        }
    });


});
var current = location.pathname;
$('ul li a').each(function () {
    var $this = $(this);
    if ($this.attr('href')?.indexOf(current) !== -1) {
        $this.addClass('active');
        $this.parents('li').addClass('active'); // Add 'active' class to parent li elements
    }
});
//date range function
$(document).ready(function () {
    $('#rangestart').calendar({
        type: 'date',
        endCalendar: $('#rangeend')
    });
    $('#rangeend').calendar({
        type: 'date',
        startCalendar: $('#rangestart')
    });
});
//user select creation form function
$(window).on('load', function () {
    $("#target_User").change();
});
// $(document).ready(function () {
$(document).ready(function () {

    $(".user-fields").hide();
    $(".defaultSoShow").show();
    $("#user_type").on("change", function () {
        var selectedUserType = $(this).val();
        $(".user-fields").hide().find(':input').prop('disabled', true); // Hide and disable all user fields
        $("#" + selectedUserType + "Fields").show().find(':input').prop('disabled', false); // Show and enable fields for selected user type
    });

    $("#template_download").on("click", function (e) {
        var href = $(this).attr("href");
        if (!href) {
            e.preventDefault(); // Prevent the click if href is not set
            alert("No template URL available for download.");
        }
    });
    $("#target_User").on("change", function () {
        $('.validation-error').empty();
        var target_User = $(this).val();
        var selectedOption = $(this).find('option:selected');
        var templateURL = selectedOption.attr('data-templateformat') || ''; // Default to empty string if undefined
        $("#template_download").attr("href", templateURL);
        $("#common_user_form").hide();
        $("#form_title").text(target_User);
        $("#user_type").val(target_User);
        $("#error-message").text("").hide();
        $('#selectedRole').val(target_User);
        $(".user-fields").hide().find(':input').prop('disabled', true);
        $("#common_user_form").hide().find(':input').prop('disabled', false);
        if (target_User == 'RBDM_GTMS' || target_User == 'RSM' || target_User == 'ASM' || target_User == 'SO' || target_User == 'DISTRIBUTOR' || target_User == 'DSR') {
            $("#common_user_form").show();
            $('.formUser').show();
            $(".user-fields").hide().find(':input').prop('disabled', true);
            $(".RETAILERFields").hide().find(':input').prop('disabled', true);
            $(".CFAFields").hide().find(':input').prop('disabled', true);
            $("#" + target_User + "Fields").show().find(':input').prop('disabled', false);
            if (target_User === 'RBDM_GTMS') {
                $(".channelFields").hide();
                $('.RSMFields').hide();
                $('.ASMFields').hide();
                $('.SOFields').hide();
                $('.DistributorFields').hide();
                $('.DSRFields').hide();
                $(".regionNameFields").hide();
                $(".regionCodeFields").hide();
                $(".teritoryFields").hide();
                $(".RBDMFields").hide();
                // Show and enable fields for selected target user
            }
            else if (target_User === 'RSM') {
                $(".channelFields").hide();
                $('.RSMFields').hide();
                $('.ASMFields').hide();
                $('.SOFields').hide();
                $('.DistributorFields').hide();
                $('.DSRFields').hide();
                $(".regionNameFields").show();
                $(".regionCodeFields").show();
                $(".teritoryFields").hide();
                $(".RBDMFields").show();
            } else if (target_User === 'ASM') {
                $('.RSMFields').show();
                $('.SOFields').hide();
                $('.DistributorFields').hide();
                $('.DSRFields').hide();
                $('.ASMFields').hide();
                $(".RBDMFields").show();
                $(".areaCodeFields").show();
                $(".regionNameFields").hide();

            } else if (target_User === 'SO') {
                $('.ASMFields').show();
                $('.RSMFields').show();
                $('.SOFields').hide();
                $('.DistributorFields').hide();
                $('.DSRFields').hide();
                $(".channelFields").hide();
                $(".regionCodeFields").hide();
                $(".areaCodeFields").hide();
                $(".cfaCodeFields").hide();
                $(".RBDMFields").show();
                $(".regionNameFields").hide();
            } else if (target_User === 'DISTRIBUTOR') {
                $('.ASMFields').show();
                $('.RSMFields').show();
                $('.SOFields').show();
                $('.DistributorFields').hide();
                $('.DSRFields').hide();
                $(".RBDMFields").show();
                $(".teritoryFields").hide();
                $(".cfaCodeFields").show();
                $(".regionCodeFields").hide();
                $(".areaCodeFields").hide();
                $(".regionNameFields").hide();
            } else if (target_User === 'DSR') {
                $('.ASMFields').show();
                $('.RSMFields').show();
                $('.SOFields').show();
                $('.DistributorFields').show();
                $('.DSRFields').hide();
                $(".RBDMFields").show();
                $(".teritoryFields").hide();
                $(".regionCodeFields").hide();
                $(".areaCodeFields").hide();
                $(".regionNameFields").hide();
            }
        }
        else {
            $("#common_user_form").hide().find(':input').prop('disabled', true);
            if ($("#" + target_User + "Fields").length) {
                // $(".user-fields").hide();
                $(".user-fields").hide().find(':input').prop('disabled', true);
                $("#teritoryFields").hide().find(':input').prop('disabled', true);
                $('#selectedRole').val(target_User);
                // $("#" + target_User + "Fields").show();
                $("#" + target_User + "Fields").show().find(':input').prop('disabled', false); // Show and enable fields for selected target user
                $("#error-message").hide();
                $('.formUser').show();
            }
            else {
                $('.formUser').hide();
                $("#error-message").text("Form for selected role does not exist").show();
            }
        }

    });

});
//     $(".user-fields").hide();
//     $(".defaultSoShow").show();
//     $("#target_User").on("change", function () {
//         var target_User = $(this).val();
//         $("#user_type").val(target_User);
//         $('.formUser').show();
//         $(".user-fields").hide();
//         if ($("#" + target_User + "Fields").length) {
//             $('#selectedRole').val(target_User);
//             $("#" + target_User + "Fields").show();
//             $("#error-message").hide();
//         } else {
//             $('.formUser').hide();
//             $("#error-message").text("Form for selected role does not exist").show();
//         }
//     });
// });
//user creation DropDown input - select
$('.custom_select_input').val('default');
$(".custom_select_input").change(function () {
    $(this).next("input").val($(this).children("option:selected").text());
    $(this).val('default');
});

//chose images modal
(function () {
    $('.upload-input-file').each(function () {
        var $input = $(this),
            $label = $input.next('.js-labelFile'),
            labelVal = $label.html();

        $input.on('change', function (element) {
            var fileName = '';
            if (element.target.value) fileName = element.target.value.split('\\').pop();
            fileName ? $label.addClass('has-file').find('.js-fileName').html(fileName) : $label.removeClass('has-file').html(labelVal);
        });
    });

})();

//overlay-page-loder after 1min for users cannnot load properly.
$(document).ready(function () {
    // Will wait for everything on the page to load.
    // $(window).bind('load', function () {
    //     $('body').show();
    //     setTimeout(function () {
    //         $('.overlay-page-loder').hide();
    //     }, 100)
    // });
    // Will remove overlay-page-loder after 1min for users cannnot load properly.
    // setTimeout(function () {
    //     $(' body').show();
    // },);
})


