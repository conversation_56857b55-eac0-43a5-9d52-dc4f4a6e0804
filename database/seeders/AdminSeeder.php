<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Constants\RolePermissionConstants;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        #create SuperAdmin Details
        $superaAdmin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('Superadmin@2024'),
            'mobile_number'=>"7017169609",
            'profile' => 'user.avif',
        ]);

        #Create Admin details
        $admin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('Admin@2024'),
            'mobile_number'=>"7017169608",
            'profile' => 'user.avif',
        ]);

        $so_user = User::create([
            'name' => 'SO User',
            'email' => '<EMAIL>',
            'password' => bcrypt('Admin@2024'),
            'mobile_number'=>"7017169607",
            'profile' => 'user.avif',
        ]);
        $dsr_admin = User::create([
            'name' => 'DSR User',
            'email' => '<EMAIL>',
            'password' => bcrypt('Admin@2024'),
            'mobile_number'=>"7017169606",
            'profile' => 'user.avif',
        ]);
        $writer = User::create([
            'name' => 'writer',
            'email' => '<EMAIL>',
            'mobile_number'=>"7017169605",
            'password' => bcrypt('password'),
        ]);

        // Create roles
        foreach (RolePermissionConstants::ROLES as $roleName) {
            Role::create(['name' => $roleName]);
        }

        // Create permissions
        foreach (RolePermissionConstants::PERMISSIONS as $permissionName) {
            Permission::create(['name' => $permissionName]);
        }

        // Assign roles to users
        $superaAdmin->assignRole('SUPERADMIN');
        $admin->assignRole('ADMIN');
        $so_user->assignRole('SO');
        $dsr_admin->assignRole('DSR');
        $writer->assignRole('writer');

        // Give all permissions to admin_role
        Role::findByName('ADMIN')->givePermissionTo(Permission::all());
        Role::findByName('SUPERADMIN')->givePermissionTo(Permission::all());
    }
}
