<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Mailsetting;

class MailsettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Mailsetting::create([
            'mail_transport'            =>'smtp',
            'mail_host'                 =>'email-smtp.ap-south-1.amazonaws.com',
            'mail_port'                 =>'587',
            'mail_username'             =>'AKIAU7PBKP7NS3ELEBWD',
            'mail_password'             =>'BE7NH16PNqThr+14U6nHgYIOTuDMOBaFgFStKFdawk/G',
            'mail_encryption'           =>'tls',
            'mail_from'                 => '<EMAIL>',
        ]);
    }
}
