<?php

namespace Database\Seeders;

use App\Models\OutletAssetDetailsModel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AssetOutletDetailsMapping extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                'asset_description' => 'New 30L - CT 30',
                'asset_barcode' => 'M0002415',
                'outlet_code' => 'RETN_1612905',
                'outlet_name' => 'AMAR MEDICOS',
                'placement_request_number' => 'APR03072952',
                'date_of_placement' => now()->toDateString(),
                'asset_assigned_by' => 40318300,
                'asset_assigned_time' => now(),
                'last_audit_time' => now(),
            ],
            [
                'asset_description' => 'New 60L - VT30',
                'asset_barcode' => 'BAR0000613',
                'outlet_code' => 'RETN_1612975',
                'outlet_name' => 'SHRI RAM MEDICOS',
                'placement_request_number' => 'APR03072909',
                'date_of_placement' => now()->toDateString(),
                'asset_assigned_by' => '40318300',
                'asset_assigned_time' => now(),
                'last_audit_time' => now(),
            ],
        ];

        foreach ($data as $item) {
            OutletAssetDetailsModel::create($item);
        }

    }
}
