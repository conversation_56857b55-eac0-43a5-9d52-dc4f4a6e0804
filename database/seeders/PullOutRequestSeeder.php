<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PullOutRequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $names = [
            'Outlet Permanently Closed',
            'Chiller not working',
            'VPO Criteria not met',
            'Others', 
        ];

        $records = [];

        foreach ($names as $name) {
            $records[] = [
                'name' => $name,
                'type' => 'pullout_request',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        DB::table('asset_common_category')->insert($records);
    }
}
