<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AssetCommonCategory;

class AdditionalDataSeeder extends Seeder
{
    public function run()
    {
        // Types available

        $types = [
            'SRC 30' => 0,
            'SRC 60' => 0,
            'New 30L - CT 30' => 1800,
            'New 60L - VT30' => 1800,
            'SRC280' => 5500,
            'SRC380' => 0
        ];

        foreach ($types as $name => $vpo_target) {
            AssetCommonCategory::create([
                'name' => $name,
                'type' => 'chiller_size',
                'vpo_target' => $vpo_target
            ]);
        }

        // Equipment available
        AssetCommonCategory::create([
            'name' => 'Chiller Stand',
            'type' => 'equipment' // Lowercased to match the enum value
        ]);

        // Competitor Sizes available
        $sizes = ['30 LTR', '60LTR', '200 LTR', '300 LTR', 'Double door'];
        foreach ($sizes as $size) {
            AssetCommonCategory::create([
                'name' => $size,
                'type' => 'competitor_sizes' // Lowercased to match the enum value
            ]);
        }

        // Companies Available
        $companies = ['Mondelez', 'Nestle', 'Hershey’s', 'Others'];
        foreach ($companies as $company) {
            AssetCommonCategory::create([
                'name' => $company,
                'type' => 'company' // Lowercased to match the enum value
            ]);
        }

        // Locations Available
        $locations = ['Hot spot', 'Hot zone', 'Category space', 'Others'];
        foreach ($locations as $location) {
            AssetCommonCategory::create([
                'name' => $location,
                'type' => 'location' // Lowercased to match the enum value
            ]);
        }
        // Locations Available
        $locations = ['Hot spot', 'Hot zone', 'Category space', 'Others'];
        foreach ($locations as $location) {
            AssetCommonCategory::create([
                'name' => $location,
                'type' => 'location' // Lowercased to match the enum value
            ]);
        }
    }
}
