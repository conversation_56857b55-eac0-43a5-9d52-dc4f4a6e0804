<?php

namespace Database\Seeders;

use App\Models\AsssetInventory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AssetInventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $assets = [
            [
                'description' => 'SRC 30',
                'barcode' => 'BAR0001435',
                'asset_type' => 'SRC 30',
                'model_name' => 'SRC 30',
                'vendor' => 'Western Refrigeration Pvt. Limited',
                'quantity' => 100,
                'warehouse_code' => 'IN57',
                'manufactured_year' => 2023,
                'serial_number' => 'SR00000053',
            ],
            [
                'description' => 'SRC 60',
                'barcode' => 'BAR0001436',
                'asset_type' => 'SRC 60',
                'model_name' => 'SRC 60',
                'vendor' => 'LG Refrigeration Pvt. Limited',
                'quantity' => 100,
                'warehouse_code' => 'IN58',
                'manufactured_year' => 2023,
                'serial_number' => 'SR00000054',
            ],
            [
                'description' => 'New 30L - CT 30',
                'barcode' => 'BAR0001437',
                'asset_type' => 'New 30L - CT 30',
                'model_name' => 'New 30L - CT 30',
                'vendor' => 'SAMSUNG Refrigeration Pvt. Limited',
                'quantity' => 100,
                'warehouse_code' => 'IN59',
                'manufactured_year' => 2023,
                'serial_number' => 'SR00000055',
            ],
            [
                'description' => 'New 60L - VT30',
                'barcode' => 'BAR0001430',
                'asset_type' => 'New 60L - VT30',
                'model_name' => 'New 60L - VT30',
                'vendor' => 'SAMSUNG Refrigeration Pvt. Limited',
                'quantity' => 100,
                'warehouse_code' => 'IN60',
                'manufactured_year' => 2023,
                'serial_number' => 'SR00000056',
            ],
            [
                'description' => 'SRC280',
                'barcode' => 'BAR0001438',
                'asset_type' => 'SRC280',
                'model_name' => 'SRC280',
                'vendor' => 'SAMSUNG Refrigeration Pvt. Limited',
                'quantity' => 100,
                'warehouse_code' => 'IN61',
                'manufactured_year' => 2023,
                'serial_number' => 'SR00000057',
            ],
            [
                'description' => 'SRC380',
                'barcode' => 'BAR0001380',
                'asset_type' => 'SRC380',
                'model_name' => 'SRC380',
                'vendor' => 'SAMSUNG Refrigeration Pvt. Limited',
                'quantity' => 100,
                'warehouse_code' => 'IN62',
                'manufactured_year' => 2023,
                'serial_number' => 'SR00000058',
            ],
            // Add more records here if needed
        ];
        AsssetInventory::insert($assets);
    }
}
