<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cfa_businesses', function (Blueprint $table) {
            $table->id();
            $table->string('cfa_plant_code')->unique();
            $table->string('user_type');
            $table->string('business_name');
            $table->string('registered_name');
            $table->string('register_number')->nullable();;
            $table->string('address_1');
            $table->string('address_2')->nullable();
            $table->string('city');
            $table->string('state');
            $table->string('country');
            $table->string('pin_code');
            $table->date('effective_from');
            $table->date('effective_to')->nullable();
            $table->string('gst_no')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cfa_businesses');
    }
};
