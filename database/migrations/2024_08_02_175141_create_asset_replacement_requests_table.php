<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_replacement_requests', function (Blueprint $table) {
            $table->id();
            $table->string('user_id');
            $table->string('request_number')->unique()->index();
            $table->string('type');
            $table->integer('outlet_id');
            $table->string('asset_barcode');
            $table->string('asset_number');
            $table->string('chiller_type');
            $table->string('current_vpo')->nullable();
            $table->string('expected_vpo');
            $table->string('request_chiller_type');
            $table->enum('request_type', ['Normal', 'Exceptional'])->default('Normal');
            $table->json('additional_equipment')->nullable();
            $table->string('chiller_location')->nullable();
            $table->text('chiller_location_photo')->nullable();
            $table->enum('consent_status', ['Confirmed'])->nullable();
            $table->text('retailer_photo')->nullable();
            $table->string('signature')->nullable();
            $table->text('address_proof')->nullable();
            $table->string('mobile_number')->nullable();
            $table->text('customer_address')->nullable();
            $table->string('pincode')->nullable();
            $table->string('replacement_reason');
            $table->string('customer_location')->nullable();
            $table->string('current_location')->nullable();
            $table->float('distance')->nullable();
            $table->text('remarks')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->enum('correct_location', ['Yes', 'No'])->nullable();

            #approval
            $table->enum('task_status', ['Completed', 'Pending'])->default('Pending');
            $table->enum('chiller_placed', ['Yes', 'No'])->nullable();
            $table->enum('is_quantity_allocated', ['Yes', 'No'])->nullable();
            $table->enum('asset_assigned_status', ['Approved', 'Rejected', 'Pending'])->default('Pending');
            $table->date('replacement_date')->nullable();
            $table->string('approved_by_user_role')->nullable();
            $table->string('approved_by_user_id')->nullable();
            $table->string('pending_from')->nullable();
            $table->string('rejected_by_user_id')->nullable();
            $table->string('rejected_by_user_role')->nullable();
            $table->string('replacement_approved_time')->nullable();
            $table->string('replacement_rejected_time')->nullable();
            $table->date('deployment_date')->nullable();
            $table->date('expected_deployment_date')->nullable();
            $table->string('assigned_organization')->nullable();

            #completion
            $table->dateTime('replacement_complete_date')->nullable();
            $table->string('request_by')->nullable();
            $table->string('request_by_name')->nullable();
            $table->string('approved_time')->nullable();
            $table->string('rejection_reason')->nullable();
            $table->boolean('is_deploy')->default(false);

            #heirarchy
            $table->string('outlet_code')->index()->nullable();
            $table->string('ae_code')->index()->nullable();
            $table->string('rsm_code')->index()->nullable();
            $table->string('rttm_code')->index()->nullable();
            $table->string('asm_code')->index()->nullable();
            $table->string('so_code')->index()->nullable();
            $table->string('db_code')->index()->nullable();
            $table->string('dsr_code')->index()->nullable();
            $table->string('cfa_code')->index()->nullable();

            #handle upload history
            $table->string('batchID')->index()->nullable();
            $table->string('uploadBy')->index()->nullable();
            $table->string('uploadByName')->nullable();
            $table->string('uploadTime')->nullable();
            $table->enum('upload_by_org', ['cb', 'ivy'])->default('cb');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_replacement_requests');
    }
};
