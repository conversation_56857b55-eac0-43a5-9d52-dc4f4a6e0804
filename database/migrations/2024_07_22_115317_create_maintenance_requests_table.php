<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('asset_maintenance_requests', function (Blueprint $table) {
            $table->id();
            $table->string('user_id');
            $table->string('maintenance_request_number')->unique();

            $table->string('type');
            $table->integer('outlet_id');
            $table->string('asset_number');
            $table->string('asset_barcode');
            $table->string('current_vpo');
            $table->string('chiller_type');
            $table->string('customer_code');
            $table->string('customer_name')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('contact_number')->nullable();
            $table->enum('consent_status', ['Confirmed']);
            $table->text('signature');
            $table->string('email')->nullable();
            $table->string('mobile_number')->nullable();
            $table->text('customer_address')->nullable();
            $table->string('pincode')->nullable();
            $table->string('maintenance_reason');
            $table->string('customer_location')->nullable();
            $table->string('current_location')->nullable();
            $table->string('correct_location')->nullable();
            $table->decimal('distance')->nullable();
            $table->decimal('latitude')->nullable();
            $table->decimal('longitude')->nullable();
            $table->text('remarks')->nullable();
            $table->string('status')->nullable();
            $table->text('status_remarks')->nullable();

            #approval
            $table->enum('task_status', ['Completed', 'Pending'])->default('Pending');
            $table->enum('repair_status', ['YES', 'NO'])->default('NO');
            $table->enum('asset_assigned_status', ['Approved', 'Rejected', 'Pending'])->default('Pending');
            $table->date('maintenance_date')->nullable();
            $table->string('approved_by_user_role')->nullable();
            $table->string('approved_by_user_id')->nullable();
            $table->string('pending_from')->nullable();
            $table->string('rejected_by_user_id')->nullable();
            $table->string('rejected_by_user_role')->nullable();
            $table->string('maintenance_approved_time')->nullable();
            $table->string('maintenance_rejected_time')->nullable();
            $table->date('expected_maintenance_date')->nullable();
            $table->string('assigned_organization')->nullable();

            #completion
            $table->dateTime('maintenance_complete_date')->nullable();
            $table->string('request_by')->nullable();
            $table->string('request_by_name')->nullable();
            $table->string('approved_time')->nullable();
            $table->string('rejection_reason')->nullable();

            #heirarchy
            $table->string('outlet_code')->nullable()->index();
            $table->string('ae_code')->nullable()->index();
            $table->string('rsm_code')->nullable()->index();
            $table->string('asm_code')->nullable()->index();
            $table->string('so_code')->nullable()->index();
            $table->string('db_code')->nullable()->index();
            $table->string('dsr_code')->nullable()->index();
            $table->string('cfa_code')->nullable()->index();
            $table->string('batchID')->nullable()->index();
            $table->string('uploadBy')->nullable();
            $table->string('uploadByName')->nullable();
            $table->timestamp('uploadTime')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_maintenance_requests');
    }
};
