<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assets_audit', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('type');
            $table->string('request_number')->unique();
            $table->unsignedBigInteger('outlet_id');
            $table->unsignedBigInteger('asset_id');
            $table->string('asset_number');
            $table->boolean('is_chiller_available');
            $table->boolean('is_barcode_available');
            $table->string('barcode_no_scan_reason')->nullable();
            $table->string('scanned_barcode')->nullable();
            $table->string('barcode_match_status')->nullable();
            $table->string('customer_address');
            $table->string('pincode');
            $table->string('current_location');
            $table->decimal('latitude', 10, 6);
            $table->decimal('longitude', 10, 6);
            $table->decimal('distance', 10, 2);
            $table->string('audit_status');
            $table->text('remarks')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_id', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('outlet_id');
            $table->dropColumn('asset_id');
            $table->dropColumn('asset_number');
            $table->dropColumn('is_chiller_available');
            $table->dropColumn('is_barcode_available');
            $table->dropColumn('barcode_no_scan_reason');
            $table->dropColumn('scanned_barcode');
            $table->dropColumn('barcode_match_status');
            $table->dropColumn('customer_address');
            $table->dropColumn('pincode');
            $table->dropColumn('current_location');
            $table->dropColumn('latitude');
            $table->dropColumn('longitude');
            $table->dropColumn('distance');
            $table->dropColumn('audit_status');
            $table->dropColumn('audit_status');
            $table->dropColumn('remarks');
        });
    }
};
