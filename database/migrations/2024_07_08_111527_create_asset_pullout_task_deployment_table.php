<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_pullout_task_deployment', function (Blueprint $table) {
            $table->id();
            $table->string('user_id')->index();
            $table->string('pullout_request_number')->index();
            $table->enum('pullout_asset', ['Yes', 'No']);
            $table->string('reason_no_pullout')->nullable();
            $table->string('remarks')->nullable();
            $table->string('outlet_photo')->nullable();
            $table->string('retailer_photo')->nullable();
            $table->string('asset_number')->index()->nullable();
            $table->string('scanned_bar_code')->index()->nullable();
            $table->string('chiller_image')->nullable();
            $table->string('correct_location')->nullable();
            $table->float('distance')->nullable();
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            $table->string('current_location')->nullable();
            $table->string('ae_code')->nullable()->index();
            $table->string('rsm_code')->nullable()->index();
            $table->string('asm_code')->nullable()->index();
            $table->string('so_code')->nullable()->index();
            $table->string('db_code')->nullable()->index();
            $table->string('dsr_code')->nullable()->index();
            $table->string('cfa_code')->nullable()->index();
            $table->string('batchID')->nullable()->index();
            $table->text('uploadBy')->nullable();
            $table->string('uploadByName')->nullable();
            $table->timestamp('uploadTime')->nullable();
            $table->string('completion_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_pullout_task_deployment');
    }
};
