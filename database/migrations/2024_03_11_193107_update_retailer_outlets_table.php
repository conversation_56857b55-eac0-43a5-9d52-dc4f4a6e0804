<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            $table->text('outlet_image')->after('retailer_reference_code')->nullable();
            $table->text('db_name')->after('outlet_image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            //
        });
    }
};
