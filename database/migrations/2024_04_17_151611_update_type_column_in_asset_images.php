<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_images', function (Blueprint $table) {
            $table->unsignedInteger('type')
                ->comment('1=competitor_chiller_photo, 2=chiller_location_photo, 3=address_proof, 4=retailer_photo, 5=digital_sign,6=outlet_photo,7=chiller_image,8=installation_receipt')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_images', function (Blueprint $table) {
            //
        });
    }
};
