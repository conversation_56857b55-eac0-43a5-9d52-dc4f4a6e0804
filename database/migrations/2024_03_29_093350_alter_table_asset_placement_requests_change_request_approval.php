<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            // $table->string('approved_by_user_role')->nullable();
            // $table->unsignedBigInteger('approved_by_user_id')->nullable();
            // $table->string('pending_from')->nullable();
            // $table->unsignedBigInteger('rejected_by_user_id')->nullable();
            // $table->string('rejected_by_user_role')->nullable();
            // $table->dateTime('placement_approved_time')->nullable();
            // $table->dateTime('placement_rejected_time')->nullable();
            // $table->enum('is_quantity_allocated', ['YES', 'NO'])->nullable()->default('NO');
            if (!Schema::hasColumn('asset_placement_requests', 'approved_by_user_role')) {
                $table->string('approved_by_user_role')->nullable();
            }
            if (!Schema::hasColumn('asset_placement_requests', 'approved_by_user_id')) {
                $table->unsignedBigInteger('approved_by_user_id')->nullable();
            }
            if (!Schema::hasColumn('asset_placement_requests', 'pending_from')) {
                $table->string('pending_from')->nullable();
            }
            if (!Schema::hasColumn('asset_placement_requests', 'rejected_by_user_id')) {
                $table->unsignedBigInteger('rejected_by_user_id')->nullable();
            }
            if (!Schema::hasColumn('asset_placement_requests', 'rejected_by_user_role')) {
                $table->string('rejected_by_user_role')->nullable();
            }
            if (!Schema::hasColumn('asset_placement_requests', 'placement_approved_time')) {
                $table->dateTime('placement_approved_time')->nullable();
            }
            if (!Schema::hasColumn('asset_placement_requests', 'placement_rejected_time')) {
                $table->dateTime('placement_rejected_time')->nullable();
            }
            if (!Schema::hasColumn('asset_placement_requests', 'is_quantity_allocated')) {
                $table->enum('is_quantity_allocated', ['YES', 'NO'])->nullable()->default('NO');
            }
            // Define foreign key constraint for rejected_by_user_id

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {


            $table->dropColumn('approved_by_user_role');
            $table->dropColumn('approved_by_user_id');
            $table->dropColumn('pending_from');
            $table->dropColumn('rejected_by_user_id');
            $table->dropColumn('placement_approved_time');
            $table->dropColumn('placement_rejected_time');
            $table->dropColumn('is_quantity_allocated');
            // Remove foreign key constraints

        });
    }
};
