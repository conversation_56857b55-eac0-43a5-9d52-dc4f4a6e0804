<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('user_id')->default('');
            $table->string('name')->default('');
            $table->string('user_type')->default('');
            $table->string('first_name')->default('');
            $table->string('last_name')->default('');
            $table->string('email')->nullable();
            // $table->string('email')->unique()->default('');
            $table->string('personal_email')->nullable();
            // $table->string('personal_email')->unique()->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->text('password')->nullable();
            $table->string('mobile_number')->nullable()->default('');
            $table->string('phone')->nullable()->default('');
            $table->string('address_1')->default('');
            $table->string('address_2')->default('');
            $table->string('address_3')->default('');
            $table->string('city')->default('');
            $table->string('state')->default('');
            $table->string('pin_code')->default('');
            $table->string('region')->default('');
            $table->string('region_code')->default('');
            $table->string('status')->default(1);
            $table->string('retailer_id')->default('');
            $table->string('merchandiser_id')->default('');
            $table->string('dsr_id')->default('');
            $table->string('isr_id')->default('');
            $table->string('distributor_id')->default('');
            $table->string('so_id')->default('');
            $table->string('asm_id')->default('');
            $table->string('rsm_id')->default('');
            $table->string('rbdm_id')->default('');
            $table->string('ae_id')->default('');
            $table->string('vendor_id')->default('');
            $table->string('cfa_code')->default('');
            $table->string('teritory_code')->default('');
            $table->string('area_code')->default('');
            $table->string('vendor_executive_id')->default('');
            $table->string('region_manager_id')->default('');
            $table->string('channel_type')->default('');
            $table->string('tax_location')->default('');
            $table->string('registered_name')->default('');
            $table->date('effective_from')->default(now()->toDateString()); // Set default value to current date
            $table->date('effective_to')->nullable(); // Assuming effective_to can be null
            $table->string('gst_no')->nullable();
            $table->string('IsSubD')->default('');
            $table->string('Otp')->nullable()->default('');

            $table->dateTime('reset_email_sent_on')->nullable();
            $table->string('reset_token')->nullable()->default('');
            $table->rememberToken();
            $table->timestamps();



            $table->index(['retailer_id', 'merchandiser_id', 'dsr_id'], 'index_rmd');
            $table->index(['isr_id', 'distributor_id', 'so_id'], 'index_ids');
            $table->index(['asm_id', 'rsm_id', 'rbdm_id'], 'index_arr');
            $table->index(['ae_id', 'vendor_id', 'cfa_code'], 'index_avc');
            $table->index(['teritory_code', 'area_code', 'vendor_executive_id'], 'index_tav');
            $table->index(['region_manager_id'], 'index_r');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
