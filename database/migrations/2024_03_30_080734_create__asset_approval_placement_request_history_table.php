<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_approval_placement_request_history', function (Blueprint $table) {
            $table->id();
            $table->string('asset_placement_request_id');
            $table->enum('action', ['Approved', 'Rejected']);
            $table->dateTime('action_updated_time');
            $table->string('user_id');
            $table->string('user_role');
            $table->text('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('_asset_approval_placement_request_history', function (Blueprint $table) {
            $table->dropColumn('asset_placement_request_id');
            $table->dropColumn('action');
            $table->dropColumn('action_updated_time');
            $table->dropColumn('user_id');
            $table->dropColumn('user_role');
            $table->dropColumn('comment');
        });
    }
};
