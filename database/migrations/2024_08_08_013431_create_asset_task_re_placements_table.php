<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_replacement_task_deployment', function (Blueprint $table) {
            $table->id();
            $table->string('user_id')->index();
            $table->string('request_number')->index();
            $table->string('placement_request_number')->index()->nullable();
            $table->string('prev_assigned_asset_number')->index('prev_asset_nuber')->nullable();
            $table->string('outlet_id')->index();
            $table->enum('chiller_placed', ['Yes', 'No']);
            $table->text('reason_no_deployment')->nullable();
            $table->text('remarks')->nullable();
            $table->string('outlet_photo')->nullable();
            $table->string('retailer_photo')->nullable();
            $table->string('replaced_asset_number')->nullable();
            $table->string('replaced_asset_barcode')->nullable();
            $table->string('chiller_image')->nullable();
            $table->string('installation_receipt')->nullable();
            $table->text('correct_location')->nullable();
            $table->string('distance')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->string('current_location')->nullable();
            $table->date('completion_date')->nullable();
            $table->string('outlet_code')->index();
            $table->string('ae_code')->nullable()->index();
            $table->string('rsm_code')->nullable()->index();
            $table->string('rttm_code')->nullable()->index();
            $table->string('asm_code')->nullable()->index();
            $table->string('so_code')->nullable()->index();
            $table->string('db_code')->nullable()->index();
            $table->string('dsr_code')->nullable()->index();
            $table->string('cfa_code')->nullable()->index();
            $table->string('batchID')->nullable();
            $table->text('uploadBy')->nullable();
            $table->string('uploadByName')->nullable();
            $table->timestamp('uploadTime')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_replacement_task_deployment');
    }
};
