<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice_challans', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_challan_number')->index();
            $table->string('request_number')->index();
            $table->enum('invoice_prefix', ['ARR', 'AMR', 'ATR', 'APR', 'PR']);
            $table->timestamp('challan_created_datetime');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice_challans');
    }
};
