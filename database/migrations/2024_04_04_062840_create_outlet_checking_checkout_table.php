<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_outlet_audit_visit_attandance', function (Blueprint $table) {
            $table->id();
            $table->text('user_id');
            $table->text('user_type');
            $table->enum('visit_type', ['checkin', 'checkout']); // 'Checkin' or 'Checkout'
            $table->dateTime('checkin_time');
            $table->dateTime('checkout_time')->nullable();
            $table->integer('outlet_id');
            $table->double('checkin_latitude');
            $table->double('checkin_longitude');
            $table->double('checkin_distance')->nullable();
            $table->double('checkout_latitude')->nullable();
            $table->double('checkout_longitude')->nullable();
            $table->double('checkout_distance')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_outlet_audit_visit_attandance', function (Blueprint $table) {
            $table->dropColumn('user_id');
            $table->dropColumn('user_type');
            $table->dropColumn('visit_type');
            $table->dropColumn('checkin_time');
            $table->dropColumn('checkout_time');
            $table->dropColumn('outlet_id');
            $table->dropColumn('checkin_latitude');
            $table->dropColumn('checkin_longitude');
            $table->dropColumn('checkin_distance');
            $table->dropColumn('checkout_latitude');
            $table->dropColumn('checkout_longitude');
            $table->dropColumn('checkout_distance');
        });
    }
};
