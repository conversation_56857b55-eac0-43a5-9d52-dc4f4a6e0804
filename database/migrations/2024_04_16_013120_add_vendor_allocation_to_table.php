<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->date('expected_deployment_date')->nullable();
            $table->string('assigned_organization')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->dropColumn('expected_deployment_date');
            $table->dropColumn('assigned_organization');
        });
    }
};
