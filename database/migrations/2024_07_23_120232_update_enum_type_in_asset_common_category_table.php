<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE `asset_common_category` MODIFY `type` ENUM(
            'equipment',
            'location',
            'company',
            'chiller_size',
            'competitor_sizes',
            'asset_type',
            'pullout_request',
            'maintenance',
            'replacement',
            'asset_transfer',
            'a_to_b_transfer'
        )");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("ALTER TABLE `asset_common_category` MODIFY `type` ENUM(
            'equipment',
            'location',
            'company',
            'chiller_size',
            'competitor_sizes',
            'asset_type',
            'pullout_request'
        )");
    }
};
