<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->timestamp('approved_time')->nullable();
            $table->timestamp('deployment_date')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->boolean('is_deploy')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->dropColumn('approved_time');
            $table->dropColumn('deployment_date');
            $table->dropColumn('rejection_reason');
            $table->dropColumn('is_deploy');
        });
    }
};
