<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets_inventory', function (Blueprint $table) {
            $table->enum('asset_approval_status', ['pending', 'approved','rejected'])->default('pending')->after('asset_price');
            $table->string('approved_by_user')->default('')->after('asset_approval_status');
            $table->string('approved_by_user_type')->default('')->after('approved_by_user');

            $table->index('asset_approval_status', 'inv_asset_approval_status');
            $table->index('approved_by_user', 'inv_approved_by_user');
            $table->index('approved_by_user_type', 'inv_approved_by_user_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets_inventory', function (Blueprint $table) {
            $table->dropIndex('inv_asset_approval_status');
            $table->dropIndex('inv_approved_by_user');
            $table->dropIndex('inv_approved_by_user_type');

            // Remove columns
            $table->dropColumn('asset_approval_status');
            $table->dropColumn('approved_by_user');
            $table->dropColumn('approved_by_user_type');
        });
    }
};
