<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets_audit', function (Blueprint $table) {
            $table->string('outlet_code')->nullable();
            $table->string('ae_code')->nullable();
            $table->string('rsm_code')->nullable();
            $table->string('asm_code')->nullable();
            $table->string('so_code')->nullable();
            $table->string('db_code')->nullable();
            $table->string('dsr_code')->nullable();
            $table->string('cfa_code')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets_audit', function (Blueprint $table) {
            $table->dropColumn('outlet_code');
            $table->dropColumn('ae_code');
            $table->dropColumn('rsm_code');
            $table->dropColumn('asm_code');
            $table->dropColumn('so_code');
            $table->dropColumn('db_code');
            $table->dropColumn('dsr_code');
            $table->dropColumn('cfa_code');
        });
    }
};
