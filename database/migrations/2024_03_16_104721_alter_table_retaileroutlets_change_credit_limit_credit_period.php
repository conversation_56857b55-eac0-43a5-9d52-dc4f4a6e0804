<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            $table->decimal('credit_limit', 10, 2)->default(0)->change();
            $table->decimal('credit_period', 10, 2)->default(0)->change();
            $table->decimal('return_qty', 10, 2)->default(0)->change(); // Add this line
            $table->decimal('return_value', 10, 2)->default(0)->change(); // Add this line
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            //
        });
    }
};
