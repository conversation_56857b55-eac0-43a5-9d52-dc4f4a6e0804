<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('upload_histories', function (Blueprint $table) {
            $table->id();
            $table->text('batchID');
            $table->string('uploadBy')->index('uploadBy');
            $table->string('uploadByName');
            $table->string('requestIP')->index('requestIP');
            $table->string('userType');
            $table->timestamp('uploadDateTime');
            $table->string('uploadType');
            $table->integer('totalRecordscount');
            $table->integer('successCount');
            $table->integer('failedCount');
            $table->text('failedMessage')->nullable();
            $table->json('failedData')->nullable();
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('upload_histories');
    }
};
