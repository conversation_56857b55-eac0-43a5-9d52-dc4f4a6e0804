<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_placement_task_deployment', function (Blueprint $table) {
            $table->id();
            $table->string('user_id');
            $table->string('request_number');
            $table->string('outlet_id');
            $table->string('outlet_code');
            $table->enum('chiller_placed', ['Yes', 'No']);
            $table->text('reason_no_deployment')->nullable();
            $table->text('remarks')->nullable();
            $table->string('outlet_photo')->nullable();
            $table->string('retailer_photo')->nullable();
            $table->string('asset_number')->nullable();
            $table->string('asset_barcode')->nullable();
            $table->string('chiller_image')->nullable();
            $table->string('installation_receipt')->nullable();
            $table->text('correct_location')->nullable();
            $table->double('distance')->nullable();
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            $table->string('current_location')->nullable();
            $table->date('completion_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_placement_task_deployment');
    }
};
