<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            $table->text('return_qty')->nullable()->default(null)->change();
            $table->text('return_value')->nullable()->default(null)->change();
            $table->text('invoiceaddress')->nullable()->default(null)->change();
            $table->text('lat')->nullable()->default(null)->change();
            $table->text('long')->nullable()->default(null)->change();
            $table->string('is_tcs')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            //
        });
    }
};
