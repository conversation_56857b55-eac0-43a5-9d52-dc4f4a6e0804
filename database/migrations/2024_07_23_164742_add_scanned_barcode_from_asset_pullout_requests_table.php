<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_pullout_requets', function (Blueprint $table) {
            $table->string('ScannedBarcode')->nullable()->after('asset_barcode');
            $table->string('current_location')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_pullout_requets', function (Blueprint $table) {
            $table->dropColumn('ScannedBarcode');
        });
    }
};
