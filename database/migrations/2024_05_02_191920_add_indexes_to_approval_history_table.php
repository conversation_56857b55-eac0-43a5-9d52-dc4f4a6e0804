<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_approval_placement_request_history', function (Blueprint $table) {
            $table->index('asset_placement_request_id', 'idx_asset_approval_placement_request_history_index');
        });
        Schema::table('assets_outlet_details', function (Blueprint $table) {

            $table->index('asset_barcode', 'idx_asset_barcode');
            $table->index('asset_description', 'idx_asset_description');
            $table->index('placement_request_number', 'idx_placement_request_number');
            $table->index('asset_number', 'idx_asset_number');
        });
        Schema::table('asset_placement_task_deployment', function (Blueprint $table) {
            $table->index('user_id', 'asset_placement_task_deployment_user_id_index');
            $table->index('outlet_id', 'asset_placement_task_deployment_outlet_id_index');
            $table->index('outlet_code', 'asset_placement_task_deployment_outlet_code_index');
            $table->index('request_number', 'asset_placement_task_deployment_request_number_index');
            $table->index('asset_number', 'asset_placement_task_deployment_asset_number_index');
            $table->index('asset_barcode', 'asset_placement_task_deployment_asset_barcode_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_approval_placement_request_history', function (Blueprint $table) {
            $table->dropIndex('idx_asset_approval_placement_request_history');
        });
        Schema::table('assets_outlet_details', function (Blueprint $table) {
            $table->dropIndex('idx_asset_barcode');
            $table->dropIndex('idx_asset_description');
            $table->dropIndex('idx_placement_request_number');
            $table->dropIndex('idx_asset_number');
        });
        Schema::table('asset_placement_task_deployment', function (Blueprint $table) {
            $table->dropIndex('asset_placement_task_deployment_user_id_index');
            $table->dropIndex('asset_placement_task_deployment_outlet_id_index');
            $table->dropIndex('asset_placement_task_deployment_outlet_code_index');
            $table->dropIndex('asset_placement_task_deployment_request_number_index');
            $table->dropIndex('asset_placement_task_deployment_asset_number_index');
            $table->dropIndex('asset_placement_task_deployment_asset_barcode_index');
        });

    }
};
