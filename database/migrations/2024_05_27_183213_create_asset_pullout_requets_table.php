<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_pullout_requets', function (Blueprint $table) {
            $table->id();
            $table->string('user_id');
            $table->unsignedBigInteger('outlet_id')->nullable();
            $table->string('type')->nullable();
            $table->string('expected_vpo')->nullable();
            $table->string('chiller_type')->nullable();
            $table->string('request_type')->nullable();
            $table->longText('additional_equipment')->nullable();
            $table->longText('competitor_chiller_size')->nullable();
            $table->longText('competitor_company')->nullable();
            $table->string('competitor_chiller_photo')->nullable();
            $table->string('chiller_location')->nullable();
            $table->string('chiller_location_photo')->nullable();
            $table->string('address_proof')->nullable();
            $table->string('retailer_photo')->nullable();
            $table->string('signature')->nullable();
            $table->string('mobile_number')->nullable();
            $table->string('customer_address')->nullable();
            $table->string('pincode', 6)->nullable();
            $table->string('customer_location')->nullable();
            $table->string('current_location')->nullable();
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            $table->enum('correct_location', ['Yes', 'No'])->nullable();
            $table->double('distance')->nullable();
            $table->string('remarks')->nullable();
            $table->enum('consent_status', ['Confirmed'])->nullable();
            $table->string('request_number')->nullable();
            $table->enum('asset_assigned_status', ['Approved', 'Rejected', 'Pending'])->nullable();

            $table->date('approved_time')->nullable();
            $table->date('deployment_date')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->tinyInteger('is_deploy')->nullable();
            $table->integer('vpo_target')->nullable();
            $table->string('approved_by_user_role')->nullable();
            $table->string('approved_by_user_id')->nullable();
            $table->string('pending_from')->nullable();
            $table->string('rejected_by_user_id')->nullable();
            $table->string('rejected_by_user_role')->nullable();
            $table->dateTime('placement_approved_time')->nullable();
            $table->dateTime('placement_rejected_time')->nullable();
            $table->enum('is_quantity_allocated', ['YES', 'NO'])->nullable();
            $table->date('expected_deployment_date')->nullable();
            $table->string('assigned_organization')->nullable();
            $table->enum('chiller_placed', ['YES', 'NO'])->nullable();
            $table->enum('task_status', ['Completed', 'Pending'])->nullable();
            $table->string('outlet_code')->index()->nullable();
            $table->string('ae_code')->index()->nullable();
            $table->string('rsm_code')->index()->nullable();
            $table->string('asm_code')->index()->nullable();
            $table->string('so_code')->index()->nullable();
            $table->string('db_code')->index()->nullable();
            $table->string('dsr_code')->index()->nullable();
            $table->string('cfa_code')->index()->nullable();
            $table->string('challan_number')->nullable();
            $table->string('asset_barcode')->nullable();
            $table->string('asset_serial_number')->nullable();

            $table->index('user_id');
            $table->index('outlet_id');

            $table->string('asset_id')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('customer_code')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('contact_number')->nullable();
            $table->string('pullout_asset_type')->nullable();
            $table->string('pullout_reason')->nullable();
            $table->string('email')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_pullout_requets');
    }
};
