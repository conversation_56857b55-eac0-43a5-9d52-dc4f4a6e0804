<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->string('pullout_request_number')->unique()->nullable()->after('batchID'); // Replace 'existing_column' with the name of the column after which you want to add this column
            $table->dateTime('pullout_complete_date_time')->nullable()->after('pullout_request_number');
            $table->string('pullout_request_by')->nullable()->after('pullout_complete_date_time');
            $table->string('pullout_request_by_name')->nullable()->after('pullout_request_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->dropColumn('pullout_request_number');
            $table->dropColumn('pullout_complete_date_time');
            $table->dropColumn('pullout_request_by');
            $table->dropColumn('pullout_request_by_name');
        });
    }
};
