<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_maintenance_task_repair', function (Blueprint $table) {
            $table->string('asset_number')->nullable()->change();
            $table->string('scanned_barcode')->index()->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_maintenance_task_repair', function (Blueprint $table) {
           $table->string('scanned_barcode')->nullable(false)->change();
        });
    }
};
