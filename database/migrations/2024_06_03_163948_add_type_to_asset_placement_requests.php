<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->enum('asset_mapping_type', ['cb', 'ivy'])->default('cb');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_mapping_type', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
