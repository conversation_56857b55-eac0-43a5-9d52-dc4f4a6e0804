<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_pullout_requets', function (Blueprint $table) {
            $table->date('pullout_date')->nullable()->after('asset_assigned_status');
            $table->integer('is_pullout')->nullable()->default(false)->after('pullout_date');
            $table->string('pullout_request_number')->unique()->nullable()->after('is_pullout');
            $table->dateTime('pullout_complete_date')->nullable()->after('pullout_request_number');
            $table->enum('pullout_request_complete',['yes','no'])->default('no')->after('pullout_complete_date');
            $table->string('pullout_request_by')->nullable()->after('pullout_request_complete');
            $table->string('pullout_request_by_name')->nullable()->after('pullout_request_by');

            // Reverting column names
            $table->renameColumn('request_number', 'placement_request_number');
            $table->renameColumn('placement_approved_time', 'pullout_approved_time');
            $table->renameColumn('placement_rejected_time','pullout_rejected_time');
            $table->renameColumn('expected_deployment_date','expected_pullout_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_pullout_requets', function (Blueprint $table) {
            $table->dropColumn('pullout_date');
            $table->dropColumn('is_pullout');
            $table->dropColumn('pullout_request_number');
            $table->dropColumn('pullout_complete_date');
            $table->dropColumn('pullout_request_complete');
            $table->dropColumn('pullout_request_by');
            $table->dropColumn('pullout_request_by_name');

            // Reverting column names
            $table->renameColumn('placement_request_number', 'request_number');
            $table->renameColumn('pullout_approved_time', 'placement_approved_time');
            $table->renameColumn('pullout_rejected_time', 'placement_rejected_time');
            $table->renameColumn('expected_pullout_date', 'expected_deployment_date');

        });
    }
};
