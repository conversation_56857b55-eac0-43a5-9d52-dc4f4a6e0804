<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('device_details', function (Blueprint $table) {
            if (Schema::hasColumn('device_details', 'user_id')) {
                $table->dropForeign('device_details_user_id_foreign');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('device_details', function (Blueprint $table) {
            //
        });
    }
};
