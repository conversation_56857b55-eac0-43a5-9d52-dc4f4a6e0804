<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets_outlet_details', function (Blueprint $table) {
            $table->string('batchID')->nullable()->after('asset_number');
            $table->text('uploadBy')->nullable()->after('batchID');
            $table->string('uploadByName')->nullable()->after('uploadBy');
            $table->timestamp('uploadTime')->nullable()->after('uploadByName');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets_outlet_details', function (Blueprint $table) {
            $table->dropColumn('batchID');
            $table->dropColumn('uploadBy');
            $table->dropColumn('uploadByName');
            $table->dropColumn('uploadTime');
        });
    }
};
