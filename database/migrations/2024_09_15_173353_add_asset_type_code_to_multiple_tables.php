<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
       // Add asset_type_code to asset_pullout_requests
       Schema::table('asset_pullout_requets', function (Blueprint $table) {
        $table->string('asset_type_code')->nullable()->after('chiller_type'); // Adjust data type if needed
    });

    // Add asset_type_code to asset_replacement_requests
    Schema::table('asset_replacement_requests', function (Blueprint $table) {
        $table->string('asset_type_code')->nullable()->after('chiller_type');
        $table->string('request_chiller_code')->nullable()->after('request_chiller_type');
    });

    // Add asset_type_code to asset_maintenance_requests
    Schema::table('asset_maintenance_requests', function (Blueprint $table) {
        $table->string('asset_type_code')->nullable()->after('chiller_type');
    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop asset_type_code from asset_pullout_requests
        Schema::table('asset_pullout_requets', function (Blueprint $table) {
            $table->dropColumn('asset_type_code');
        });

        // Drop asset_type_code from asset_replacement_requests
        Schema::table('asset_replacement_requests', function (Blueprint $table) {
            $table->dropColumn('asset_type_code');
            $table->dropColumn('request_chiller_code');
        });

        // Drop asset_type_code from asset_maintenance_requests
        Schema::table('asset_maintenance_requests', function (Blueprint $table) {
            $table->dropColumn('asset_type_code');
        });
    }
};
