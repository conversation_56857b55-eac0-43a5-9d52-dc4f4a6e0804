<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets_inventory', function (Blueprint $table) {
            $table->enum('assigned_status', ['yes', 'no'])->default('no')->after('serial_number');
            $table->decimal('asset_price')->default(0)->after('assigned_status');
            $table->string('barcode')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets_inventory', function (Blueprint $table) {
            $table->dropColumn('assigned_status');
            $table->dropColumn('asset_price');
        });
    }
};
