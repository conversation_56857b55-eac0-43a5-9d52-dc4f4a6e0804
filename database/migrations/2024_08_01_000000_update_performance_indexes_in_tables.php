<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations to optimize database performance.
     *
     * @return void
     */
    public function up()
    {
        // 1. Optimize User-related tables
        $this->optimizeUserTables();
        
        // 2. Optimize Asset-related tables
        $this->optimizeAssetTables();
        
        // 3. Optimize Request-related tables
        $this->optimizeRequestTables();
        
        // 4. Add database configuration optimizations
        $this->optimizeDatabaseConfig();
    }

    /**
     * Optimize user-related tables
     */
    private function optimizeUserTables()
    {
        // Users table optimizations
        Schema::table('users', function (Blueprint $table) {
            // Add indexes for frequently filtered columns
            $table->index('user_type');
            $table->index('user_id');
            
            // Add indexes for hierarchical lookups
            $table->index('so_id');
            $table->index('asm_id');
            $table->index('rsm_id');
            $table->index('distributor_id');
            
            // Optimize string columns
            DB::statement('ALTER TABLE users MODIFY name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
            DB::statement('ALTER TABLE users MODIFY email VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
        });
        
        // Retailer outlets table optimizations
        if (Schema::hasTable('retailer_outlets')) {
            Schema::table('retailer_outlets', function (Blueprint $table) {
                $table->index('route_name');
                $table->index('salesman_name');
                $table->index('contact_name');
            });
        }
    }

    /**
     * Optimize asset-related tables
     */
    private function optimizeAssetTables()
    {
        // Asset inventory optimizations
        if (Schema::hasTable('asset_inventory')) {
            Schema::table('asset_inventory', function (Blueprint $table) {
                $table->index('serial_number');
                $table->index('asset_type');
                $table->index('approval_status');
                
                // Optimize decimal columns for coordinates
                DB::statement('ALTER TABLE asset_inventory MODIFY latitude DECIMAL(10,7)');
                DB::statement('ALTER TABLE asset_inventory MODIFY longitude DECIMAL(10,7)');
            });
        }
        
        // Asset placement requests optimizations
        if (Schema::hasTable('asset_placement_requests')) {
            Schema::table('asset_placement_requests', function (Blueprint $table) {
                $table->index('request_number');
                $table->index('outlet_id');
                $table->index('asset_assigned_status');
                $table->index('task_status');
                $table->index(['created_at']);
                
                // Create composite index for common filters
                $table->index(['assigned_organization', 'asset_assigned_status']);
            });
        }
    }

    /**
     * Optimize request-related tables
     */
    private function optimizeRequestTables()
    {
        // Asset replacement requests optimizations
        if (Schema::hasTable('asset_replacement_requests')) {
            Schema::table('asset_replacement_requests', function (Blueprint $table) {
                $table->index('request_number');
                $table->index(['user_id', 'type']);
                $table->index('asset_assigned_status');
                $table->index('task_status');
                $table->index('created_at');
                
                // Create composite index for common API queries
                $table->index(['assigned_organization', 'asset_assigned_status']);
            });
        }
        
        // Asset pullout requests optimizations
        if (Schema::hasTable('asset_pullout_requets')) {
            Schema::table('asset_pullout_requets', function (Blueprint $table) {
                $table->index('pullout_request_number');
                $table->index('asset_serial_number');
                $table->index('outlet_id');
                $table->index('task_status');
                $table->index('created_at');
            });
        }
        
        // Asset maintenance requests optimizations
        if (Schema::hasTable('asset_maintenance_requests')) {
            Schema::table('asset_maintenance_requests', function (Blueprint $table) {
                $table->index('request_number');
                $table->index('asset_number');
                $table->index('request_status');
                $table->index('created_at');
            });
        }
    }

    /**
     * Optimize database configuration
     */
    private function optimizeDatabaseConfig()
    {
        // Increase innodb_buffer_pool_size for better performance
        // Note: This requires server access and should be done in my.cnf
        
        // Add query cache optimization
        DB::statement('SET GLOBAL query_cache_type = 1');
        DB::statement('SET GLOBAL query_cache_size = 10485760'); // 10MB
        
        // Optimize table storage
        if (Schema::hasTable('users')) {
            DB::statement('OPTIMIZE TABLE users');
        }
        if (Schema::hasTable('asset_placement_requests')) {
            DB::statement('OPTIMIZE TABLE asset_placement_requests');
        }
        if (Schema::hasTable('asset_replacement_requests')) {
            DB::statement('OPTIMIZE TABLE asset_replacement_requests');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove added indexes from users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['user_type']);
            $table->dropIndex(['user_id']);
            $table->dropIndex(['so_id']);
            $table->dropIndex(['asm_id']);
            $table->dropIndex(['rsm_id']);
            $table->dropIndex(['distributor_id']);
        });
        
        // Remove indexes from retailer_outlets
        if (Schema::hasTable('retailer_outlets')) {
            Schema::table('retailer_outlets', function (Blueprint $table) {
                $table->dropIndex(['route_name']);
                $table->dropIndex(['salesman_name']);
                $table->dropIndex(['contact_name']);
            });
        }
        
        // Remove indexes from asset_inventory
        if (Schema::hasTable('asset_inventory')) {
            Schema::table('asset_inventory', function (Blueprint $table) {
                $table->dropIndex(['serial_number']);
                $table->dropIndex(['asset_type']);
                $table->dropIndex(['approval_status']);
            });
        }
        
        // Remove indexes from asset_placement_requests
        if (Schema::hasTable('asset_placement_requests')) {
            Schema::table('asset_placement_requests', function (Blueprint $table) {
                $table->dropIndex(['request_number']);
                $table->dropIndex(['outlet_id']);
                $table->dropIndex(['asset_assigned_status']);
                $table->dropIndex(['task_status']);
                $table->dropIndex(['created_at']);
                $table->dropIndex(['assigned_organization', 'asset_assigned_status']);
            });
        }
        
        // Remove indexes from asset_replacement_requests
        if (Schema::hasTable('asset_replacement_requests')) {
            Schema::table('asset_replacement_requests', function (Blueprint $table) {
                $table->dropIndex(['request_number']);
                $table->dropIndex(['user_id', 'type']);
                $table->dropIndex(['asset_assigned_status']);
                $table->dropIndex(['task_status']);
                $table->dropIndex(['created_at']);
                $table->dropIndex(['assigned_organization', 'asset_assigned_status']);
            });
        }
        
        // Reset query cache settings
        DB::statement('SET GLOBAL query_cache_type = 0');
        DB::statement('SET GLOBAL query_cache_size = 0');
    }
};