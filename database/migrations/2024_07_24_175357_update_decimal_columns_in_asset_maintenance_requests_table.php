<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_maintenance_requests', function (Blueprint $table) {
            $table->string('distance')->change();
            $table->string('latitude')->change();
            $table->string('longitude')->change();
            $table->string('current_vpo')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_maintenance_requests', function (Blueprint $table) {
            $table->float('distance')->change();
            $table->float('latitude')->change();
            $table->float('longitude')->change();
            $table->string('current_vpo')->nullable(false)->change();
        });
    }
};
