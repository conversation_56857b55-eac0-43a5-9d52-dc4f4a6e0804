<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_error_log', function (Blueprint $table) {
            $table->id();
            $table->string('failed_type');
            $table->string('error_message')->nullable();
            $table->string('uploadedBy');
            $table->timestamp('uploadedTime')->nullable();
            $table->json('error_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_error_log');
    }
};
