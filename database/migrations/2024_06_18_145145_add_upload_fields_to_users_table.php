<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('batchID')->nullable()->after('device_type');
            $table->text('uploadBy')->nullable()->after('batchID');
            $table->string('uploadByName')->nullable()->after('uploadBy');
            $table->timestamp('uploadTime')->nullable()->after('uploadByName');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['batchID', 'uploadBy', 'uploadByName', 'uploadTime']); // Drop the added columns
        });
    }
};
