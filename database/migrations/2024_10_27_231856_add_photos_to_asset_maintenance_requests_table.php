<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_maintenance_requests', function (Blueprint $table) {
            $table->string('chiller_image')->nullable()->after('remarks');
            $table->string('outlet_photo')->nullable()->after('chiller_image');
            $table->string('retailer_photo')->nullable()->after('outlet_photo');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_maintenance_requests', function (Blueprint $table) {
            $table->dropColumn(['chiller_image', 'outlet_photo', 'retailer_photo']);
        });
    }
};
