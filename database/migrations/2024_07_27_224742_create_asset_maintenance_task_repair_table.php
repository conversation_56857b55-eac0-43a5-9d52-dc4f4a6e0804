<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_maintenance_task_repair', function (Blueprint $table) {
            $table->id();
            $table->string('user_id')->index();
            $table->string('outlet_id')->index();
            $table->string('request_number')->unique();
            $table->enum('maintenance_asset',['Yes','No'])->default('No');
            $table->string('reason_no_maintenance')->nullable();
            $table->text('remarks')->nullable();
            $table->string('outlet_photo')->nullable();
            $table->string('retailer_photo')->nullable();
            $table->string('asset_number')->index();
            $table->string('asset_barcode')->index()->nullable();
            $table->string('scanned_barcode');
            $table->string('chiller_image')->nullable();
            $table->decimal('distance')->nullable();
            $table->decimal('latitude')->nullable();
            $table->decimal('longitude')->nullable();
            $table->enum('correct_location',['Yes','No'])->default('No');
            $table->string('current_location')->nullable();
            $table->string('completion_date')->nullable();
            #heirarchy
            $table->string('ae_code')->index()->nullable();
            $table->string('rsm_code')->index()->nullable();
            $table->string('asm_code')->index()->nullable();
            $table->string('so_code')->index()->nullable();
            $table->string('db_code')->index()->nullable();
            $table->string('dsr_code')->index()->nullable();
            $table->string('cfa_code')->index()->nullable();

            #handle upload history
            $table->string('batchID')->index()->nullable();
            $table->string('uploadBy')->index()->nullable();
            $table->string('uploadByName')->nullable();
            $table->string('uploadTime')->nullable();
            $table->enum('upload_by_org',['cb','ivy'])->default('cb');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_maintenance_task_repair');
    }
};
