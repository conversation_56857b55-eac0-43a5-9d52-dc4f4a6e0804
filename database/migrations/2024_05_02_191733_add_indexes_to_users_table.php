<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->index('name', 'users_name_index');
            $table->index('city', 'users_city_index');
            $table->index('mobile_number', 'users_mobile_number_index');
            $table->index('channel_type', 'users_channel_type_index');
            $table->index('area_code', 'users_area_code_index');
            $table->index('teritory_code', 'users_teritory_code_index');
            $table->index('cfa_code', 'users_cfa_code_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_name_index');
            $table->dropIndex('users_city_index');
            $table->dropIndex('users_mobile_number_index');
            $table->dropIndex('users_channel_type_index');
            $table->dropIndex('users_area_code_index');
            $table->dropIndex('users_teritory_code_index');
            $table->dropIndex('users_cfa_code_index');
        });
    }
};
