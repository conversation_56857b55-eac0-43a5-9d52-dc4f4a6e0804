<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('retaileroutlets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->string('region')->nullable();
            $table->string('salesman_code');
            $table->string('salesman_name');
            $table->string('route_code');
            $table->string('route_name');
            $table->string('category_code');
            $table->string('category_name');
            $table->string('sub_channel_code');
            $table->string('sub_channel_name');
            $table->string('channel_code');
            $table->string('channel_name');
            $table->string('class_name');
            $table->string('pan_no')->nullable();
            $table->enum('is_tcs', ['YES', 'NO']);
            $table->string('invoiceaddress');
            $table->string('town')->nullable();
            $table->float('lat');
            $table->float('long');
            $table->string('contact_name');
            $table->string('aadhar')->nullable();
            $table->integer('credit_limit');
            $table->integer('credit_period');
            $table->string('location_code');
            $table->string('location_name');
            $table->integer('return_qty');
            $table->integer('return_value');
            $table->string('drug_license_no')->nullable();
            $table->date('drug_license_exp_date')->nullable();
            $table->string('fssai_no')->nullable();
            $table->date('fssai_exp_date')->nullable();
            $table->date('retailer_created_on');
            $table->date('modified_date');
            $table->string('retailer_reference_code')->nullable();
            $table->enum('active', ['YES', 'NO']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('retaileroutlets');
    }
};
