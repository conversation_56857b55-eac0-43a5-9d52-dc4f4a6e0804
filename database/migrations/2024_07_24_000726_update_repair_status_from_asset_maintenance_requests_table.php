<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_maintenance_requests', function (Blueprint $table) {
            DB::statement("ALTER TABLE `asset_maintenance_requests` MODIFY `repair_status` ENUM(
                'Yes',
                'No'
            )");
             $table->boolean('is_maintenance')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_maintenance_requests', function (Blueprint $table) {
            // Dropping the column
            $table->dropColumn('is_maintenance');

            // Reverting the ENUM type change
            DB::statement("ALTER TABLE `asset_maintenance_requests` MODIFY `repair_status` ENUM(
                'YES',
                'NO'
            )"); // Adjust this statement according to the original ENUM values
        });
    }
};
