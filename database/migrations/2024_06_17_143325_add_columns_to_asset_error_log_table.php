<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_error_log', function (Blueprint $table) {
            $table->string('batch_id');
            $table->text('uploadByName')->nullable();
            $table->text('request_ip');
            $table->text('userType')->nullable();
            $table->longText('error_trace')->nullable();
            $table->integer('error_code')->nullable();
            $table->integer('totalRecordscount')->nullable();
            $table->integer('successCount')->nullable();
            $table->integer('failedCount')->nullable();
            $table->string('file')->nullable();
            $table->integer('line')->nullable();
            $table->json('context')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_error_log', function (Blueprint $table) {
            $table->dropColumn([
                'batch_id',
                'uploadByName',
                'request_ip',
                'userType',
                'error_trace',
                'error_code',
                'totalRecordscount',
                'successCount',
                'failedCount',
                'file',
                'line',
                'context',
            ]);
        });
    }
};
