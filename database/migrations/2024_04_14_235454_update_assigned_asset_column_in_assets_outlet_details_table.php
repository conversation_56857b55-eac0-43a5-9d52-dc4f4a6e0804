<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets_outlet_details', function (Blueprint $table) {
            $table->string('asset_number')->nullable();
            $table->string('outlet_name')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets_outlet_details', function (Blueprint $table) {
            $table->dropColumn('asset_number');
        });
    }
};
