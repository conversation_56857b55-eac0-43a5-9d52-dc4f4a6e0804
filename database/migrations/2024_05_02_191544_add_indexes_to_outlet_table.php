<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            $table->index(['region', 'channel_code'], 'retaileroutlets_region_channel_index');
            $table->index(['class_name', 'category_name'], 'retaileroutlets_class_category_index');
            $table->index('route_name', 'retaileroutlets_route_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('retaileroutlets', function (Blueprint $table) {
            $table->dropIndex('retaileroutlets_region_channel_index');
            $table->dropIndex('retaileroutlets_class_category_index');
            $table->dropIndex('retaileroutlets_route_index');
        });
    }
};

