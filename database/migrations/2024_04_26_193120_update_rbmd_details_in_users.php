<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('rtmm_code')->nullable()->after('cfa_code');
            $table->string('rbdm_name')->nullable()->after('rtmm_code');
            $table->string('rbdm_email')->nullable()->after('rbdm_name');
            $table->string('rbdm_mobile_number')->nullable()->after('rbdm_email');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['rtmm_code','rbdm_name', 'rbdm_email', 'rbdm_mobile_number']);
        });
    }
};
