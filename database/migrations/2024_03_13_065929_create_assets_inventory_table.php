<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assets_inventory', function (Blueprint $table) {
            $table->id();
            $table->string('description');
            $table->string('barcode')->unique();
            $table->string('asset_type');
            $table->string('model_name');
            $table->string('vendor');
            $table->integer('quantity');
            $table->string('warehouse_code');
            $table->unsignedInteger('manufactured_year');
            $table->string('serial_number')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assets_inventory');
    }
};
