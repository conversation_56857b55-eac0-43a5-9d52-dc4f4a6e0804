<?php
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPulloutRequestToTypeEnumInAssetCommonCategory extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Adding the new enum value to the 'type' column
        Schema::table('asset_common_category', function (Blueprint $table) {
            // Here we will use raw SQL to modify the enum column since <PERSON><PERSON> doesn't directly support altering existing enums
            DB::statement("ALTER TABLE `asset_common_category` CHANGE `type` `type` ENUM('equipment', 'location', 'company', 'chiller_size', 'competitor_sizes', 'asset_type', 'pullout_request') NOT NULL");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Reverting the changes
        Schema::table('asset_common_category', function (Blueprint $table) {
            // Again using raw SQL to revert the enum column
            DB::statement("ALTER TABLE `asset_common_category` CHANGE `type` `type` ENUM('equipment', 'location', 'company', 'chiller_size', 'competitor_sizes', 'asset_type') NOT NULL");
        });
    }
}
