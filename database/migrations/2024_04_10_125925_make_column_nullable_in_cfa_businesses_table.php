<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cfa_businesses', function (Blueprint $table) {
            $table->string('registered_name')->nullable()->change();
            $table->string('register_number')->nullable()->change();
            $table->string('address_1')->nullable()->change();
            $table->string('address_2')->nullable()->change();
            $table->string('city')->nullable()->change();
            $table->string('state')->nullable()->change();
            $table->string('country')->nullable()->change();
            $table->string('pin_code')->nullable()->change();
            $table->string('effective_from')->nullable()->change();
            $table->string('effective_to')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cfa_businesses', function (Blueprint $table) {
            //
        });
    }
};
