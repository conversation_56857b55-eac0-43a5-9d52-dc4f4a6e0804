<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assets_outlet_details', function (Blueprint $table) {
            $table->id();
            $table->string('asset_description');
            $table->string('asset_barcode');
            $table->string('outlet_code');
            $table->string('outlet_name');
            $table->string('placement_request_number');
            $table->date('date_of_placement');
            $table->string('asset_assigned_by');
            $table->dateTime('asset_assigned_time');
            $table->dateTime('last_audit_time');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets_outlet_details', function (Blueprint $table) {
            $table->dropColumn('asset_description');
            $table->dropColumn('asset_barcode');
            $table->dropColumn('outlet_code');
            $table->dropColumn('outlet_name');
            $table->dropColumn('placement_request_number');
            $table->dropColumn('date_of_placement');
            $table->dropColumn('asset_assigned_by');
            $table->dropColumn('asset_assigned_time');

        });
    }
};
