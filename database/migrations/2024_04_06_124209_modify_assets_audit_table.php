<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets_audit', function (Blueprint $table) {
            $table->string('contact_number')->nullable();
            $table->string('retailer_name')->nullable();

            // Modify an existing column
            $table->string('pincode')->nullable()->change();
            $table->string('customer_address')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('table_name_here', function (Blueprint $table) {
            $table->dropColumn('contact_number');
            $table->dropColumn('retailer_name');


            // If you modified an existing column, you might want to reverse the changes here
            // $table->string('existing_column_name')->change();
        });
    }
};
