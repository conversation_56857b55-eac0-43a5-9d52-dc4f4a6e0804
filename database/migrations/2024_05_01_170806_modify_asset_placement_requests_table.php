<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->string('outlet_code')->nullable();
            $table->string('ae_code')->nullable();
            $table->string('rsm_code')->nullable();
            $table->string('asm_code')->nullable();
            $table->string('so_code')->nullable();
            $table->string('db_code')->nullable();
            $table->string('dsr_code')->nullable();
            $table->string('cfa_code')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('asset_placement_requests', function (Blueprint $table) {
            $table->dropColumn(['outlet_code', 'ae_code', 'rsm_code', 'asm_code', 'so_code', 'db_code', 'dsr_code', 'cfa_code']);
        });
    }
};
