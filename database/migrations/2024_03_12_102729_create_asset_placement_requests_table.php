<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_placement_requests', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('outlet_id');
            $table->string('type');
            $table->string('expected_vpo');
            $table->string('eligible_chiller_type')->nullable();
            $table->string('request_type');
            $table->json('additional_equipment')->nullable();
            $table->json('competitor_chiller_size')->nullable();
            $table->json('competitor_company')->nullable();
            $table->string('competitor_chiller_photo')->nullable();
            $table->string('chiller_location');
            $table->string('chiller_location_photo');
            $table->string('address_proof');
            $table->string('retailer_photo');
            $table->string('signature');
            $table->string('mobile_number');
            $table->string('customer_address');
            $table->string('pincode', 6);
            $table->string('customer_location')->nullable();
            $table->string('current_location')->nullable();
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            $table->enum('correct_location', ['Yes', 'No'])->nullable();
            $table->double('distance')->nullable();
            $table->string('remarks')->nullable();
            $table->enum('consent_status', ['Confirmed'])->nullable();
            $table->string('request_number')->unique()->nullable();
            $table->enum('asset_assigned_status', ['Approved', 'Rejected', 'Pending'])->default('Pending');


            // Define foreign key constraints
            // $table->foreign('user_id')->references('user_id')->on('users');
            $table->foreign('outlet_id')->references('id')->on('retaileroutlets');
            $table->index('user_id');
        $table->index('outlet_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_placement_requests');
    }
};
