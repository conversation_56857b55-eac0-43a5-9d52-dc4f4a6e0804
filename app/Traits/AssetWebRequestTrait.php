<?php

namespace App\Traits;

use App\Services\AsssetInventory\AsssetInventoryService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

trait AssetWebRequestTrait
{
    public function handleAssetRequest(Request $request, $type)
    {
        $user = Auth::user();
        $filterData = PlacementRequestService::PlacementFilter($user, $type);
        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);
        $asset_type = AsssetInventoryService::getAssetType($user);
        $assetPlacementList = PlacementRequestService::placementRequestList($request, $user, $type)
            ->latest()
            ->paginate($limit, ['*'], 'page', $currentPage);

        $assetPlacementListView = PlacementRequestService::placementRequestTableData($assetPlacementList, $type);
        return [
            "asset_type" => $asset_type,
            "assetPlacementList" => $assetPlacementList,
            "assetPlacementListView" => $assetPlacementListView,
            "user" => $user,
            'so_territory_list' => $filterData['so_territory_list'] ?? '',
            'asm_territory_list' => $filterData['asm_territory_list'] ?? '',
            'city_list' => $filterData['city_list'] ?? '',
            'db_name_list' => $filterData['db_name_list'] ?? '',
            'db_code_list' => $filterData['db_code_list'] ?? '',
            'placement_request_list' => $filterData['placement_request_list'] ?? '',
        ];
    }
}
