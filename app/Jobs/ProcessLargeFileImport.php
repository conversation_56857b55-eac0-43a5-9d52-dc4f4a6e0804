<?php

namespace App\Jobs;

use App\Imports\UserMasterImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;

class ProcessLargeFileImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;
    protected $userRole;
    protected $batchID;
    
    // Optimized for very large files (300k+ records)
    public $timeout = 14400; // 4 hours for massive files
    public $tries = 1; // Don't retry large files automatically
    public $maxExceptions = 3;
    public $backoff = [300, 900]; // 5 min, 15 min backoff

    /**
     * Create a new job instance.
     */
    public function __construct($filePath, $userRole, $batchID)
    {
        $this->filePath = $filePath;
        $this->userRole = $userRole;
        $this->batchID = $batchID;
        
        // Always use high-priority queue for large files
        $this->onQueue('high-priority');
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $startTime = microtime(true);
        $file = Storage::path($this->filePath);
        
        Log::info('Starting large file import processing', [
            'file_path' => $this->filePath,
            'user_role' => $this->userRole,
            'batch_id' => $this->batchID,
            'file_size' => file_exists($file) ? $this->formatBytes(filesize($file)) : 'unknown',
            'estimated_rows' => file_exists($file) ? $this->estimateRowCount(filesize($file)) : 0
        ]);

        try {
            // Set maximum limits for large file processing
            $this->setMaximumLimits($file);
            
            // Enable garbage collection for memory optimization
            gc_enable();
            
            // Process the import with optimized settings
            Excel::import(new UserMasterImport($this->userRole, $this->batchID), $file);
            
            // Force garbage collection
            gc_collect_cycles();
            
            $endTime = microtime(true);
            Log::info('Large file import completed successfully', [
                'batch_id' => $this->batchID,
                'execution_time' => round($endTime - $startTime, 2) . ' seconds',
                'peak_memory' => $this->formatBytes(memory_get_peak_usage()),
                'final_memory' => $this->formatBytes(memory_get_usage())
            ]);
            
        } catch (Throwable $th) {
            Log::error('ProcessLargeFileImport failed', [
                'error' => $th->getMessage(),
                'stack' => $th->getTraceAsString(),
                'file' => $this->filePath,
                'user_role' => $this->userRole,
                'batch_id' => $this->batchID,
                'memory_usage' => $this->formatBytes(memory_get_usage()),
                'peak_memory' => $this->formatBytes(memory_get_peak_usage())
            ]);
            throw $th;
        } finally {
            // Clean up the temporary file
            if (Storage::exists($this->filePath)) {
                Storage::delete($this->filePath);
                Log::info('Deleted large file after processing', [
                    'file' => $this->filePath,
                    'batch_id' => $this->batchID
                ]);
            }
        }
    }
    
    /**
     * Set maximum memory and time limits for very large files
     */
    private function setMaximumLimits($file)
    {
        // Set maximum time limit
        set_time_limit(14400); // 4 hours
        
        if (file_exists($file)) {
            $fileSize = filesize($file);
            
            // Set aggressive memory limit for large files
            $recommendedMemory = max($fileSize * 6, 2 * 1024 * 1024 * 1024); // 6x file size or 2GB minimum
            $maxMemory = 8 * 1024 * 1024 * 1024; // Cap at 8GB
            
            $finalMemory = min($recommendedMemory, $maxMemory);
            ini_set('memory_limit', $this->formatBytes($finalMemory, 0));
            
            Log::info('Set maximum limits for large file', [
                'file_size' => $this->formatBytes($fileSize),
                'memory_limit' => $this->formatBytes($finalMemory),
                'time_limit' => '4 hours'
            ]);
        }
    }
    
    /**
     * Estimate row count based on file size
     */
    private function estimateRowCount($fileSize)
    {
        // More accurate estimation for large files
        return intval($fileSize / 120); // Average 120 bytes per row
    }
    
    /**
     * Format bytes for display
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Handle job failure
     */
    public function failed(Throwable $exception)
    {
        Log::error('Large file import job failed permanently', [
            'batch_id' => $this->batchID,
            'file_path' => $this->filePath,
            'user_role' => $this->userRole,
            'error' => $exception->getMessage(),
            'stack' => $exception->getTraceAsString()
        ]);
        
        // Clean up file on failure
        if (Storage::exists($this->filePath)) {
            Storage::delete($this->filePath);
        }
    }
}
