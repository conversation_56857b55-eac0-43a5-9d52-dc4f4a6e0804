<?php

namespace App\Jobs;

use App\Models\AssetApprovalPlacementRequestHistory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AssetPlacementApprovalHistoryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $prepareUserData;
    protected $user;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($prepareUserData, $user)
    {
        $this->prepareUserData = $prepareUserData;
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $amprocess = $this->prepareUserData;

        $user = $this->user;
        $actionUpdatedTime = now();
        $request_status = $amprocess['request_status'];

        if($amprocess['request_pending_from']=='ASM'){
            $data[] = [
                'user_id' => $amprocess['asm_code'],
                'user_role' => 'ASM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => $request_status=='rejected'?'Rejected':'Approved',
            ];
        }
        if($amprocess['request_pending_from']=='RSM'){
            $data[] = [
                'user_id' => $amprocess['asm_code'],
                'user_role' => 'ASM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ];
            $data[] =  [
                'user_id' => $amprocess['rsm_code'],
                'user_role' => 'RSM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => $request_status=='rejected'?'Rejected':'Approved',
            ];
        }
        if($amprocess['request_pending_from']=='AE'){
            $data[] = [
                'user_id' => $amprocess['asm_code'],
                'user_role' => 'ASM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ];
            $data[] =  [
                'user_id' => $amprocess['rsm_code'],
                'user_role' => 'RSM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ];
            $data[] =  [
                'user_id' => $amprocess['ae_code'],
                'user_role' => 'AE',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => $request_status=='rejected'?'Rejected':'Approved',
            ];
        }
        if($amprocess['request_pending_from']=='AE'){
            $data[] = [
                'user_id' => $amprocess['asm_code'],
                'user_role' => 'ASM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ];
            $data[] =  [
                'user_id' => $amprocess['rsm_code'],
                'user_role' => 'RSM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ];
            $data[] =   [
                'user_id' => $amprocess['ae_code'],
                'user_role' => 'AE',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ];
            $data[] =   [
                'user_id' => $amprocess['cfa_code'],
                'user_role' => 'VENDOR',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => $request_status=='rejected'?'Rejected':'Approved',
            ];
        }

        // Unique columns to determine if a record should be updated
        $uniqueBy = ['user_id', 'user_role', 'asset_placement_request_id'];

        // Columns to be updated if a matching record exists
        $update = ['action_updated_time', 'action'];

        // Perform the upsert operation
        AssetApprovalPlacementRequestHistory::upsert($data, $uniqueBy, $update);
        Log::info('AssetPlacementApprovalHistoryJob  job processd for Asset Retailter Mapping with userID completed', ['request_number' => $amprocess['request_number']]);
    }
}
