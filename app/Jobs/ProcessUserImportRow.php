<?php

namespace App\Jobs;

use App\Library\Utils\DateFormate;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\ErrorService\ErrorHandlingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;
class ProcessUserImportRow implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;
    protected $userRole;
    protected $userData;
    protected $prepareUserData;
    protected $row;
    protected $authUser;
    protected $ipAddress;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userId, $userRole, $userData, $prepareUserData, $row, $authUser,$ipAddress)
    {
        $this->userId = $userId;
        $this->userRole = $userRole;
        $this->userData = $userData;
        $this->prepareUserData = $prepareUserData;
        $this->row = $row;
        $this->authUser = $authUser;
        $this->ipAddress = $ipAddress;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $prepareUserData = $this->prepareUserData;

        $authUser = $this->authUser;
        try {
            DB::beginTransaction();
//            if (empty($this->userData['user_id'])) {
//                throw new \Exception("User ID is missing from userData.");
//            }
            $user = User::updateOrCreate(
                ['user_id' => $this->userData['user_id'], 'user_type' => $this->userRole],
                $this->userData
            );
            Log::info('ProcessUserImportRow '.$this->userRole, ['id' => $user->id, 'userId' => $user->user_id]);

            if ($this->userRole !== 'RETAILER' && $user->wasRecentlyCreated) {
                UserImport::dispatch($user);
            }

            if ($this->userRole == 'RETAILER') {
                $prepareUserData['drug_license_exp_date'] = isset($this->row['drug_license_exp_date']) ? $this->convertDateFormatToDbFormat($this->row['drug_license_exp_date']) : null;
                $prepareUserData['fssai_exp_date'] = isset($this->row['fssai_exp_date']) ? $this->convertDateFormatToDbFormat($this->row['fssai_exp_date']) : null;
                $prepareUserData['retailer_created_on'] = isset($this->row['retailer_created_on']) ? $this->convertDateFormatToDbFormat($this->row['retailer_created_on']) : date('Y-m-d');
                $prepareUserData['modified_date'] = isset($this->row['modified_date']) ? $this->convertDateFormatToDbFormat($this->row['modified_date']) : date('Y-m-d');
                $prepareUserData['user_id'] = $user->id;

                $retailerOutlet = RetailerOutlet::firstOrCreate(
                    ['user_id' => $user->id],
                    $prepareUserData
                );
                Log::info('ProcessUserImportRow RetailerOutlet: ', ['RetailerOutlet id' => $retailerOutlet->id]);

            }
            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        }
    }
    private function handleException($e, $prepareUserData, $LoginAuthUser)
    {
        $authUser = $this->authUser;
        $errorMessage = $e->getMessage();
        $logData = array_merge($prepareUserData, $this->userData);

        ErrorHandlingService::handleErrorLog(
            $this->userData['batchID'],
            $errorMessage,
            $this->ipAddress,
            $authUser->user_type,
            $this->userData['user_type'],
            $logData,
            $authUser->userId,
            $authUser->name
        );

        Log::error('ProcessUserImportRow Exception: ' . $errorMessage, $logData);
    }
    private function convertDateFormatToDbFormat($excelField)
    {
        return DateFormate::convertDbFormat($excelField);
    }
}
