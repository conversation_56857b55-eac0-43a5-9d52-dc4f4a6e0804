<?php

namespace App\Jobs;

use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\ErrorService\ErrorHandlingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;
class UpdateUserRetailerGSTupdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ipAddress;
    protected $authUser;
    protected $userRole;
    protected $batchID;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $prepareUserData,$ipAddress, $authUser, $userRole, $batchID)
    {
        $this->ipAddress = $ipAddress;
        $this->authUser = $authUser;
        $this->userRole = $userRole;
        $this->batchID = $batchID;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $authUser = $this->authUser;
        try {
            DB::beginTransaction();
        $prepareUserData = $this->prepareUserData;
        $user = User::where(['user_id' => $prepareUserData['user_id']])->first();
        if ($user) {
            $user->update([
                'gst_no' => $prepareUserData['gst_no']??'',
            ]);
            Log::info('UpdateUserRetailerGSTupdateJob job queue userId: ', ['userId' => $prepareUserData['user_id']]);
        }else{
            throw new \Exception("Customer Code not exist ".$this->userRole);
        }
            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        }
    }

    private function handleException($e, $prepareUserData, $authUser)
    {
        $errorMessage = $e->getMessage();
        ErrorHandlingService::handleErrorLog(
            $this->batchID,
            $errorMessage,
            $this->ipAddress,
            $this->userRole,
            $authUser->user_type,
            $prepareUserData,
            $authUser->user_id,
            $authUser->name
        );
        Log::error('UpdateUserRetailerGSTupdateJob Exception: ' . $errorMessage, $prepareUserData);
    }
}
