<?php

namespace App\Jobs;

use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\ErrorService\ErrorHandlingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;
class UpdateRetailer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ipAddress;
    protected $authUser;
    protected $userRole;
    protected $batchID;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $prepareUserData, $ipAddress, $authUser, $userRole, $batchID)
    {
        //
        $this->ipAddress = $ipAddress;
        $this->authUser = $authUser;
        $this->userRole = $userRole;
        $this->batchID = $batchID;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $authUser = $this->authUser;
        try {
            DB::beginTransaction();
            $prepareUserData = $this->prepareUserData;
            $user = User::where(['user_id' => $prepareUserData['user_id']])->first();
            if ($user) {
                $user->update([
                    'address_1' => $prepareUserData['address_1'] ?? '',
                    'address_2' => $prepareUserData['address_2'] ?? '',
                    'address_3' => $prepareUserData['address_3'] ?? '',
                    'pin_code' => $prepareUserData['pin_code'] ?? '',
                    'batchID' => $this->batchID,
                ]);
                $user_id = $user->id;
                $outlet = RetailerOutlet::where('user_id', $user_id)->update([
                    'lat' => $prepareUserData['lat'] ?? '',
                    'long' => $prepareUserData['long'] ?? '',
                ]);
                Log::info('job queue userId: ', ['userId' => $user_id]);
            }
            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        }
    }

    private function handleException($e, $prepareUserData, $authUser)
    {
        $errorMessage = $e->getMessage();
        ErrorHandlingService::handleErrorLog(
            $this->batchID,
            $errorMessage,
            $this->ipAddress,
            $this->userRole,
            $authUser->user_type,
            $prepareUserData,
            $authUser->user_id,
            $authUser->name
        );

        Log::error('ProcessUserImportRow Exception: ' . $errorMessage, $prepareUserData);
    }
}
