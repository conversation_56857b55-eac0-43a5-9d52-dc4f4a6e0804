<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\ErrorService\ErrorHandlingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;

class DBToCFAVendorMappingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $prepareUserData;
    protected $ipAddress;
    protected $authUser;
    protected $userRole;
    protected $batchID;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($prepareUserData, $ipAddress, $authUser, $userRole, $batchID)
    {
        $this->prepareUserData = $prepareUserData;
        $this->ipAddress = $ipAddress;
        $this->authUser = $authUser;
        $this->userRole = $userRole;
        $this->batchID = $batchID;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $authUser = $this->authUser;
        try {
            DB::beginTransaction();
            $prepareUserData = $this->prepareUserData;
            User::where(['user_id' => $prepareUserData['distributor_id'], 'user_type' => 'DISTRIBUTOR'])
                ->update($prepareUserData);
            Log::info('DBToCFAVendorMappingJob Mapped Queue processed: ', ['user_id' => $prepareUserData['distributor_id']]);
            DB::commit();
        } catch (QueryException $e) {
            Log::info('DBToCFAVendorMappingJob QueryException: ' . $e->getMessage(), ['user_id' => $prepareUserData['distributor_id']]);
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        } catch (\Exception $e) {
            Log::info('DBToCFAVendorMappingJob Exception: ' . $e->getMessage(), ['user_id' => $prepareUserData['distributor_id']]);
            DB::rollBack();
            $this->handleException($e, $prepareUserData, $authUser);
        }
    }

    private function handleException($e, $prepareUserData, $authUser)
    {
        $errorMessage = $e->getMessage();
        ErrorHandlingService::handleErrorLog(
            $this->batchID,
            $errorMessage,
            $this->ipAddress,
            $this->userRole,
            $authUser->user_type,
            $prepareUserData,
            $authUser->user_id,
            $authUser->name
        );

        Log::error('ProcessUserImportRow Exception: ' . $errorMessage, $prepareUserData);
    }
}
