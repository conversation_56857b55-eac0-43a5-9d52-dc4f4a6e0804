<?php

namespace App\Jobs;

use App\Imports\UserMasterImport;
use App\Services\UserImportService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Throwable;
use Maatwebsite\Excel\Facades\Excel;
class ProcessUploaderImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;
    protected $userRole;
    protected $uploadToken;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($filePath, $userRole,$uploadToken)
    {
        $this->filePath = $filePath;
        $this->userRole = $userRole;
        $this->uploadToken = $uploadToken; // Store the upload token
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(UserImportService $userImportService)
    {
        $file = Storage::path($this->filePath);

        try {
            Excel::import(new UserMasterImport( $this->userRole), $file);
            // $userImportService->importUsers($file, $this->userRole);
        } catch (Throwable $th) {
            \Log::error('ProcessUploaderImport Failed to import users in background job', [
                'error' => $th->getMessage(),
                'stack' => $th->getTraceAsString(),
                'file' => $this->filePath,
                'user_role' => $this->userRole,
            ]);
            throw $th;
        } finally {
            // Storage::delete($this->filePath);
            if (Storage::exists($this->filePath)) {
                Storage::delete($this->filePath);
                \Log::info('Deleted uploaded file after processing', [
                    'file' => $this->filePath,
                ]);
            }
        }
    }
}
