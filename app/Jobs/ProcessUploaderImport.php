<?php

namespace App\Jobs;

use App\Imports\UserMasterImport;
use App\Services\UserImportService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Throwable;
use Maatwebsite\Excel\Facades\Excel;
class ProcessUploaderImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;
    protected $userRole;
    protected $uploadToken;
    protected $batchID;

    // Performance optimization properties
    public $timeout = 7200; // 2 hours for very large files
    public $tries = 2; // Retry once if failed
    public $maxExceptions = 5; // Allow up to 5 exceptions
    public $backoff = [60, 300]; // Backoff delays: 1 min, 5 min

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($filePath, $userRole, $uploadToken, $batchID = null)
    {
        $this->filePath = $filePath;
        $this->userRole = $userRole;
        $this->uploadToken = $uploadToken;
        $this->batchID = $batchID;

        // Set queue based on estimated file complexity
        $this->onQueue($this->determineQueue());
    }

    /**
     * Determine appropriate queue based on user role complexity
     */
    private function determineQueue()
    {
        $complexRoles = ['asset_retailer_mapping', 'asset_inventory_upload'];

        if (in_array($this->userRole, $complexRoles)) {
            return 'high-priority';
        }

        return 'default';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $startTime = microtime(true);
        $file = Storage::path($this->filePath);

        Log::info('Starting background import processing', [
            'file_path' => $this->filePath,
            'user_role' => $this->userRole,
            'batch_id' => $this->batchID,
            'file_size' => file_exists($file) ? $this->formatBytes(filesize($file)) : 'unknown'
        ]);

        try {
            // Set optimal memory and time limits
            $this->setOptimalLimits($file);

            // Process the import with batch ID
            Excel::import(new UserMasterImport($this->userRole, $this->batchID), $file);

            $endTime = microtime(true);
            Log::info('Background import completed successfully', [
                'batch_id' => $this->batchID,
                'execution_time' => round($endTime - $startTime, 2) . ' seconds',
                'peak_memory' => $this->formatBytes(memory_get_peak_usage())
            ]);

        } catch (Throwable $th) {
            Log::error('ProcessUploaderImport Failed to import users in background job', [
                'error' => $th->getMessage(),
                'stack' => $th->getTraceAsString(),
                'file' => $this->filePath,
                'user_role' => $this->userRole,
                'batch_id' => $this->batchID,
            ]);
            throw $th;
        } finally {
            // Clean up the temporary file
            if (Storage::exists($this->filePath)) {
                Storage::delete($this->filePath);
                Log::info('Deleted uploaded file after processing', [
                    'file' => $this->filePath,
                    'batch_id' => $this->batchID
                ]);
            }
        }
    }

    /**
     * Set optimal memory and time limits for large file processing
     */
    private function setOptimalLimits($file)
    {
        if (file_exists($file)) {
            $fileSize = filesize($file);

            // Set time limit based on file size
            if ($fileSize > 104857600) { // > 100MB
                set_time_limit(7200); // 2 hours
            } elseif ($fileSize > 52428800) { // > 50MB
                set_time_limit(3600); // 1 hour
            } else {
                set_time_limit(1800); // 30 minutes
            }

            // Increase memory limit if needed
            $currentLimit = $this->getMemoryLimitInBytes();
            $recommendedMemory = max($fileSize * 4, 1024 * 1024 * 1024); // 4x file size or 1GB minimum

            if ($currentLimit < $recommendedMemory && $currentLimit != -1) {
                ini_set('memory_limit', $this->formatBytes($recommendedMemory, 0));
            }
        }
    }

    /**
     * Get memory limit in bytes
     */
    private function getMemoryLimitInBytes()
    {
        $memoryLimit = ini_get('memory_limit');

        if ($memoryLimit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) $memoryLimit;

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }

    /**
     * Format bytes for display
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
