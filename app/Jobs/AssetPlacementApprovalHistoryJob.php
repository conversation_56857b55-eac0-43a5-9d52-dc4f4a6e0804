<?php

namespace App\Jobs;

use App\Models\AssetApprovalPlacementRequestHistory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AssetPlacementApprovalHistoryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $prepareUserData;
    protected $user;

    /**
     * Create a new job instance.
     *
     * @param array $prepareUserData
     * @param mixed $user
     */
    public function __construct($prepareUserData, $user)
    {
        $this->prepareUserData = $prepareUserData;
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $amprocess = $this->prepareUserData;
        $request_status = $amprocess['request_status'];
        $actionUpdatedTime = now();
        $data = [];

        // Default: ASM action (based on pending status)
        if ($amprocess['request_pending_from'] === 'ASM') {
            $this->addAction($data, $amprocess['asm_code'], 'ASM', $amprocess['request_number'], $actionUpdatedTime, $request_status);
        }
        // If RSM is pending, ASM should be approved by default, and then handle RSM's status
        if ($amprocess['request_pending_from'] === 'RSM') {
            $this->addAction($data, $amprocess['asm_code'], 'ASM', $amprocess['request_number'], $actionUpdatedTime, 'approved'); // Ensure ASM is added
            $this->addAction($data, $amprocess['rsm_code'], 'RSM', $amprocess['request_number'], $actionUpdatedTime, $request_status);
        }

        // If AE is pending, ASM and RSM should be approved by default, and then handle AE's status
        if ($amprocess['request_pending_from'] === 'AE') {
            $this->addAction($data, $amprocess['asm_code'], 'ASM', $amprocess['request_number'], $actionUpdatedTime, 'approved'); // Ensure ASM is added
            $this->addAction($data, $amprocess['rsm_code'], 'RSM', $amprocess['request_number'], $actionUpdatedTime, 'approved'); // Ensure RSM is added
            $this->addAction($data, $amprocess['ae_code'], 'AE', $amprocess['request_number'], $actionUpdatedTime, $request_status);
        }

        // If CFA (Vendor) is pending, ASM, RSM, and AE should be approved by default, and handle Vendor's status
        if ($amprocess['request_pending_from'] === 'CFA') {
            $this->addAction($data, $amprocess['asm_code'], 'ASM', $amprocess['request_number'], $actionUpdatedTime, 'approved'); // Ensure ASM is added
            $this->addAction($data, $amprocess['rsm_code'], 'RSM', $amprocess['request_number'], $actionUpdatedTime, 'approved'); // Ensure RSM is added
            $this->addAction($data, $amprocess['ae_code'], 'AE', $amprocess['request_number'], $actionUpdatedTime, 'approved'); // Ensure AE is added
            $this->addAction($data, $amprocess['cfa_code'], 'VENDOR', $amprocess['request_number'], $actionUpdatedTime, $request_status);
        }

        // Unique columns to determine if a record should be updated
        $uniqueBy = ['user_id', 'user_role', 'asset_placement_request_id'];

        // Columns to be updated if a matching record exists
        $update = ['action_updated_time', 'action'];

        // Perform the upsert operation
        AssetApprovalPlacementRequestHistory::upsert($data, $uniqueBy, $update);

        Log::info('AssetPlacementApprovalHistoryJob processed', ['request_number' => $amprocess['request_number']]);
    }

    /**
     * Helper function to add action data
     *
     * @param array $data
     * @param mixed $userId
     * @param string $role
     * @param mixed $requestNumber
     * @param \Illuminate\Support\Carbon $actionUpdatedTime
     * @param string $request_status
     * @return void
     */
    private function addAction(array &$data, $userId, $role, $requestNumber, $actionUpdatedTime, $request_status)
    {
        $data[] = [
            'user_id' => $userId,
            'user_role' => $role,
            'asset_placement_request_id' => $requestNumber,
            'action_updated_time' => $actionUpdatedTime,
            'action' => $request_status === 'rejected' ? 'Rejected' : 'Approved',
        ];
    }
}
