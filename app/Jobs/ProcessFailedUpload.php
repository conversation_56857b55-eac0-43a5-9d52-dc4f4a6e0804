<?php

namespace App\Jobs;

use App\Models\AssetErrorLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
class ProcessFailedUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $failedUploader;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($failedUploader)
    {
        $this->failedUploader = $failedUploader;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        AssetErrorLog::create($this->failedUploader);
    }
}
