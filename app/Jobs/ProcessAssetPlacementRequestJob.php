<?php

namespace App\Jobs;

use App\Models\AssetPlacementRequest;
use App\Services\ErrorService\ErrorHandlingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;
class ProcessAssetPlacementRequestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $aprData;
    protected $ip;
    protected $authUser;
    protected $uploadType;
    protected $batchId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($aprData, $ip, $authUser, $uploadType, $batchId)
    {
        $this->aprData = $aprData;
        $this->ip = $ip;
        $this->authUser = $authUser;
        $this->uploadType = $uploadType;
        $this->batchId = $batchId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $authUser = $this->authUser;
        try {
            DB::beginTransaction();
            AssetPlacementRequest::firstOrCreate(
                ['request_number' => $this->aprData['request_number']],
                $this->aprData
            );
            Log::info('ProcessAssetPlacementRequestJob job Processed', ['request_number' => $this->aprData['request_number'], 'asset_serial_number' => $this->aprData['asset_serial_number']]);
            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            $this->handleException($e, $this->aprData, $authUser);
            Log::error('ProcessAssetPlacementRequestJob QueryException database error: ' . $e->getMessage());
        } catch (\Exception $e) {
            DB::rollBack();
            $this->handleException($e, $this->aprData, $authUser);
            Log::error('ProcessAssetPlacementRequestJob Exception: ' . $e->getMessage());
        }
    }

    private function handleException($e, $prepareUserData, $authUser)
    {
        $errorMessage = $e->getMessage();
        ErrorHandlingService::handleErrorLog(
            $this->batchId,
            $errorMessage,
            $this->ip,
            $this->uploadType,
            $authUser->user_type,
            $prepareUserData,
            $authUser->user_id,
            $authUser->name
        );
        Log::error('ProcessAssetPlacementRequestJob Exception: ' . $errorMessage, $prepareUserData);
    }
}
