<?php

namespace App\Jobs;

use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\AssetTaskPlacement\AssetPlacementTaskDeploymentModel;
use App\Models\OutletAssetDetailsModel;
use App\Models\User;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessAssetMappingUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $prepareUserData;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($prepareUserData)
    {
        $this->prepareUserData = $prepareUserData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $amprocess = $this->prepareUserData;
        //create Asset Placement Request
        $user = User::where('user_id', $amprocess['outlet_code'])->with(['retaileroutlets' => function ($query) {
            $query->select('id', 'user_id', 'lat', 'long');
        }])->first();
        $aprData = [
            'user_id' => $amprocess['so_code'],
            'outlet_id' => $user->retaileroutlets->id ?? '',
            'type' => 'SO',
            'expected_vpo' => $amprocess['expected_vpo'] ?? '',
            'eligible_chiller_type' => $amprocess['eligible_chiller_type'] ?? null,
            'request_type' => 'Normal',
            'additional_equipment' => [],
            'competitor_chiller_size' => [],
            'competitor_company' => [],
            'chiller_location' => 'Others',
            'chiller_location_photo' => 'NULL',
            'customer_address' => $amprocess['customer_address'],
            'customer_location' => $amprocess['customer_address'],
            'current_location' => $amprocess['customer_address'],
            'latitude' => $user->retaileroutlets->lat ?? '',
            'longitude' => $user->retaileroutlets->long ?? '',
            'correct_location' => 'Yes',
            'remarks' => 'ivy asset mapping',
            'asset_mapping_type' => 'ivy',
            'consent_status' => 'Confirmed',
            'vpo_target' => $amprocess['vpo'],
            "outlet_code" => $amprocess['outlet_code'],
            "ae_code" => $amprocess['ae_code'],
            "rsm_code" => $amprocess['rsm_code'],
            "asm_code" => $amprocess['asm_code'],
            "so_code" => $amprocess['so_code'],
            "db_code" => $amprocess['db_code'],
            "dsr_code" => $amprocess['dsr_code'],
            "cfa_code" => $amprocess['cfa_code'],
            "asset_serial_number" => $amprocess['asset_serial_number'],
            "asset_barcode" => $amprocess['asset_barcode'],
            "asset_assigned_status" => 'Approved',
            "approved_time" => now(),
            "placement_approved_time" => now(),
            "deployment_date" => $amprocess['deployment_date'],

            'approved_by_user_id' => $amprocess['cfa_code'],
            'approved_by_user_role' => 'VENDOR',
            'pending_from' => 'VE',
            'is_deploy' => 1,
            'is_quantity_allocated' => 'YES',
            'expected_deployment_date' => $amprocess['deployment_date'],
            'assigned_organization' => $amprocess['cfa_code'] . "_001",
            'chiller_placed' => 'Yes',
            'task_status' => 'Completed',
        ];

        // Create or update the asset placement request record
        AssetPlacementRequest::updateOrCreate(
            ['request_number' => $aprData['request_number']],
            $aprData
        );

        //Create placement Approval history
        $actionUpdatedTime = now();
        $data = [
            [
                'user_id' => $amprocess['asm_code'],
                'user_role' => 'ASM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ],
            [
                'user_id' => $amprocess['rsm_code'],
                'user_role' => 'RSM',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ],
            [
                'user_id' => $amprocess['ae_code'],
                'user_role' => 'AE',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ],
            [
                'user_id' => $amprocess['cfa_code'],
                'user_role' => 'VENDOR',
                'asset_placement_request_id' => $amprocess['request_number'],
                'action_updated_time' => $actionUpdatedTime,
                'action' => 'Approved',
            ],
        ];

        // Unique columns to determine if a record should be updated
        $uniqueBy = ['user_id', 'user_role', 'asset_placement_request_id'];

        // Columns to be updated if a matching record exists
        $update = ['action_updated_time', 'action'];

        // Perform the upsert operation
        AssetApprovalPlacementRequestHistory::upsert($data, $uniqueBy, $update);

        //create task complete placement

        $taskDeployAPRData = [
            'asset_barcode' => $amprocess['asset_barcode'],
            'asset_number' => $amprocess['asset_serial_number'],
            'chiller_placed' => 'Yes',
            'correct_location' => 'Yes',
            'current_location' => $amprocess['customer_address'],
            'latitude' => $user->retaileroutlets->lat ?? '',
            'longitude' => $user->retaileroutlets->long ?? '',
            'outlet_id' => $user->retaileroutlets->id ?? '',
            'remarks' => 'ivy task completed',
            'request_number' => $aprData['request_number'],
            'user_id' => $amprocess['cfa_code'] . "_001",
            'outlet_code' => $amprocess['outlet_code'],
            "ae_code" => $amprocess['ae_code'],
            "rsm_code" => $amprocess['rsm_code'],
            "asm_code" => $amprocess['asm_code'],
            "so_code" => $amprocess['so_code'],
            "db_code" => $amprocess['db_code'],
            "dsr_code" => $amprocess['dsr_code'],
            "cfa_code" => $amprocess['cfa_code'],
            "completion_date" => now(),
        ];
        AssetPlacementTaskDeploymentModel::updateOrCreate(
            ['request_number' => $taskDeployAPRData['request_number']],
            $taskDeployAPRData
        );
        //create outlet details

        $assignedAssetData = [
            'asset_description' => $amprocess['eligible_chiller_type'] ?? null,
            'asset_barcode' =>  $amprocess['asset_barcode'] ?? '',
            'asset_number' => $amprocess['asset_serial_number'] ?? '',
            'outlet_code' => $amprocess['outlet_code'] ?? '',
            'placement_request_number' => $amprocess['request_number'] ?? '',
            'date_of_placement' => date("Y-m-d"),
            'asset_assigned_by' => $amprocess['cfa_code'] . "_001",
            'asset_assigned_time' => now(),
            'last_audit_time' => now(),
        ];
        $res = OutletAssetDetailsModel::updateOrCreate(
            [
                'asset_number' => $amprocess['asset_serial_number'],
                'placement_request_number' => $amprocess['request_number']
            ],
            $assignedAssetData
        );
        Log::info('ProcessAssetMappingUpload Asset Retailter Mapping: ', $amprocess);
    }
}
