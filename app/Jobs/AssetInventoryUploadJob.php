<?php

namespace App\Jobs;

use App\Models\AsssetInventory;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\ErrorService\ErrorHandlingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class AssetInventoryUploadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $prepareUserData;
    public $userType;
    public $userID;
    public $ip;
    public $authUser;
    public $uploadType;
    public $batchId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $prepareUserData, string $userType, string $userID, $ip, $authUser, $uploadType, $batchId)
    {
        $this->prepareUserData = $prepareUserData;
        $this->userType = $userType;
        $this->userID = $userID;
        $this->ip = $ip;
        $this->authUser = $authUser;
        $this->uploadType = $uploadType;
        $this->batchId = $batchId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $authUser = $this->authUser;
        try {
            DB::beginTransaction();
            $data = $this->prepareUserData;
            $loginUserID = $this->userID;
            $loginUserType = $this->userType;
            // Log::info('Processing AssetInventoryUploadJob', ['loginUserID' => $loginUserID, 'loginUserType' => $loginUserType]);
            $alreadyExsit = AsssetInventory::where('serial_number',$data['serial_number'])->exists();
            if ($alreadyExsit) {
                throw new \Exception("Serial number already exist   " . $data['serial_number']);
                Log::info('"Serial number already exist .', ['data' => $data]);
            }

            if ($loginUserType == "VENDOR" && $data['warehouse_code'] == $loginUserID) {
                $data['asset_approval_status'] = 'pending';
                $data['approved_by_user'] = '';
                $data['approved_by_user'] = '';
                $data['approval_time'] = now();
                $this->updateAssetInventory($data, 'VENDOR');
            } elseif ($loginUserType == "AE") {
                $data['asset_approval_status'] = 'approved';
                $data['approved_by_user_type'] = $loginUserType;
                $data['approved_by_user'] = $loginUserID;
                $data['approval_time'] = now();
                // Log::info('AE User, assigning approval details', ['approved_by_user' => $loginUserID, 'approved_by_user_type' => $loginUserType]);

                $this->updateAssetInventory($data, 'AE');
            } else {
                throw new \Exception("invalid access user add " . $this->uploadType);
                Log::info('try another user detail upload AssetInventoryUploadJob skipped: unmatched condition.', ['data' => $data]);
            }
            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            $this->handleException($e, $data, $authUser);
            Log::error('AssetInventoryUploadJob database error: ' . $e->getMessage());
        } catch (Exception $e) {
            DB::rollBack();
            $this->handleException($e, $data, $authUser);
            Log::error('AssetInventoryUploadJob error: ' . $e->getMessage());
        }
    }

    private function handleException($e, $prepareUserData, $authUser)
    {
        $errorMessage = $e->getMessage();
        ErrorHandlingService::handleErrorLog(
            $this->batchId,
            $errorMessage,
            $this->ip,
            $this->userType,
            $authUser->user_type,
            $prepareUserData,
            $authUser->user_id,
            $authUser->name
        );
        Log::error('UpdateUserRetailerGSTupdateJob Exception: ' . $errorMessage, $prepareUserData);
    }

    private function updateAssetInventory(array $data, string $userType)
    {
        // AsssetInventory::updateOrCreate(
        //     ['serial_number' => $data['serial_number']],
        //     $data
        // );
        AsssetInventory::create($data);
        Log::info("$userType AssetInventoryUploadJob processed.", ['data' => $data]);
    }

}
