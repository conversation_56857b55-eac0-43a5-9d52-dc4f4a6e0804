<?php

namespace App\Jobs;

use App\Imports\UsersHierarchyImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Bus\Batchable;

class ImportHierarchyFileJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,Batchable;

    public $timeout = 120;
    // public $failOnTimeout = true;
    public $tries=1;
    protected $filePath;

    public function __construct(string $filePath)
    {
        $this->filePath = $filePath;
    }

    public function handle()
    {
        try {
            $this->importUsersHierarchy();
        } catch (\Throwable $th) {
            $this->handleImportFailure($th);
        }
    }

    protected function importUsersHierarchy()
    {
        // Excel::import(new UsersHierarchyImport, $this->filePath);
        Log::info('Import started');
        Excel::import(new UsersHierarchyImport, $this->filePath, null, \Maatwebsite\Excel\Excel::XLSX);
        Log::info('Import ended');
    }

    protected function handleImportFailure($exception)
    {
        Log::error('Failed to import users using job: ' . $exception->getMessage());
        Log::error($exception);

        // $this->retryJob();
    }

    protected function retryJob()
    {
        $retryDelay = $this->calculateRetryDelay();
        $this->release($retryDelay);
    }

    protected function calculateRetryDelay()
    {
        return 60; // Example: 1 minute
    }
}
