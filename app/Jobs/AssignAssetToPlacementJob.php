<?php

namespace App\Jobs;

use App\Models\OutletAssetDetailsModel;
use App\Services\ErrorService\ErrorHandlingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;
class AssignAssetToPlacementJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $prepareUserData;
    protected $user;
    protected $ip;
    protected $authUser;
    protected $uploadType;
    protected $batchId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($prepareUserData, $user, $ip, $authUser, $uploadType, $batchId)
    {
        $this->prepareUserData = $prepareUserData;
        $this->user = $user;
        $this->ip = $ip;
        $this->authUser = $authUser;
        $this->uploadType = $uploadType;
        $this->batchId = $batchId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $amprocess = $this->prepareUserData;
        $authUser = $this->authUser;
        try {
            DB::beginTransaction();
            $assignedAssetData = [
                'asset_description' => $amprocess['eligible_chiller_type'] ?? null,
                'asset_barcode' => $amprocess['asset_barcode'] ?? '',
                'asset_number' => $amprocess['asset_serial_number'] ?? '',
                'outlet_code' => $amprocess['outlet_code'] ?? '',
                'placement_request_number' => $amprocess['request_number'] ?? '',
                'date_of_placement' => date("Y-m-d"),
                'asset_assigned_by' => $amprocess['cfa_code'] . "_001",
                'asset_assigned_time' => now(),
                'last_audit_time' => now(),
                'batchID' => $this->batchId,
                'uploadBy' => $authUser->user_id ?? '',
                'uploadByName' => $authUser->name ?? '',
                'uploadTime' => now(),
            ];
            OutletAssetDetailsModel::updateOrCreate(
                [
                    'asset_number' => $amprocess['asset_serial_number'],
                    'placement_request_number' => $amprocess['request_number']
                ],
                $assignedAssetData
            );
            Log::info('AssignAssetToPlacementJob  job processd for Asset Retailter Mapping with userID completed', ['request_number' => $amprocess['request_number']]);
            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            $this->handleException($e,$assignedAssetData, $authUser);
            Log::error('ProcessAssetPlacementRequestJob QueryException database error: ' . $e->getMessage());
        } catch (\Exception $e) {
            DB::rollBack();
            $this->handleException($e, $assignedAssetData, $authUser);
            Log::error('ProcessAssetPlacementRequestJob Exception: ' . $e->getMessage());
        }
    }

    private function handleException($e, $prepareUserData, $authUser)
    {
        $errorMessage = $e->getMessage();
        ErrorHandlingService::handleErrorLog(
            $this->batchId,
            $errorMessage,
            $this->ip,
            $this->uploadType,
            $authUser->user_type,
            $prepareUserData,
            $authUser->user_id,
            $authUser->name
        );
        Log::error('ProcessAssetPlacementRequestJob Exception: ' . $errorMessage, $prepareUserData);
    }
}
