<?php

namespace App\Http\Controllers\AdminReport;

use App\Http\Controllers\Controller;
use App\Models\AssetAudit;
use App\Models\AssetPlacementRequest;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\PlacementRequestService\PlacementRequestService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuditReportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

     public function getUserCodeList($type='', $column)
     {
         if($type){
             $user = User::where('user_type', $type)
             ->whereNotNull($column)
             ->where($column, '<>', '') // Exclude empty strings
             ->pluck($column)
             ->unique();
         }else{
             $user = User::whereNotNull($column)->where($column, '<>', '')->pluck($column)->unique();
         }

         return  $user;
     }
    public function index(Request $request)
    {

        $user = Auth::user();
        // Filter
        $filterData = PlacementRequestService::PlacementFilter($user);

        #filter value
        $so_name = $request->input('so_teritory');
        $asm_name = $request->input('asm_teritory');
        $db_code = $request->input('db_code');
        $distributor_name = $request->input('distributor_name');
        $customer_name = $request->input('customer_name');
        $customer_city = $request->input('customer_city');
        $customer_code = $request->input('customer_code');
        $route_name = $request->input('route_name');
        $request_no = $request->input('request_no');

        $fromDate = $request->input('FromDate');
        $toDate = $request->input('ToDate');

        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);



        $query = AssetAudit::with([
            'outlet.dsr.distributor.so.asm.rsm', // Eager load nested relationships
            'asset', // Eager load 'asset' relationship
            'asset.placement', // Eager load 'placement' relationship for 'asset'
        ]);

        if ($user->user_type == 'ASM' || $user->user_type == 'RSM' ||  $user->user_type == 'SO' || $user->user_type == 'AE') {
            $query->whereHas('outlet.retailer', function ($query) use ($user) {
                $key = $user->user_type == 'ASM' ? 'asm_id' : ($user->user_type == 'RSM' ? 'rsm_id' : ($user->user_type == 'SO' ? 'so_id' : ($user->user_type == 'AE' ? 'ae_id' : 'vendor_id')));
                $query->where($key, $user->user_id);
            });
        }
        #check login user is under hierarchy flow
        if ($user->user_type == 'VENDOR') {
            $query->whereHas('outlet.dsr.distributor', function ($query) use ($user) {
                $query->where('cfa_code', $user->user_id);
            });
        }

        if ($fromDate) {
            $query->whereDate('created_at', '>=', Carbon::parse($fromDate));
        }

        if ($toDate) {
            $query->whereDate('created_at', '<=', Carbon::parse($toDate));
        }

        if ($so_name) {
            $query->whereHas('outlet.dsr.distributor.so', function ($query) use ($so_name) {
                $query->where('teritory_code', $so_name);
            });
        }
        if ($asm_name) {
            $query->whereHas('outlet.dsr.distributor.so.asm', function ($query) use ($asm_name) {
                $query->where('area_code', $asm_name);
            });
        }
        if ($db_code || $distributor_name) {
            $query->whereHas('outlet.dsr.distributor', function ($query) use ($db_code, $distributor_name) {
                $query->where(function ($query) use ($db_code, $distributor_name) {
                    if ($db_code) {
                        $query->where('user_id', $db_code);
                    }
                    if ($distributor_name) {
                        $query->where('name', $distributor_name);
                    }
                });
            });
        }
        if ($customer_name || $customer_city || $customer_code) {
            $query->whereHas('outlet.retailer', function ($query) use ($customer_name, $customer_city, $customer_code) {
                $query->where(function ($query) use ($customer_city, $customer_name, $customer_code) {
                    if ($customer_city) {
                        $query->where('city', $customer_city);
                    }
                    if ($customer_name) {
                        $query->where('name', $customer_name);
                    }
                    if ($customer_code) {
                        $query->where('user_id', $customer_code);
                    }
                });
            });
        }
        if ($request_no) {
            $query->where('request_number', $request_no);
        }
        if ($route_name) {
            $query->whereHas('outlet', function ($query) use ($route_name) {
                $query->where('route_name', $route_name);
            });
        }

        $assetAuditList = $query->latest()->paginate($limit, ['*'], 'page', $currentPage);
        return view('adminReport.audit_report', ["assetAuditList" => $assetAuditList, "user" => $user,
        'so_territory_list'=>$filterData['so_territory_list']??'',
        'asm_territory_list'=>$filterData['asm_territory_list']??'',
        'customer_code_list' => $filterData['customer_code_list']??'',
        'city_list' => $filterData['city_list']??'',
        'db_name_list' => $filterData['db_name_list']??'',
        'db_code_list' => $filterData['db_code_list']??'',
        'placement_request_list' => $filterData['placement_request_list']??'',
        'placement_task_list' => $filterData['placement_task_list']??'',
        'customer_name_list' => $filterData['customer_name_list']??'',
        'route_list' => $filterData['route_list']??'',
    ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
