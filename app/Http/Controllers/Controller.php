<?php

namespace App\Http\Controllers;

use App\Library\Utils\ResponseUtil;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Auth;
use App\Models\Notifications;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * Send success response
     *
     * @param string $message
     * @param mixed $data
     * @param integer $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function response($data, string $message = '', $code = 200)
    {
        $res = ResponseUtil::makeResponse($message, $data);

        return response()->json($res, $code);
    }

    public function errorReponse($data, string $message = '', $status = false, $code = 200)
    {
        $res = ResponseUtil::response($message, $data,$status);

        return response()->json($res, $code);
    }
    /**
     * Send paginated response
     *
     * @param string $message
     * @param mixed $data
     * @param integer $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function paginate($data, string $message = '', $code = 200)
    {
        $data = $data->toArray();
        $data['success'] = true;
        $data['message'] = $message;

        return response()->json($data, 200);
    }

    /**
     * Create token response
     *
     * @param string $token
     * @return array
     */
    public function token($token)
    {
        return [
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => $this->guard('api')->factory()->getTTL()
        ];
    }

    /**
     * Send error response
     *
     * @param string $message
     * @param integer $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function error(string $message, $code = 400)
    {
        $res = ResponseUtil::makeError($message);

        return response()->json($res, $code);
    }

    // public function error(string $message, int $code = 400)
    // {
    //     $res = [
    //         'error' => [
    //             'status' => false,     // Always false for errors
    //             'code' => $code,       // HTTP status code
    //             'message' => $message, // A human-readable error message
    //             'details' => null      // Placeholder for additional error details, optional
    //         ]
    //     ];

    //     return response()->json($res, $code);
    // }
    /**
     * Get the guard to be used during authentication.
     *
     * @return \Illuminate\Contracts\Auth\Guard
     */
    public function guard()
    {
        return Auth::guard('api');
    }
}
