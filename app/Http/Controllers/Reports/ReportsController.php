<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ReportsController extends Controller
{
    public function placementReport()
    {
        return view('reports.placement_report');
    }
    public function replacementReport()
    {
        return view('reports.replacement_report');
    }
    public function pulloutReport()
    {
        return view('reports.pullout_report');
    }
    public function maintenanceReport()
    {
        return view('reports.maintenance_report');
    }
    public function auditReport()
    {
        return view('reports.audit_report');
    }
    public function masterMappingReport()
    {
        return view('reports.asset_master_mapping_report');
    }
    public function assetWiseReport()
    {
        return view('reports.asset_wise_inventory_report');
    }
    public function abTransferReport()
    {
        return view('reports.ab_transfer_report');
    }
}
