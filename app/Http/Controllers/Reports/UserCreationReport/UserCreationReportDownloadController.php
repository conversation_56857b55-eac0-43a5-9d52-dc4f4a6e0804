<?php

namespace App\Http\Controllers\Reports\UserCreationReport;

use App\Http\Controllers\Controller;
use App\Services\ExportReportService\ExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class UserCreationReportDownloadController extends Controller
{
    public function downloadReport(Request $request)
    {
        try {
            $user = Auth::user();

            // Get the query and headers from the service with applied filter
            $userCreation = ExportService::getUsers($request);
            $headerColumns = $userCreation['headerColumns'] ?? [];
            $dbQuery = $userCreation['dbQuery'] ?? null;
            $dbColumns = $userCreation['dbColumns'] ?? [];

            if (!$dbQuery) {
                return redirect()->back()->with('error', 'User Creation Data not available');
            }

            // Prepare CSV headers
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $request->user_type . '_ReportDownload_' . now()->format('Y_m_d_H_i_s') . '.csv"',
            ];

            return response()->stream(function () use ($dbQuery, $headerColumns, $dbColumns) {
                $handle = fopen('php://output', 'w');

                // Write header row
                fputcsv($handle, $headerColumns);

                // Chunk user data to avoid memory overload
                $dbQuery->chunk(1000, function ($users) use ($handle, $dbColumns) {
                    foreach ($users as $user) {
                        $row = [];
                        foreach ($dbColumns as $column) {
                            // Handle nested properties like 'asm.name'
                            $value = $user;
                            if (strpos($column, '.') !== false) {
                                $nestedProperties = explode('.', $column);
                                foreach ($nestedProperties as $nestedProperty) {
                                    $value = $value->{$nestedProperty} ?? '';
                                }
                            } else {
                                $value = $user->{$column} ?? '';
                            }
                            $row[] = $value;
                        }
                        fputcsv($handle, $row);
                    }
                });

                fclose($handle);
            }, 200, $headers);
        } catch (\Throwable $th) {
            Log::error('Failed to download User Creation Report: ' . $th->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again later.');
        }
    }
}
