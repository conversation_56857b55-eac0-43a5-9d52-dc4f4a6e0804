<?php

namespace App\Http\Controllers\VendorAllocationChallan;

use App\Http\Controllers\Controller;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\User;
use App\Services\Approval\ApprovalService;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use App\Services\PlacementRequestService\TaskAllocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PlacementController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function index()
    {
        return view('vendorAllocationChallan.placementAllocationChallan.placement_allocation');
    }

    public function edit($id, Request $request)
    {
        $requestNumber = decrypt($id);
        $user = Auth::user();
        $model = $request->request_type??'placement';
        $modelClassData = PlacementRequestService::getModelWithRequestNumber($model);
        $modelClass = $modelClassData['model'];
        $request_number = $modelClassData['request_number'];
        $placementRequest = $modelClass::where($request_number, $requestNumber)->with(['outlet', 'retailer', 'approvalHistory', 'distributor.cfa', 'so', 'asm', 'rsm', 'dsr'])
            ->first();
        // dd($placementRequest->outlet->retailer->pin_code);
        $placementRequest->asm_name = $placementRequest->asm->name;
        $placementRequest->request_number = $modelClassData['request_number'] ? $placementRequest->{$modelClassData['request_number']} : $placementRequest->request_number;
        $placementRequest->placement_request_type = ucfirst($model);
        $placementRequest->asm_mobile_number = $placementRequest->asm->mobile_number;
        $placementRequest->so_name = $placementRequest->so->name;
        $placementRequest->so_mobile_number = $placementRequest->so->mobile_number;
        $placementRequest->retailer_code = $placementRequest->retailer->user_id;
        $placementRequest->retailer_name = $placementRequest->retailer->name;
        $placementRequest->retailer_city = $placementRequest->retailer->city;
        $placementRequest->retailer_pin_code = $placementRequest->retailer->pin_code;;
        $placementRequest->retailer_address = $placementRequest->retailer->address_1 ?? '';
        $placementRequest->retailer_contact = $placementRequest->retailer->mobile_number;
        $placementRequest->distributor_code = $placementRequest->distributor->user_id;
        $placementRequest->distributor_name = $placementRequest->distributor->name;
        $placementRequest->request_module = $model;
        $placementRequest->eligible_chiller_type = $model=='maintenance'?$placementRequest->chiller_type:$placementRequest->eligible_chiller_type;
        $ae_approval = $placementRequest->approvalHistory->filter(function ($record) {
            return $record['user_role'] === 'AE';
        })->first();
        $aeApprovalDate = Carbon::parse($ae_approval->action_updated_time);
        $currentDate = Carbon::now();
        $dayAfterApproval = $aeApprovalDate->diffInDays($currentDate);
        $placementRequest->day_after_approval = $dayAfterApproval;
        $cfa_code = $placementRequest->cfa_code;
        $placementRequest->vendorExecutiveList =  User::where(["user_type" => "VE", "cfa_code" => $cfa_code])->pluck('user_id')->unique();
        return view('vendorAllocationChallan.placementAllocationChallan.placement_edit', [
            "placementRequest" => $placementRequest
        ]);
    }

    #Vendor Assign Task (organization) to VE  for deployment
    public function allocateTaskToVE(Request $request)
    {

        DB::beginTransaction(); // Start a database transaction
        try {
            $validator = Validator::make($request->all(), [
                'request_number' => 'required',
                'ExpectedDeploymentDate' => 'required|date', // Example validation rule for date
                'assigned_organization' => 'required',
            ]);

            if ($validator->fails()) {
                return Redirect::back()->withErrors($validator)->withInput();
            }
            $user = Auth::user();

            $requestNumber = $request->request_number;
            $ExpectedDeploymentDate = $request->ExpectedDeploymentDate;
            $assigned_organization = $request->assigned_organization;
            // Instantiate TaskStatusService
            $taskStatusService = new TaskStatusService();

            //update next  HeirarchyRole for assigning task
            $pending_from = $taskStatusService->getNextHeirarchyRole($user->user_type);

            $asset_assigned_status = "Approved";
            $request_module = $request->request_module;
            $updateData = [
                'asset_assigned_status' => $asset_assigned_status,
                'approved_by_user_role' => $user->user_type,
                'pending_from' => $pending_from,
                // 'deployment_date' =>  date('Y-m-d', strtotime($ExpectedDeploymentDate)),
                'assigned_organization' => $assigned_organization,
            ];
            $approval = ApprovalService::getApprovalTime($request_module);
            $requestNumberKey = $approval['approved'];
            $deploymentKey = $approval['expected_date'];
            // $requestNumberKey=$request_module=='pullout'?'pullout_approved_time':'placement_approved_time';
            // $deploymentKey=$request_module=='pullout'?'expected_pullout_date':'expected_deployment_date';

            $updateData[$requestNumberKey]=now();
            $updateData[$deploymentKey]=date('Y-m-d', strtotime($ExpectedDeploymentDate));
            $modelClassData = PlacementRequestService::getModelWithRequestNumber($request_module);
            $modelClass = $modelClassData['model'];
            $request_number = $modelClassData['request_number'];
            $updateStatus = $modelClass::where($request_number, $requestNumber)->update($updateData);
            if (!$updateStatus) {
                Log::error('Error : Task not allocated to VE ');
                return Redirect::back()->with('error','Failed to allocate Task To VE');
            }

            DB::commit(); // Commit the transaction
            #Updater approvalhistory
            $taskStatusService->approvalHistoryUpdate($request->request_number, $asset_assigned_status);
            $route = ApprovalService::redirectUrl($request_module);

            return redirect()->route($route)->with('success', 'Task Allocated successfully');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : allocateTaskToVE at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return Redirect::back()->with('error', 'Failed to allocate Task To VE');
        }
    }

    public function submitAllocateMultipleTaskToVE(Request $request)
    {

        $taskAllocationService = new TaskAllocationService();
        $results = [];
        foreach ($request->request_number as $requestNumber) {
            $requestData = [
                'request_number' => $requestNumber,
                'ExpectedDeploymentDate' => $request->ExpectedDeploymentDate,
                'assigned_organization' => $request->assigned_organization,
                'request_type' => $request->request_type,
            ];

            $result = $taskAllocationService->allocateTaskToVE($requestData);

            if ($result['success']) {
                $results[] = ['success' => true, 'message' => $result['message']];
            } else {
                $results[] = ['success' => false, 'error' => $result['error']];
            }
        }

        $successCount = collect($results)->where('success', true)->count();

        $redirectURL=[
            'pullout'=>'approval_center.pullout',
            'placement'=>'approval_center.placement',
            'maintenance'=>'approval_center.maintenance',
            'replacement'=>'approval_center.replacement'
        ];
        if ($successCount == count($results)) {
            $redirectRoute = $redirectURL[$request->request_type]??'approval_center.placement';
            return redirect()->route($redirectRoute)->with('success', 'All tasks allocated successfully');
        } else {
            return Redirect::back()->withErrors(['errors' => collect($results)->pluck('error')->implode(', ')]);
        }
    }


    public function assignedVendorExecutiveAllocation(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$request->aprID) {
                return Redirect::back()->withErrors(['error' => 'INVALID Request']);
            }
            $requestNumberArray = explode('#$', $request->aprID);
            $vendorExecutiveList =  User::where(["user_type" => "VE", "cfa_code" => $user->user_id])->pluck('user_id')->unique();
            return view('vendorAllocationChallan.placementAllocationChallan.assignedVendorExecutiveFordMultipleRequest', [
                "vendorExecutiveList" => $vendorExecutiveList,
                "selectdRequestNumber" => $requestNumberArray,
                "request_type" => $request->request_type??'placemment'
            ]);
        } catch (\Exception $exception) {
            Log::error('Error : assignedVendorExecutiveAallocation at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return Redirect::back()->withErrors(['error' => 'Failed to allocate Task To VE']);
        }
    }
}
