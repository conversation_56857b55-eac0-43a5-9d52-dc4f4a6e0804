<?php

namespace App\Http\Controllers\InventoryManagement;

use App\Exports\Inventory\InwardOutWardReport;
use App\Http\Controllers\Controller;
use App\Library\Utils\DateFormate;
use App\Models\AsssetInventory;
use App\Services\AsssetInventory\AsssetInventoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
class InwordOutwordInventoryController extends Controller
{

    protected $assetInventoryService;

    public function __construct(AsssetInventoryService $assetInventoryService)
    {
        $this->assetInventoryService = $assetInventoryService;
    }

    public function index(Request $request)
    {
        $filteredData = $this->assetInventoryService->getInwardOutwardInventory($request);
        $inwardOutwardInventory = $filteredData['inwardOutwardInventory'];
        $grandTotals = $filteredData['grandTotals'];
        return view('inventoryManagement.inwardAndOutwardRerort.index', compact('inwardOutwardInventory', 'grandTotals'));
    }


    public function InwardOutwardRepoertDownload(Request $request){
        $fileName='InwardOutwardReport_';
        return Excel::download(new InwardOutWardReport($request), 'Upload_data_RSM_' . now() . $fileName . '.xlsx');
    }
    public function edit($id)
    {
        return view('inventoryManagement.inwardAndOutwardRerort.edit');
    }
}
