<?php

namespace App\Http\Controllers\InventoryManagement;

use App\Http\Controllers\Controller;
use App\Models\AsssetInventory;
use App\Services\AsssetInventory\AsssetInventoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
class WorkingInventoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        $user = Auth::user();
        $limit = $request->query('limit', 10);
        $act = $request->query('act') ? decrypt($request->query('act')) : '';
        $currentPage = $request->query('page', 1);
        $approval_status = strtolower($request->query('status', 'all'));
        $assetChillerType = AsssetInventory::distinct()->pluck('asset_type')->toArray();
        $from = $request->query('from_date') ? AsssetInventoryService::convertDateFormatToDbFormat($request->query('from_date')) : '';
        $to = $request->query('to_date') ? AsssetInventoryService::convertDateFormatToDbFormat($request->query('to_date')) : '';
        $inventoryList = AsssetInventoryService::getWorkingInventoryList($user, $limit, $act, $currentPage, $approval_status='',$from,$to,true);
        return view('inventoryManagement.workingInventory.index',compact('inventoryList', 'user', 'assetChillerType'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return view('inventoryManagement.workingInventory.edit');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
