<?php

namespace App\Http\Controllers\API\Replacement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Replacement\ReplacementCreateRequest;
use App\Http\Requests\Replacement\ReplacementRequestDetailsRequest;
use App\Models\AssetReplacementRequest;
use App\Models\InvoiceChallan;
use App\Services\API\Replacement\ReplacementRequestAPIService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ReplacementRequestController extends Controller
{
    public function createReplacementRequest(ReplacementCreateRequest $request): \Illuminate\Http\JsonResponse
    {
        try {
            Log::info('RequestData : createReplacementRequest request data', $request->all());

            $validatedData = $request->validated();
            $validatedData = PlacementRequestService::getHierarchy($validatedData);
            $replacement_request_number = PlacementRequestService::generateRequestNumber(InvoiceChallan::PREFIX_ASSET_REPLACEMENT_REQUEST);
            $validatedData['pending_from'] = 'ASM';
            $validatedData['additional_equipment'] =  json_encode($validatedData['additional_equipment'] ?? []);
            $validatedData['request_number'] = $replacement_request_number;
            $validatedData['is_bolt_town'] = (boolean)($request->is_bolt_town ?? false);
            Log::info('createReplacementRequest API SO APP stored data', $validatedData);
            $replacementRequest = AssetReplacementRequest::create($validatedData);
            if (!$replacementRequest) {
                return $this->error('Failed to create asset replacement request, please try again', 400);
            }
            return $this->response(["ReplacementRequestNumber" => $replacement_request_number], 'Asset Replacement Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : createReplacementRequest data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to create asset createReplacementRequest request', 400);
        }
    }

    public function GetAssetReplacementRequestList(Request $request)
    {
        try {
            Log::info("GetAssetReplacementRequestList", [$request->all()]);

            $userId = $request->input('user_id');
            $type = $request->input('type');
            $status = $request->input('status');
            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 10);
            $fromDate = $request->input('from_date');
            $toDate = $request->input('to_date');

            $query = ReplacementRequestAPIService::buildQuery($userId, $type, $status, $fromDate, $toDate);

            $replacementRequests = $query->orderBy('created_at', 'DESC')
                ->offset($offset)
                ->limit($limit)
                ->get();

            $data = ReplacementRequestAPIService::formatReplacementRequests($replacementRequests);
            return $this->response($data, 'Asset Replacement Request list.');
        } catch (\Throwable $th) {
            Log::error("Error in GetAssetReplacementRequestList", [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString()
            ]);
            return $this->error("Something went wrong in GetAssetReplacementRequestList");
        }
    }

    public function GetAssetReplacementRequestDetails(ReplacementRequestDetailsRequest $request){
        try {
            Log::info("GetAssetReplacementRequestDetails", [$request->all()]);
            $validatedData = $request->validated();
            $replaceewntRequestListDetails = ReplacementRequestAPIService::GetAssetReplacementRequestDetails($validatedData);
            return $this->response($replaceewntRequestListDetails, 'Asset replacement Request list.');
        } catch (\Throwable $th) {
            Log::error("Error in GetAssetReplacementRequestDetails", [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString()
            ]);
            return $this->error("Something went wrong in GetAssetReplacementRequestDetails");
        }
    }
}
