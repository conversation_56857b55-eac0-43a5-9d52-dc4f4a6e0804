<?php

namespace App\Http\Controllers\API\IVY;

use App\Constants\IVYConstant;
use App\Http\Controllers\Controller;
use App\Http\Requests\IVY\AssetRetailerMasterRequest;
use App\Http\Requests\IVY\FailedIvyAssetRecordListRequest;
use App\Models\User;
use App\Models\RetailerOutlet;
use App\Services\IvyAssetMaster\IvyAssetMasterService;
use App\Services\UploadHistoryDetails\UploadHistoryService;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class IVYassetRetailerMaster extends Controller
{

    public function StoredIvyAssetRetailerMaster(Request $request)
    // public function StoredIvyAssetRetailerMaster(Request $request)
    {
        Log::info("StoredIvyAssetRetailerMaster Asset master Retailer API", [$request->all()]);
        $requestIP = $request->ip();
        try {
            $validatedData = $request->all();
            $requestCount = count($validatedData);
            $batchID = uniqid('ivyBatch_');
            $userId = IVYConstant::IVYUSERID;
            $uploadByName = IVYConstant::IVYUSERNAME;
            $userRole = IVYConstant::IVYUSERROLE;
            $authUser = IvyAssetMasterService::authUser($userId, $uploadByName);
            UploadHistoryService::ManageUploadHistory($batchID, $requestCount, 0, 0, $request, $userId, $uploadByName, $requestIP, $userRole, IVYConstant::IVYASSETRETAILERMASTERAPI);
            $assetRetailerMaster = IvyAssetMasterService::assetRetailerMasterStored($validatedData, $requestIP, $batchID);
            return $this->response(['batchID' => $assetRetailerMaster], 'Asset retailer master data Processed successfully', 200);
        } catch (\Exception $exception) {
            Log::error('Error : StoredIvyAssetRetailerMaster asset retailer master data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Oops something went wrong,Please try again', 400);
        }
    }


    public function FailedIvyAssetRecordList(FailedIvyAssetRecordListRequest $request)
    {
        try {
            Log::info("FailedIvyAssetRecordList Asset master Retailer API", [$request->all()]);
            $failedData = UploadHistoryService::getIVYLatestHistorybyBatchID($request->batchID);
            if (empty($failedData)) {
                return $this->error('In this batch ID not found any failed data', 400);
            }
            $res = [
                'BatchID' => $failedData->batchID,
                'RequestIP' => $failedData->requestIP,
                'UploadDateTime' => $failedData->created_at,
                'failedData' => $failedData->assetLog,
            ];
            return $this->response($res, 'Asset Master  Failed data retrieved successfully', 200);
        } catch (\Exception $exception) {
            Log::error('Error : FailedIvyAssetRecordList asset retailer master data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Oops something went wrong,Please try again', 400);
        }
    }
}
