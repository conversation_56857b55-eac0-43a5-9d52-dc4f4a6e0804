<?php

namespace App\Http\Controllers\API\Outlet;

use App\Http\Controllers\Controller;
use App\Http\Requests\Outlet\GetOutletListRequest;
use App\Http\Requests\Outlet\OutletDetailsRequest;
use App\Http\Requests\Outlet\UpdateOutletDetailsRequest;
use App\Models\AssetOutletAuditVisitAttandance;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\RetailerOutlet;
use Illuminate\Support\Facades\Log;
use App\Traits\UploadImage;

class OutlletHierarchyAPIController extends Controller
{
    use UploadImage;

    public function __construct()
    {
        $this->middleware('validateUserId');
    }

    public function getOutletList(GetOutletListRequest $request)
    {
        try {
            $UserId = $request->UserId;
            $assignedStatus = $request->input('AssignedStatus', 'All');
            $status=$assignedStatus == 'Assigned'?'Assigned':($assignedStatus == 'Unassigned'?'NotAssigned':'All');
            $offset = $request->input('Offset', 0);
            $limit = $request->input('Limit', 10);
            $keyword = $request->input('Keyword');

            // $mappedUsers = User::where('so_id', $soId)->with('outlets')->get();
            if ($request->Type === 'SO') {
                $whereClause = ['so_id' => $UserId, 'user_type' => "RETAILER"];
            } elseif ($request->Type === 'DSR') {
                $whereClause = ['user_type' => "RETAILER"];
            }
            $mappedUsers = User::where($whereClause)
                ->when($request->Type === 'SO', function ($query) use ($UserId) {
                    return $query->where('so_id', $UserId);
                })
                ->when($request->Type === 'DSR', function ($query) use ($UserId) {
                    return $query->whereHas('outlets', function ($query) use ($UserId) {
                        $query->where('salesman_code', $UserId);
                    });
                })
                ->when(!empty($status) && $status != 'All', function ($query) use ($status) {
                    return $query->whereHas('outlets', function ($query) use ($status) {
                        $query->where('asset_assigned_status', $status);
                    });
                })
                ->when(!empty($keyword), function ($query) use ($keyword) {
                    $query->where(function ($query) use ($keyword) {
                        $query->where('name', 'like', "%$keyword%")
                            ->orWhere('user_id', 'like', "%$keyword%");
                    });
                })
                ->with(['outlets', 'assets' => function ($query) {
                    $query->select('asset_description', 'asset_barcode', 'outlet_code'); // Adjust 'user_id' according to your foreign key
                }])
                ->latest()->offset($offset)->limit($limit)->get();
            $outlets = [];
            foreach ($mappedUsers as $user) {
                $outlet = $user->outlets->first();
                if ($outlet) {
                    $outlets[] = $this->formatOutletData($outlet->toArray(), $user);
                }
            }

            return $this->response($outlets, 'Outlet data retrieved successfully');
        } catch (\Exception $exception) {
            Log::error('Error Outlet List data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to retrieve outlet list', 500);
        }
    }

    public function getOutletDetails(OutletDetailsRequest $request)
    {
        try {
            $retailerData = RetailerOutlet::with('user')->find($request->OutletId);
            if (!$retailerData) {
                return $this->error('outlet details not found');
            }
            $outletUser = $retailerData->user;
            //            $user = User::where('user_id', $retailerData->salesman_code)->first();
            //            $dsrName = optional($user)->name;

            $attandace = AssetOutletAuditVisitAttandance::where('outlet_id', $retailerData->id)->latest()->first();
            $outletDetailsData = [
                'OutletId' =>  $retailerData->id,
                'OutletCode' => $retailerData->user->user_id ?? null,
                'OutletName' => $outletUser->name ?? '',
                'OutletAddress' => $outletUser->address_1??'',
                'Latitude' => $retailerData->lat,
                'Longitude' => $retailerData->long,
                'DSRName' => $retailerData->salesman_name,
                'DSRCode' => $retailerData->salesman_code,
                'RouteName' => $retailerData->route_name,
                'CategoryName' => $retailerData->category_name,
                'Classification' =>  $retailerData->class_name ?? '',
                'OutletImageUrl' => $retailerData->outlet_image ? asset($retailerData->outlet_image) : null,
                'DBName' =>  $retailerData->db_name,
                'ContactNumber' =>  $retailerData->user->mobile_number ?? null,
                'City' =>  $retailerData->user->city ?? null,
                'LastVisitDateTime' => $attandace->checkin_time ?? null,
            ];
            return $this->response($outletDetailsData, 'Outlet details retrieved successfully');
        } catch (\Exception $exception) {
            Log::error('ERROR: getOutletDetails OutletDetails  data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to retrieve outlet list', 500);
        }
    }


    public function updateOutletDetails(UpdateOutletDetailsRequest $request)
    {
        try {

            $retailerOutlet = RetailerOutlet::where('id', $request->OutletId)->first();

            $retailerOutletUpdateData = ["db_name" => $request->DBName];

            #need atleast one field to update
            if (empty($request->DBName) && empty($request->City) && empty($request->ContactNumber) && !$request->hasFile('Image')) {
                return $this->error('At least one field to update is required.');
            }

            #if imaage not empty
            if ($request->hasFile('Image')) {
                $image = $request->file('Image');
                $imageName = $this->saveImage($image, 'images/outlets/');
                $imagePath = 'images/outlets/' . $imageName;
                $retailerOutletUpdateData['outlet_image'] = $imagePath;
            }

            // Update the RetailerOutlet record
            $retailerOutlet->update($retailerOutletUpdateData);

            // Access the User model related to this RetailerOutlet
            $user = $retailerOutlet->user;

            // Update the city field in the User table
            if ($user) {
                $updateData = [];
                if ($request->City) {
                    $updateData['city'] = $request->City;
                }
                if ($request->DBName) {
                    $updateData['db_name'] = $request->DBName;
                }
                if ($request->ContactNumber) {
                    $updateData['mobile_number'] = $request->ContactNumber;
                }
                $user->update($updateData);
            }
            return $this->response([], 'Outlet details Updated successfully');
        } catch (\Exception $exception) {
            Log::error('ERROR: getOutletDetails OutletDetails  data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('outlet details failed to update', 500);
        }
    }





    private function formatOutletData($outlet, $user)
    {
        $attandace = AssetOutletAuditVisitAttandance::where('outlet_id', $outlet['id'])->latest()->first();


        $assetDescriptions = array_column($user->assets->flatten()->toArray(), 'asset_description');
        $assetBarcodes = array_column($user->assets->flatten()->toArray(), 'asset_barcode');
        return [
            'OutletId' => $outlet['id'],
            'OutletCode' => $user['user_id'],
            'OutletName' => $user['name'],
            'OutletAddress' => $user['address_1'] ?? '',
            'Latitude' => $outlet['lat'],
            'Longitude' => $outlet['long'],
            'DSRName' => $outlet['salesman_name'],
            'DSRCode' => $outlet['salesman_code'],
            'RouteName' => $outlet['route_name'],
            'CategoryName' => $outlet['category_name'],
            // 'AssetAssignedStatus' => $outlet['assigned'] ?? "NotAssigned",
            'AssetAssignedStatus' => count($assetBarcodes) > 0 ? "Assigned" : "NotAssigned",
            'AssetCode' =>  $assetBarcodes ?? [],
            'ChillerType' => $assetDescriptions ?? [],
            'LastVisitDateTime' => $attandace->checkin_time ?? null,
            'OutletImageUrl' => $outlet['outlet_image'] ? asset($outlet['outlet_image']) : null,
            'DBName' =>  $outlet['db_name'],
            'Classification' =>  $outlet['class_name'],
            'AE_Code' => $user['ae_id'],
            'RSM_Code' => $user['rsm_id'],
            'ASM_Code' => $user['asm_id'],
            'So_Code' => $user['so_id'],
            'DB_Code' => $user['distributor_id'],
            'CFA_Code' => null,
        ];
    }
}
