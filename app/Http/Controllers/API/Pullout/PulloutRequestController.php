<?php

namespace App\Http\Controllers\API\Pullout;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\RetailerOutlet;
use App\Models\AssetPulloutRequest;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\AssetPlacementRequest;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\Pullout\PulloutRequest;
use App\Http\Requests\Pullout\PulloutRequestList;
use App\Http\Requests\Pullout\PulloutRequestDetails;
use App\Http\Requests\Pullout\PulloutAssetDetailsRequest;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;

class PulloutRequestController extends Controller
{
    protected $taskStatusService;
    public function __construct(TaskStatusService $taskStatusService)
    {
        $this->taskStatusService = $taskStatusService;
    }
    public function pulloutCreate(PulloutRequest $request)
    {
        //    return $request->all();
        Log::info("pulloutCreate", [$request->all()]);
        try {
            // Generate the asset placement request number
            $assetRequestNumber = PlacementRequestService::generateRequestNumber('PR');

            // Add the request number to the mapped data

            $validated = $request->validated();
           $serialNumberRequest = AssetPulloutRequest::where('asset_serial_number', $validated["AssetNumber"])
                ->orderBy('created_at', 'desc')
                ->first();
            if ($serialNumberRequest) {
                if ($serialNumberRequest->asset_assigned_status !== 'Rejected') {
                    return $this->error("Request already submitted for this Asset Serial Number",409);
                }
            }
            #handle Hierarchy
            $heirarchy = PlacementRequestService::outletHeirarchy($request->input("OutletId"));
            $heirarchyUser = $heirarchy->user ?? '';
            $distributor = $heirarchy->user->distributor ?? '';
            $data = [
                "user_id" => $validated["UserId"],
                "type" => $validated["Type"],
                "outlet_id" => $validated["OutletId"],
                "chiller_type" => $validated["ChillerType"],
                "asset_serial_number" => $validated["AssetNumber"],
                "asset_barcode" => $validated["AssetBarCode"],
                "ScannedBarcode" => $validated["ScannedBarcode"],
                "current_vpo" => $validated["CurrentVPO"],
                "customer_name" => $validated["CustomerName"],
                "customer_code" => $validated["CustomerCode"],
                "outlet_code" => $validated["CustomerCode"],
                "contact_person" =>  $request->input("ContactPerson"),
                "contact_number" =>  $request->input("ContactNumber"),
                "pullout_asset_type" => $validated["PulloutAssetType"],
                "consent_status" => $validated["ConsentStatus"],
                "signature" => $validated["Signature"],
                "email" => $validated["Email"],
                "mobile_number" => $validated["MobileNo"],
                "customer_address" => $validated["CustomerAddress"],
                "pinCode" => $validated["PinCode"],
                "pullout_reason" => $validated["PulloutReason"],
                "customer_location" => $validated["CustomerLocation"],
                "current_location" => $validated["CurrentLocation"]??'',
                "distance" => $validated["Distance"],
                "latitude" => $validated["Latitude"],
                "longitude" => $validated["Longitude"],
                "asset_assigned_status" => $validated["Status"],
                "remarks" =>  $request->input("Remarks"),
                "created_at" => now(),
                "updated_at" => now(),
                'ae_code' => $heirarchyUser->ae_id ?? '',
                'rsm_code' => $heirarchyUser->rsm_id ?? '',
                'asm_code' => $heirarchyUser->asm_id ?? '',
                'so_code' => $heirarchyUser->so_id ?? '',
                'db_code' => $heirarchyUser->distributor_id ?? '',
                'dsr_code' => $heirarchy->salesman_code ?? '',
                'cfa_code' => $distributor->cfa_code ?? '',
                'pending_from' => 'ASM',
                'task_status' => 'Pending',
            ];
            $data['pullout_request_number'] = $assetRequestNumber;
            Log::info('pulloutCreate stored data', $data);
            $response = AssetPulloutRequest::insert($data);
            if (!$response) {
                return $this->response([], 'Submit Failed.');
            }
            return $this->response(['request_number' => $assetRequestNumber], 'Asset Placement Requst submitted successfully.');
        } catch (\Throwable $th) {
            Log::error("Error in pulloutCreate", ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString()]);
            return $this->error("pulloutCreate Request Submit Failed.");
        }
    }

    public function pulloutList(PulloutRequestList $request)
    {
        try {
            // return $request->all();
            Log::info("pulloutList", [$request->all()]);
            $validated = $request->validated();

            $user_id = $request->UserId;
            $type = $request->Type;


            $asset_assigned_status = $request->input("Status");
            // Set default values for Offset and Limit if not provided
            $offset = $request->input('Offset', 0) ?? 0;
            $limit = $request->input('Limit', 10) ?? 10;

            $FromDate = $request->input("FromDate") ? Carbon::createFromFormat("Y-m-d", $request->input("FromDate")) : '';
            $ToDate = $request->input("ToDate") ? Carbon::createFromFormat("Y-m-d", $request->input("ToDate")) : '';



            $query = AssetPulloutRequest::where('so_code', $user_id)
                ->with(['retaileroutlets' => function ($query) {
                    $query->select('id', 'route_name', 'salesman_name');
                }])
                ->Where('type', $type)

                ->when($asset_assigned_status !== 'All' && $asset_assigned_status !== '', function ($query) use ($asset_assigned_status) {
                    return $query->where('asset_assigned_status', $asset_assigned_status);
                })->when($FromDate || $ToDate, function ($query) use ($FromDate, $ToDate) {
                    $query->where(function ($subquery) use ($FromDate, $ToDate) {
                        if ($FromDate) {
                            $subquery->whereDate('created_at', '>=', $FromDate);
                        }
                        if ($ToDate) {
                            $subquery->whereDate('created_at', '<=', $ToDate);
                        }
                    });
                });


            if ($offset > 0) {
                $query->skip($offset);
            }
            // Apply limit
            $posts = $query->orderBy('created_at', 'DESC')->take($limit)->get();
            $datas = [];
            if (!empty($posts)) {
                foreach ($posts as $data) {
                    $approvalData = $this->taskStatusService->placemenStatus($data,'pullout');
                    $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
                    $message = !empty($approvalData['message']) ? $approvalData['message'] : null;
                    $datas[] = [
                        "RequestId" => $data->id ?? '',
                        "RequestNumber" => $data->pullout_request_number ?? '',
                        "AssetNumber" => $data->asset_serial_number ?? '',
                        "CustomerName" => $data->customer_name ?? '',
                        "PulloutReason" => $data->pullout_reason ?? '',
                        "CustomerCode" => $data->customer_code ?? '',
                        "CustomerAddress" => $data->customer_address ?? '',
                        "DSRName" => $data->retaileroutlets->salesman_name ?? '',
                        "RouteName" => $data->retaileroutlets->route_name ?? '',
                        "ChillerType" => $data->chiller_type ?? '',
                        "DateTime" => $data->created_at->format("Y-m-d H:i:s"),
//                        "Status" => $data->asset_assigned_status ?? '',
                        "Status" =>$approvalStatus ?? '',
                        'ApprovalMessage' => $message ?? null,
                        'PendingAt' => $data->pending_from
                    ];
                }
            }
            return $this->response($datas, 'Pullout request list.');
        } catch (\Throwable $th) {
            Log::error("Error in pulloutList", ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString()]);

            return $this->error("No data found!");
        }
    }

    public function pulloutDetails(PulloutRequestDetails $request)
    {
        try {
            $validated = $request->validated();
            // return $request->all();
            Log::info("pulloutDetails", [$request->all()]);

            $user_id = $request->UserId;
            $type = $request->validated("Type");
            $RequestId = $request->validated("RequestId");


            $data = AssetPulloutRequest::where(['so_code' => $user_id, 'type' => $type, 'pullout_request_number' => $RequestId])
                ->with(['retaileroutlets' => function ($query) {
                    $query->select('id', 'route_name', 'salesman_name');
                }])->first();
            $approvalData = $this->taskStatusService->placemenStatus($data,'pullout');
            $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
            $message = !empty($approvalData['message']) ? $approvalData['message'] : null;

            $array = [
                "RequestId" => $data->id ?? '',
                "RequestNumber" => $data->pullout_request_number ?? '',
                "Status" => $data->asset_assigned_status ?? '',
                "DateTime" => $data->created_at->format("Y-m-d H:i:s") ?? '',
                "AssetId" => $data->asset_id ?? '',
                "AssetNumber" => $data->asset_serial_number ?? '',
                "AssetBarCode" => $data->asset_barcode ?? '',
                "ScannedBarcode" => $data->ScannedBarcode ?? '',
                "CurrentVPO" => $data->current_vpo ?? '',
                "PulloutAssetType" => $data->pullout_asset_type ?? '',
                "CustomerName" => $data->customer_name ?? '',
                "DSRName" => $data->retaileroutlets->salesman_name ?? '',
                "RouteName" => $data->retaileroutlets->route_name ?? '',
                "ChillerType" => $data->chiller_type ?? '',
                "ContactPerson" => $data->contact_person ?? '',
                "ContactNumber" => $data->contact_number ?? '',
                "Signature" => $data->signature ? asset($data->signature) : '',
                "EmailId" => $data->email ?? '',
                "MobileNumber" => $data->mobile_number ?? '',
                "CustomerAddress" => $data->customer_address ?? '',
                "Pincode" => $data->pincode ?? '',
                "PulloutReasons" => $data->pullout_reason ?? '',
                "CustomerLocation" => $data->customer_location ?? '',
                "CurrentLocation" => $data->current_location ?? '',
                "Distance" => $data->distance ?? '',
                "Remarks" => $data->remarks ?? '',
                'Status' => $approvalStatus ?? null,
                'ApprovalMessage' => $message ?? null,
                'PendingAt' => $data->pending_from
            ];

            return $this->response($array, 'Pullout request details.');
        } catch (\Throwable $th) {
            Log::error("Error in pulloutList", ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString()]);
            return $this->error("No data found!");
        }
    }

    public function pulloutAssetList(PulloutRequestList $request)
    {
        try {
            Log::info("pulloutAssetList", [$request->all()]);

            $validated = $request->validated();
            $so_id = $validated["UserId"];
            $type = $validated["Type"];
            $Keyword = $request->Keyword;

            // Set default values for Offset and Limit if not provided
            $offset = $request->input('Offset', 0);
            $limit = $request->input('Limit', 10);

            $query = AssetPlacementRequest::where(function ($query) use ($so_id, $type) {
                $query->where('so_code', $so_id)
                    ->where('type', $type);
            })
                ->when($Keyword, function ($query) use ($Keyword) {
                    $query->where(function ($q) use ($Keyword) {
                        $q->orWhere('outlet_code', 'like', "%{$Keyword}%")
                            ->orWhere('asset_serial_number', 'like', "%{$Keyword}%")
                            ->orWhereHas('userOne', function ($q) use ($Keyword) {
                                $q->where('name', 'like', "%{$Keyword}%");
                            });
                    });
                })
                ->with([
                    'retaileroutlets:id,route_name,salesman_name,lat,long',
                    'placementAssets:id,placement_request_number,asset_description',
                    'userOne:id,user_id,name,mobile_number'
                ])
                ->whereNotNull('asset_serial_number')
                ->orderBy('created_at', 'DESC');

            // Apply offset if necessary
            if ($offset > 0) {
                $query->skip($offset);
            }

            // Apply limit and get the results
            $posts = $query->take($limit)->get();

            $datas = [];

            foreach ($posts as $data) {
                $datas[] = [
                    "OutletId" => $data->outlet_id ?? '',
                    "RequestNo" => $data->request_number ?? '',
                    "AssetNumber" => $data->asset_serial_number ?? '',
                    "AssetName" => $data->placementAssets->asset_description ?? '',
                    "CustomerName" => $data->userOne->name ?? '',
                    "CustomerCode" => $data->outlet_code ?? '',
                    "DSRName" => $data->retaileroutlets->salesman_name ?? '',
                    "RouteName" => $data->retaileroutlets->route_name ?? '',
                    "ChillerType" => $data->eligible_chiller_type ?? '',
                    "LastOrderDate" => $data->created_at->format("Y-m-d") ?? '',
                    "LastScannedDate" => $data->created_at->format("Y-m-d") ?? '',
                    "AssetVPOThreshold" => $data->AssetVPOThreshold ?? '', // blank
                    "VPOStatus" => $data->VPOStatus ?? '', // blank
                    "Latitude" => $data->retaileroutlets->lat ?? '',
                    "Longitude" => $data->retaileroutlets->long ?? '',
                    "CustomerAddress" => $data->customer_address ?? '',
                ];
            }
            return $this->response($datas, 'Pullout request list.');
        } catch (\Throwable $th) {
            Log::error("Error in pulloutList", ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString()]);
            return $this->error("No data found!");
        }
    }


    public function pulloutAssetDetails(PulloutAssetDetailsRequest $request)
    {
        try {
            //  return $request->all();
            Log::info("pulloutDetails", [$request->all()]);
            $validated = $request->validated();
            $user_id = $request->UserId;
            $type = $request->validated("Type");
            $AssetSerialNumber = $request->input("AssetSerialNumber"); //optional
            $OutletCode = $request->validated("OutletCode");

            // Set default values for Offset and Limit if not provided
            $offset = $request->input('Offset', 0) ?? 0;
            $limit = $request->input('Limit', 10) ?? 10;

            $query = AssetPlacementRequest::where(function ($query) use ($user_id, $type, $AssetSerialNumber, $OutletCode) {

                $query->where(['so_code' => $user_id, 'type' => $type, 'outlet_code' => $OutletCode]);
                $query->whereNotNull('asset_serial_number');
            })->with(['retaileroutlets' => function ($query) {
                $query->select('id', 'route_name', 'contact_name', 'lat', 'long');
            }])->when($AssetSerialNumber, function ($query) use ($AssetSerialNumber) {
                $query->orWhere('asset_serial_number', $AssetSerialNumber);
            })
                ->with([
                    'placementAssets' => function ($query) {
                        $query->select('id', 'placement_request_number', 'asset_description',);
                    }, 'inventoryOne' => function ($query) {
                        $query->select('id', 'vpo_target', 'serial_number');
                    }, 'userOne' => function ($query) {
                        $query->select('id', 'user_id', 'name', 'mobile_number');
                    }
                ]);
            if ($offset > 0) {
                $query->skip($offset);
            }
            // Apply limit
            $datas = $query->take($limit)->get();

            //[{AssetSerialNumber, AssetNumber, AssetName, CurrentVPO, ChillerType, AssetNumber, AssetBarcode, CustomerName, CustomerCode, ContactPerson, ContactNumber, Latitude, Longitude, CustomerAddress }]


            $array = [];

            foreach ($datas as $data) {
                $array[] = [
                    "AssetSerialNumber" => $data->asset_serial_number  ?? '',
                    "AssetNumber" => $data->asset_serial_number ?? '',
                    "RequestNo" => $data->request_number ?? '',
                    "AssetName" => $data->placementAssets->asset_description ?? '',
                    "CurrentVPO" => $data->inventoryOne->vpo_target ?? '',
                    "ChillerType" => $data->eligible_chiller_type ?? '',
                    "AssetTypeCode" => $data->asset_type_code ?? '',
                    "AssetBarcode" => $data->asset_barcode ?? '',
                    "CustomerName" => $data->userOne->name ?? '',
                    "CustomerCode" => $data->outlet_code ?? '',
                    "ContactPerson" => $data->retaileroutlets->contact_name ?? '',
                    "ContactNumber" => $data->userOne->mobile_number  ?? '',
                    "Latitude" => $data->retaileroutlets->lat ?? '',
                    "Longitude" => $data->retaileroutlets->long ?? '',
                    "CustomerAddress" => $data->customer_address ?? '',
                ];
            }

            return $this->response($array, 'Pullout request details.');
        } catch (\Throwable $th) {
            Log::error("Error in pullout details", ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString()]);
            return $this->error("No data found!");
        }
    }

    private function generateAssetPlacementRequestNumber()
    {
        $timestampComponent = substr(date('YmdHis'), -8);
        $newRequestNumber = 'APR' . $timestampComponent;
        return $newRequestNumber;
    }
}
