<?php

namespace App\Http\Controllers\API\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\ForgetPasswordAPIRequest;
use App\Mail\SendMail;
use App\Models\User;
use Illuminate\Support\Facades\Log;
// use Mail;
use App\Mail\ForgotPasswordMail;
use App\Mail\OtpVerificationMail;
use Illuminate\Support\Facades\Mail;
class ForgePasswordController extends Controller
{

    public function index(ForgetPasswordAPIRequest $request)
    {

        try {
            // Attempt to retrieve the user by email or mobile number
            $user = User::where('email', $request->UserID)
                        ->orWhere('mobile_number', $request->UserID)
                        ->first();

            // If no user is found, return an unauthorized error response
            if (!$user) {
                return $this->error('Invalid Credentials.');
            }

            // Generate OTP and prepare mail response
            $otp = random_int(100000, 999999);
            $mailResponse = [
                'name' => $user->name,
                'otp' => $otp,
                'email' => $user->email,
            ];

           $sentRecipients= Mail::to($user->email)->send(new OtpVerificationMail($mailResponse));
            if (!$sentRecipients > 0) {
                return $this->error('Failed to send OTP. Please try again.');
            }
            $user->update([
                'otp' => $otp,
                'reset_email_sent_on' => now(),
                'reset_token' => uniqid(),
            ]);
            return $this->response([], 'OTP has been sent to your registered email. It will be valid for 5 minutes.');

        } catch (\Exception $e) {
            Log::error("Failed to process forgot password request: " . $e->getMessage());
            // return $this->error('An unexpected error occurred. Please try again.', 500);
            return $this->error($e->getMessage(), 500);
        }
    }


}
