<?php

namespace App\Http\Controllers\API\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;

class AuthController extends Controller
{
    public function login(LoginRequest $request)
    {
        try {
            $credentials = ['user_id' => $request->UserName, 'password' => $request->Password];
            if (!Auth::attempt($credentials)) {
                return $this->error('Invalid Credentials.', 401);
            }

            $user = Auth::user();
            $roles = $user->getRoleNames()->first();
            if ($roles != 'SO' && $roles != 'DSR') {
                return $this->error('Unauthorized userd access.', 401);
            }
            $successResponse = [
                "UserId" => $user->user_id,
                // "UserId" => Crypt::encrypt($user->user_id),
                "Type" => $user->getRoleNames()->first(),
                'Name' => $user->name,
                'Mobile' => $user->mobile_number,
            ];
            return $this->response($successResponse, $user->name . ' logged in successfullyl');
        } catch (\Exception $e) {
            Log::error("Failed to Login Request: " . $e->getMessage());
            return $this->error($e->getMessage(), 500);
        }
    }

    public function cretaUser(Request $request)
    {
        // Validate request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'user_id' => 'required',
            'user_type' => 'required',
            'password' => 'required',
        ]);
        // Create user
        $user = User::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'user_id' => $validatedData['user_id'],
            'user_type' => $validatedData['user_type'],
            'password' => bcrypt($validatedData['password']),
        ]);
        $user->syncRoles($request->user_type);

        return response()->json(['message' => 'User created successfully', 'user' => $user], 201);
    }

}
