<?php

namespace App\Http\Controllers\API\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\OtpVerifyRequest;
use App\Http\Requests\Vendor\UpdateDeviceFCMTokenRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Mail\OtpVerificationMail;
use Illuminate\Support\Facades\Mail;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class VendorAuthController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['login', 'otpVerify']]);
    }

    public function login(LoginRequest $request)
    {
        try {
            $credentials = ['user_id' => $request->UserName, 'password' => $request->Password,'user_type'=>"VE"];
            if (!Auth::attempt($credentials)) {
                return $this->error('Invalid Credentials.', 401);
            }
            $user = Auth::user();
            $token = JWTAuth::fromUser($user);
            $authorizationToken = $this->token($token);
            // $authorizationToken = $this->formatToken($token);
            $successResponse = [
                "UserId" => $user->user_id,
                'Name' => $user->name,
                "Type" => $user->getRoleNames()->first(),
                'Email' => $user->email,
                'Mobile' => $user->mobile_number,
                'ProfileImage' => null,
                'Designation' => $user->getRoleNames()->first(),
                'Autherization' => $authorizationToken,
            ];
            return $this->response($successResponse, $user->name . ' logged in successfully');
        } catch (\Exception $e) {
            Log::error("Login: " . $e->getMessage());
            return $this->error($e->getMessage(), 500);
        }
    }

    public function UpdateDeviceFCMToken(UpdateDeviceFCMTokenRequest $request)
    {
        try {

            // Retrieve validated data
            $validatedData = $request->validated();

            $status = User::where("user_id", $request->UserId)->update([
                "device_token" => $validatedData['DeviceToken'],
                "device_type" => $validatedData['DeviceType']
            ]);

            if (!$status) {
                return $this->error("Something went wrong , device details not udpated");
            }
            // Your update logic here
            return $this->response([], 'Device details updated successfully');
        } catch (\Exception $e) {
            Log::error("UpdateDeviceFCMToken: " . $e->getMessage());
            return $this->error($e->getMessage(), 500);
        }
    }

    public function logout()
    {
        try {
            Auth::guard('api')->logout(); // Invalidate the token
            return $this->response([], 'Successfully logged out');
        } catch (\Exception $e) {
            Log::error("Logout Error: " . $e->getMessage());
            // Return an error response if something goes wrong
            return $this->error("An unexpected error occurred. Please try again later.", 500);
        }
    }

    public function refresh()
    {
        try {
            return $this->response($this->token($this->guard()->refresh()));
        } catch (\Exception $e) {
            Log::error("Unexpected error during token refresh: " . $e->getMessage());
            return $this->error("An unexpected error occurred. Please try again later.", 500);
        }
    }

    private function formatToken($token)
    {
        // Add any formatting or processing if necessary
        return $token;
    }
}
