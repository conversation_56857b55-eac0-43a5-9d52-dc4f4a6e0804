<?php

namespace App\Http\Controllers\API\V2\Maintenance;

use App\Http\Controllers\Controller;
use App\Http\Requests\Maintenance\MaintenanceRequestDetails;
use App\Http\Requests\Maintenance\MaintenanceRequestStoreRequest;
use App\Models\MaintenanceRequest;
use App\Services\API\Maintenance\MaintenanceRequestAPIService;
use App\Services\AssetInventoryService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MaintenanceRequestControllerV2 extends Controller
{
    public function storeMaintenanceRequest(MaintenanceRequestStoreRequest $request)
    {
        try {
            Log::info('RequestData : storeMaintenanceRequest data', $request->all());

            $validatedData = $request->validated();

            $hierarchyList = PlacementRequestService::outletHeirarchy($validatedData['outlet_id']);
            $maintenance_request_number = PlacementRequestService::generateRequestNumber('AMR');
            $hierarchyUser = $hierarchyList->user ?? '';
            $distributor = $hierarchyList->user->distributor ?? '';

            #asset type
            $validAsset = AssetInventoryService::getAssetDetails($validatedData['chiller_type']);
            if(empty($validAsset['asset_code'])){
                return $this->error('Invalid Asset type', 400);
            }
            $asset_unique_code = $validAsset['asset_code'];
            $asset_type = $validAsset['asset_type']??$validatedData['chiller_type'];

            #Hierarchy User
            $validatedData['chiller_type'] = $asset_type;
            $validatedData['asset_type_code'] = $asset_unique_code;
            $validatedData['outlet_code'] = $validatedData['customer_code'];
            $validatedData['ae_code'] = $hierarchyUser->ae_id ?? '';
            $validatedData['rsm_code'] = $hierarchyUser->rsm_id ?? '';
            $validatedData['asm_code'] = $hierarchyUser->asm_id ?? '';
            $validatedData['so_code'] = $hierarchyUser->so_id ?? '';
            $validatedData['db_code'] = $hierarchyUser->distributor_id ?? '';
            $validatedData['dsr_code'] = $hierarchyList->salesman_code ?? '';
            $validatedData['cfa_code'] = $distributor->cfa_code ?? '';
            $validatedData['pending_from'] = 'ASM';
            $validatedData['maintenance_request_number'] = $maintenance_request_number;
            Log::info('storeMaintenanceRequest stored data', $validatedData);
            $maintenanceRequest = MaintenanceRequest::create($validatedData);
            if (!$maintenanceRequest) {
                return $this->error('Failed to create asset maintenance request, please try again', 400);
            }
            return $this->response(["MaintenanceRequestNumber" => $maintenance_request_number], 'Asset Maintenance Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : storeMaintenanceRequest data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to create asset Maintenance request', 400);
        }
    }
    private function parseDate($date)
    {
        return $date ? Carbon::createFromFormat('Y-m-d', $date) : null;
    }


}
