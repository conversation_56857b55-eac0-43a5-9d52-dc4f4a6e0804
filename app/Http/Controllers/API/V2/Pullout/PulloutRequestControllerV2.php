<?php

namespace App\Http\Controllers\API\V2\Pullout;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\RetailerOutlet;
use App\Models\AssetPulloutRequest;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\AssetPlacementRequest;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\Pullout\PulloutRequest;
use App\Http\Requests\Pullout\PulloutRequestList;
use App\Http\Requests\Pullout\PulloutRequestDetails;
use App\Http\Requests\Pullout\PulloutAssetDetailsRequest;
use App\Services\AssetInventoryService;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;

class PulloutRequestControllerV2 extends Controller
{
    protected $taskStatusService;
    public function __construct(TaskStatusService $taskStatusService)
    {
        $this->taskStatusService = $taskStatusService;
    }
    public function pulloutCreate(PulloutRequest $request)
    {
        //    return $request->all();
        Log::info("pulloutCreate", [$request->all()]);
        try {
            // Generate the asset placement request number
            $assetRequestNumber = PlacementRequestService::generateRequestNumber('PR');

            // Add the request number to the mapped data

            $validated = $request->validated();
           $serialNumberRequest = AssetPulloutRequest::where('asset_serial_number', $validated["AssetNumber"])
                ->orderBy('created_at', 'desc')
                ->first();
            if ($serialNumberRequest) {
                if ($serialNumberRequest->asset_assigned_status !== 'Rejected') {
                    return $this->error("Request already submitted for this Asset Serial Number",409);
                }
            }

            #validate asset type :
            $validAsset = AssetInventoryService::getAssetDetails($validated["ChillerType"]);
            if(empty($validAsset['asset_code'])){
                return $this->error('Invalid Asset type', 400);
            }
            $asset_type_code = $validAsset['asset_code'];
            $asset_type = $validAsset['asset_type']??$validated["ChillerType"];

            #handle Hierarchy
            $heirarchy = PlacementRequestService::outletHeirarchy($request->input("OutletId"));
            $heirarchyUser = $heirarchy->user ?? '';
            $distributor = $heirarchy->user->distributor ?? '';
            $data = [
                "user_id" => $validated["UserId"],
                "type" => $validated["Type"],
                "outlet_id" => $validated["OutletId"],
                "chiller_type" => $asset_type,
                "asset_type_code" => $asset_type_code,
                "asset_serial_number" => $validated["AssetNumber"],
                "asset_barcode" => $validated["AssetBarCode"],
                "ScannedBarcode" => $validated["ScannedBarcode"],
                "current_vpo" => $validated["CurrentVPO"],
                "customer_name" => $validated["CustomerName"],
                "customer_code" => $validated["CustomerCode"],
                "outlet_code" => $validated["CustomerCode"],
                "contact_person" =>  $request->input("ContactPerson"),
                "contact_number" =>  $request->input("ContactNumber"),
                "pullout_asset_type" => $validated["PulloutAssetType"],
                "consent_status" => $validated["ConsentStatus"],
                "signature" => $validated["Signature"],
                "email" => $validated["Email"],
                "mobile_number" => $validated["MobileNo"],
                "customer_address" => $validated["CustomerAddress"],
                "pinCode" => $validated["PinCode"],
                "pullout_reason" => $validated["PulloutReason"],
                "customer_location" => $validated["CustomerLocation"],
                "current_location" => $validated["CurrentLocation"]??'',
                "distance" => $validated["Distance"],
                "latitude" => $validated["Latitude"],
                "longitude" => $validated["Longitude"],
                "asset_assigned_status" => $validated["Status"],
                "remarks" =>  $request->input("Remarks"),
                "created_at" => now(),
                "updated_at" => now(),
                'ae_code' => $heirarchyUser->ae_id ?? '',
                'rsm_code' => $heirarchyUser->rsm_id ?? '',
                'asm_code' => $heirarchyUser->asm_id ?? '',
                'so_code' => $heirarchyUser->so_id ?? '',
                'db_code' => $heirarchyUser->distributor_id ?? '',
                'dsr_code' => $heirarchy->salesman_code ?? '',
                'cfa_code' => $distributor->cfa_code ?? '',
                'pending_from' => 'ASM',
                'task_status' => 'Pending',
            ];
            $data['pullout_request_number'] = $assetRequestNumber;
            Log::info('pulloutCreate stored data', $data);
            $response = AssetPulloutRequest::insert($data);
            if (!$response) {
                return $this->response([], 'Submit Failed.');
            }
            return $this->response(['request_number' => $assetRequestNumber], 'Asset Placement Requst submitted successfully.');
        } catch (\Throwable $th) {
            Log::error("Error in pulloutCreate", ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString()]);
            return $this->error("pulloutCreate Request Submit Failed.");
        }
    }


    private function generateAssetPlacementRequestNumber()
    {
        $timestampComponent = substr(date('YmdHis'), -8);
        $newRequestNumber = 'APR' . $timestampComponent;
        return $newRequestNumber;
    }
}
