<?php

namespace App\Http\Controllers\API\V2\Vendor\Placement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\AssetTaskPlacement\AssetPlacementTaskDeploymentRequest;
use App\Http\Requests\Vendor\AssetTaskPlacement\CompletePlacemementDetailsRequest;
use App\Http\Requests\Vendor\AssetTaskPlacement\VendorVerifyChillerSerialNumberRequest;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\AssetPulloutRequest;
use App\Models\AssetReplacementRequest;
use App\Models\AssetTaskPlacement\AssetPlacementTaskDeploymentModel;
use App\Models\AsssetInventory;
use App\Models\MaintenanceRequest;
use App\Models\OutletAssetDetailsModel;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VendorPlacementTaskController_V2 extends Controller
{

    public function VendorVerifyChillerSerialNumber(VendorVerifyChillerSerialNumberRequest $request)
    {
        try {
            $serialNumber = $request->input('serialNumber');
            $chillerType = $request->input('chillerType');
            $user = Auth::guard('api')->user();
            $assetInventory = AsssetInventory::where(
                [
                    'serial_number' => $serialNumber,
                    'warehouse_code' => $user->cfa_code,
                    'asset_type_code' => $chillerType,
                    'assigned_status' => 'no',
                    'asset_approval_status' => 'approved',
                ]
            )->first();
            $VerifyStatus = $assetInventory ? true : false;
            return $this->response(['serialNumberstatus' => $VerifyStatus], 'Asset Serial number verification');
        } catch (\Exception $exception) {
            Log::error('Error : VendorVerifyChillerSerialNumber data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get VendorVerifyChillerSerialNumber request list', 500);
        }
    }
}
