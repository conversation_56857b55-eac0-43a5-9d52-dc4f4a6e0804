<?php

namespace App\Http\Controllers\API\V2\AssetPlacement;

use App\Http\Controllers\Controller;
use App\Http\Requests\AssetPlacement\CreateNewAssetPlacementRequest;
use App\Models\AssetPlacementRequest;
use App\Models\AsssetInventory;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\AssetInventoryService;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class AssetPlacementRequestController_V2 extends Controller
{

    protected $taskStatusService;
    public function __construct(TaskStatusService $taskStatusService)
    {
        $this->middleware('validateUserId');
        $this->taskStatusService = $taskStatusService;
    }

    public function createNewAssetPlacement(createNewAssetPlacementRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $heirarchy = RetailerOutlet::where('id', $validatedData['OutletId'])
                ->with(['user' => function ($query) {
                    $query->select('id', 'user_id', 'ae_id', 'rsm_id', 'asm_id', 'so_id', 'distributor_id');
                }])
                ->first();
            $heirarchyUser = $heirarchy->user ?? '';
            $distributor = $heirarchy->user->distributor ?? '';

            $requestAlreadyExist=PlacementRequestService::getRetailerChillerRequest($heirarchyUser->user_id);
            if($requestAlreadyExist > 0 ){
                return $this->error("Request already submitted for this Outlet Number : ".$heirarchyUser->user_id,409);
            }
            // Get validated data from the request
            $validAsset = AssetInventoryService::getAssetDetails($validatedData['EligibleChillerType']);
            if(empty($validAsset['asset_code'])){
                return $this->error('Invalid Asset type', 400);
            }
            $asset_unique_code = $validAsset['asset_code'];
            $asset_type = $validAsset['asset_type']??$validatedData['EligibleChillerType'];
            // Map request fields to database columns
            $mappedData = [
                'user_id' => $validatedData['UserId'],
                'outlet_id' => $validatedData['OutletId'],
                'type' => $validatedData['Type'],
                'expected_vpo' => $validatedData['ExpectedVPO'] ?? '',
                'eligible_chiller_type' => $asset_type ?? null,
                'asset_type_code' => $asset_unique_code??null, //add for asset code
                'request_type' => $validatedData['RequestType'],
                'additional_equipment' => json_encode($validatedData['AdditionalEquipment'] ?? []),
                'competitor_chiller_size' => json_encode($validatedData['CompetitorChillerSize'] ?? []),
                'competitor_company' => json_encode($validatedData['CompetitorCompany'] ?? []),
                'competitor_chiller_photo' => $validatedData['CompetitorChillerPhoto'],
                'chiller_location' => $validatedData['ChillerLocation'],
                'chiller_location_photo' => $validatedData['ChillerLocationPhoto'],
                'address_proof' => $validatedData['AddressProof'],
                'retailer_photo' => $validatedData['RetailerPhoto'],
                'signature' => asset($validatedData['Signature']),
                'mobile_number' => $validatedData['MobileNumber'],
                'customer_address' => $validatedData['CustomerAddress'],
                'pincode' => $validatedData['Pincode'],
                'customer_location' => $validatedData['CustomerLocation'],
                'current_location' => $validatedData['CurrentLocation'],
                'latitude' => $validatedData['Latitude'],
                'longitude' => $validatedData['Longitude'],
                'correct_location' => $validatedData['CorrectLocation'],
                'distance' => $validatedData['Distance'],
                'remarks' => $validatedData['Remarks'],
                'consent_status' => $validatedData['ConsentStatus'],
                'vpo_target' => $request->VPOTarget ?? 0,
                "outlet_code" => $request->OutletCode ?? $heirarchyUser->user_id ?? '',
                "ae_code" => $request->AE_Code ?? $heirarchyUser->ae_id ?? '',
                "rsm_code" => $request->RSM_Code ?? $heirarchyUser->rsm_id ?? '',
                "asm_code" => $request->ASM_Code ?? $heirarchyUser->asm_id ?? '',
                "so_code" => $request->So_Code ?? $heirarchyUser->so_id ?? '',
                "db_code" => $request->DB_Code ?? $heirarchyUser->distributor_id ?? '',
                "dsr_code" => $request->DSRCode ?? $heirarchy->salesman_code ?? '',
                'cfa_code' => $request->CFA_Code ?? $distributor->cfa_code ?? '',
                'pending_from' => 'ASM',
                'is_bolt_town' => (boolean)($request->IsBoltTown ?? false),
            ];
            // Generate the asset placement request number
            $assetRequestNumber = $this->generateAssetPlacementRequestNumber();

            // Add the request number to the mapped data
            $mappedData['request_number'] = $assetRequestNumber;

            // Create a new AssetPlacementRequest directly using create()
            $res = AssetPlacementRequest::create($mappedData);
            if (!$res) {
                return $this->error('Asset Request not created', 404);
            }
            return $this->response(["RequestNumber" => $assetRequestNumber], 'Asset Placement Requst submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : createNewAssetPlacement data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to create asset placement request', 500);
        }
    }

    private function generateAssetPlacementRequestNumber()
    {
        $timestampComponent = substr(date('YmdHis'), -8);
        $newRequestNumber = 'APR' . $timestampComponent;

        return $newRequestNumber;
    }
}
