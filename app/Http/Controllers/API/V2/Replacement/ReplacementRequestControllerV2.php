<?php

namespace App\Http\Controllers\API\V2\Replacement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Replacement\ReplacementCreateRequest;
use App\Http\Requests\Replacement\ReplacementRequestDetailsRequest;
use App\Models\AssetReplacementRequest;
use App\Models\InvoiceChallan;
use App\Services\API\Replacement\ReplacementRequestAPIService;
use App\Services\AssetInventoryService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ReplacementRequestControllerV2 extends Controller
{
    public function createReplacementRequest(ReplacementCreateRequest $request): \Illuminate\Http\JsonResponse
    {
        try {
            Log::info('RequestData : createReplacementRequest request data', $request->all());

            $validatedData = $request->validated();
            $validatedData = PlacementRequestService::getHierarchy($validatedData);
            $replacement_request_number = PlacementRequestService::generateRequestNumber(InvoiceChallan::PREFIX_ASSET_REPLACEMENT_REQUEST);
            $validatedData['pending_from'] = 'ASM';
            $validatedData['additional_equipment'] =  json_encode($validatedData['additional_equipment'] ?? []);
            $validatedData['request_number'] = $replacement_request_number;

            #asset type
            $validAsset = AssetInventoryService::getAssetDetails($validatedData['chiller_type']);
            if(empty($validAsset['asset_code'])){
                return $this->error('Invalid Asset type', 400);
            }
            $asset_type_code = $validAsset['asset_code'];
            $asset_type = $validAsset['asset_type']??$validatedData['chiller_type'];
            $validatedData['chiller_type'] = $asset_type;
            $validatedData['asset_type_code'] = $asset_type_code;

            #request Chiller type :
            $requestAsset = AssetInventoryService::getAssetDetails($validatedData['request_chiller_type']);
            if(empty($requestAsset['asset_code'])){
                return $this->error('Invalid Request Asset type', 400);
            }
            $request_chiller_code = $requestAsset['asset_code'];
            $request_chiller_type = $requestAsset['asset_type']??$validatedData['request_chiller_type'];
            $validatedData['request_chiller_type'] = $request_chiller_type;
            $validatedData['request_chiller_code'] = $request_chiller_code;
            $validatedData['is_bolt_town'] = (boolean)($request->IsBoltTown ?? false);

            Log::info('createReplacementRequest API SO APP stored data', $validatedData);
            $replacementRequest = AssetReplacementRequest::create($validatedData);
            if (!$replacementRequest) {
                return $this->error('Failed to create asset replacement request, please try again', 400);
            }
            return $this->response(["ReplacementRequestNumber" => $replacement_request_number], 'Asset Replacement Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : createReplacementRequest data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to create asset createReplacementRequest request', 400);
        }
    }

}
