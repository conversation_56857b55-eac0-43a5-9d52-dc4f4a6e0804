<?php

namespace App\Http\Controllers\API\AssetAudit;

use App\Http\Controllers\Controller;
use App\Http\Requests\Audit\AssetAuditDetailRequest;
use App\Http\Requests\Audit\AssetAuditListRequest;
use App\Http\Requests\Audit\AssetAuditRequest;
use App\Models\AssetAudit;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AssetAuditAPIController extends Controller
{
    #create audit request
    public function assetAuditRequestStore(AssetAuditRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $heirarchy = PlacementRequestService::outletHeirarchy($validatedData['outlet_id']);
            $heirarchyUser = $heirarchy->user ?? '';
            $distributor = $heirarchy->user->distributor ?? '';
            $dataToStore = [
                'request_number' => $this->generateAssetPlacementRequestNumber(),
                'user_id' => $validatedData['user_id'] ?? null,
                'type' => $validatedData['type'] ?? null,
                'outlet_id' => $validatedData['outlet_id'] ?? null,
                'asset_id' => $validatedData['asset_id'] ?? null,
                'asset_number' => $validatedData['asset_number'] ?? null,
                'is_chiller_available' => $validatedData['is_chiller_available'] ?? null,
                'is_barcode_available' => $validatedData['is_barcode_available'] ?? null,
                'barcode_no_scan_reason' => $validatedData['barcode_no_scan_reason'] ?? null,
                'scanned_barcode' => $validatedData['scanned_barcode'] ?? null,
                'barcode_match_status' => $validatedData['barcode_match_status'] ?? null,
                'customer_address' => $validatedData['customer_address'] ?? null,
                'contact_number' => $validatedData['contact_number'] ?? null,
                'retailer_name' => $validatedData['retailer_name'] ?? null,
                'pincode' => $validatedData['pincode'] ?? null,
                'current_location' => $validatedData['current_location'] ?? null,
                'latitude' => $validatedData['latitude'] ?? null,
                'longitude' => $validatedData['longitude'] ?? null,
                'distance' => $validatedData['distance'] ?? null,
                'audit_status' => $validatedData['audit_status'] ?? null,
                'remarks' => $validatedData['remarks'] ?? null,
                "outlet_code" => $request->OutletCode ?? '',
                "ae_code" =>  $heirarchyUser->ae_id ?? '',
                "rsm_code" => $heirarchyUser->rsm_id ?? '',
                "asm_code" => $heirarchyUser->asm_id ?? '',
                "so_code" => $heirarchyUser->so_id ?? '',
                "db_code" => $heirarchyUser->distributor_id ?? '',
                "dsr_code" => $heirarchy->salesman_code ?? '',
                'cfa_code' => $distributor->cfa_code ?? '',
            ];
            $assetAudit = AssetAudit::create($dataToStore);
            if (!$assetAudit) {
                return $this->error('Request not created,please try again once', 400);
            }
            return $this->response(['request_number' => $assetAudit->request_number], 'Asset Audit Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : Failed to  assetAuditRequestStore update at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to  create Asset Audit Request', 500);
        }
    }

    #get list of Audit request
    public function getAuditRequestList(AssetAuditListRequest $request)
    {
        try {
            $limit = $request->input('limit', 10);
            $offset = $request->input('offset', 0);

            $query = AssetAudit::with([
                'outlet', // Eager load nested relationships
                'asset', // Eager load 'asset' relationship
                'asset.placement', // Eager load 'placement' relationship for 'asset'
            ])
                ->when($request->filled('UserId'), function ($q) use ($request) {
                    return $q->where('user_id', $request->input('UserId'));
                })
                ->when($request->filled('OutletId'), function ($q) use ($request) {
                    return $q->where('outlet_id', $request->input('OutletId'));
                })
                ->when($request->filled('Type'), function ($q) use ($request) {
                    return $q->where('type', $request->input('Type'));
                })
                ->when($request->filled('FromDate'), function ($q) use ($request) {
                    return $q->whereDate('created_at', '>=', $request->input('FromDate'));
                })
                ->when($request->filled('ToDate'), function ($q) use ($request) {
                    return $q->whereDate('created_at', '<=', $request->input('ToDate'));
                })
                ->latest()
                ->offset($offset)
                ->limit($limit);

            // Retrieve asset audits
            $assetAudits = $query->get();
            $formattedResponse = $assetAudits->map(function ($audit) {
                return [
                    'OutletId' => $audit->outlet_id,
                    'RequestId' => $audit->request_number,
                    'CustomerName' => $audit->asset->outlet_name ?? '',
                    'CustomerAddress' => $audit->customer_address,
                    'CustomerCode' => $audit->asset->outlet_code ?? '',
                    'AssetBarCode' => $audit->asset->asset_barcode ?? '',
                    'AuditedAssetBarCode' => $audit->scanned_barcode,
                    'DSRName' => $audit->outlet->salesman_name ?? '',
                    'ChillerType' => $audit->asset->placement->eligible_chiller_type ?? '',
                    'AuditDateTime' => $audit->created_at,
                    'AuditStatus' => $audit->audit_status,

                    'ChillerAvailable' => $audit->is_chiller_available,
                    'BarCodeAvailable' => $audit->is_barcode_available,
                    'BarCodeMatchStatus' => $audit->barcode_match_status,
                    'NoScanReason' => $audit->barcode_no_scan_reason,
                    'Remarks' => $audit->remarks,
                ];
            });
            // Return the response
            return $this->response($formattedResponse, 'Asset Audit Request list get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : Failed to  getAuditRequestList update at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to  get Asset Audit Request', 500);
        }
    }

    public function getAuditRequestDetails(AssetAuditDetailRequest $request)
    {
        try {
            $asset_audit = AssetAudit::where(['user_id' => $request->UserId, 'request_number' => $request->RequestId])

                ->with([
                    'asset' => function ($query) {
                        $query->select('id', 'asset_barcode', 'placement_request_number');
                    }, 'outlet' => function ($query) {
                        $query->select('id', 'user_id', 'salesman_code', 'salesman_name', 'invoiceaddress');
                    }, 'outlet.user' => function ($query) {
                        $query->select('id', 'user_id', 'name', 'user_id');
                    },
                    'asset.placement' => function ($query) {
                        $query->select('id', 'user_id', 'request_number', 'eligible_chiller_type');
                    },
                ])
                ->first();
            if (!$asset_audit) {
                return $this->error('Asset audit details not found ', 400);
            }

            $details = [
                'OutletId' => $asset_audit->outlet_id,
                'RequestId' => $asset_audit->request_number,
                'CustomerName' => $asset_audit->outlet->user->name ?? '',
                'CustomerCode' => $asset_audit->outlet->user->user_id ?? '',
                'DSRName' => $asset_audit->outlet->salesman_name ?? '',
                'AssetId' => $asset_audit->asset_id,
                'AssetNumber' => $asset_audit->asset_number ?? '',
                'AssetBarcode' => $asset_audit->asset->asset_barcode ?? '',
                'ChillerType' => $asset_audit->asset->placement->eligible_chiller_type ?? '',
                'AuditDateTime' => $asset_audit->created_at,
                'IsChillerAvailable' => $asset_audit->is_chiller_available,
                'IsBarCodeAvailable' => $asset_audit->is_barcode_available,
                'ScannedBarCode' => $asset_audit->scanned_barcode,
                'BarCodeMatchStatus' => $asset_audit->barcode_match_status,
                'BarCodeNoScanReason' => $asset_audit->barcode_no_scan_reason,
                'CustomerAddress' => $asset_audit->customer_address ?? '',
                'CurrentLocation' => $asset_audit->current_location ?? '',
                'CustomerLocation' => $asset_audit->outlet->invoiceaddress ?? '',
                'Distance' => $asset_audit->distance,
                'AuditStatus' => $asset_audit->audit_status,
                'ContactNumber' => $asset_audit->contact_number,
                'RetailerName' => $asset_audit->retailer_name,
                'Pincode' => $asset_audit->pincode,
                'Remarks' => $asset_audit->remarks

            ];
            return $this->response($details, 'Asset Audit details get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : Failed to  getAuditRequestDetails update at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to  get Asset Audit Request details', 500);
        }
    }
    private function generateAssetPlacementRequestNumber()
    {
        do {
            $timestampComponent = substr(date('YmdHis'), -8);
            $newRequestNumber = 'AAR' . $timestampComponent;
            // Check if the generated number already exists in the database
            $existingRequest = AssetAudit::where('request_number', $newRequestNumber)->first();
        } while ($existingRequest !== null);

        return $newRequestNumber;
    }
}
