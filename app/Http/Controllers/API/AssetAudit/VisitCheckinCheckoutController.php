<?php

namespace App\Http\Controllers\API\AssetAudit;

use App\Http\Controllers\Controller;
use App\Http\Requests\Audit\GetLastCheckInCheckOutDetailsRequest;
use App\Http\Requests\Audit\VisitCheckinCheckoutRequest;
use App\Models\AssetOutletAuditVisitAttandance;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class VisitCheckinCheckoutController extends Controller
{
    public function CheckinCheckoutAttandanceMark(VisitCheckinCheckoutRequest $request)
    {
        try {
            $requestData = $request->all();
            $updateData = [
                'user_id' => $requestData['UserId'],
                'user_type' => $requestData['Type'],
                'outlet_id' => $requestData['OutletId'],
                'visit_type' => $requestData['VisitType'],

            ];

            if (!empty($requestData['attandance_id'])) {
                $attandace = AssetOutletAuditVisitAttandance::where('id', $request->attandance_id)->first();
                if (!$attandace) {
                    return $this->error('invalid attandance details');
                }
                $updateData['checkout_time'] = now();
                $updateData['checkout_latitude'] = $requestData['Latitude'];
                $updateData['checkout_longitude'] = $requestData['Longitude'];
                $updateData['checkout_distance'] = $requestData['Distance'];
                $updateData['visit_type'] = 'checkout';
                $res = AssetOutletAuditVisitAttandance::where('id', $request->attandance_id)->update($updateData);
                $id = $requestData['attandance_id'];
                $message = "Outlet Checkout Successfully";
            } else {
                $updateData['checkin_time'] = now();
                $updateData['checkin_latitude'] = $requestData['Latitude'];
                $updateData['checkin_longitude'] = $requestData['Longitude'];
                $updateData['checkin_distance'] = $requestData['Distance'];

                $today = Carbon::now()->format('Y-m-d');
                #check for duplicate request .if in single day its not checkout whenever comming next request to update.
                $latestActivity = AssetOutletAuditVisitAttandance::where(['user_id' => $requestData['UserId'], 'user_type' => $requestData['Type'], 'outlet_id' => $requestData['OutletId']])
                    ->whereDate('checkin_time', $today)
                    ->latest()
                    ->first();
                    if ($latestActivity) {
                        // User has some activity on the current date
                        if ($latestActivity->visit_type == 'checkout') {
                            // User has already checked out, create a new record for check in
                            $attandace = AssetOutletAuditVisitAttandance::create($updateData);
                            $id = $attandace->id;
                            $message = "Outlet Checkin Successfully";
                        } else {
                            // User has already checked in, update the existing record
                            $latestActivity->update($updateData);
                            $id = $latestActivity->id;
                            $message = "Outlet Checkin updated Successfully";
                        }
                    } else {
                        // User is checking in for the first time on the current date
                        $attandace = AssetOutletAuditVisitAttandance::create($updateData);
                        $id = $attandace->id;
                        $message = "Outlet Checkin Successfully";
                    }
            }

            return $this->response(['attandance_id' => $id], $message);
        } catch (\Exception $exception) {
            Log::error('Error : Failed to  CheckinCheckoutAttandanceMark at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to  mark visit attandacne', 500);
        }
    }


    public function GetLastCheckInCheckOutDetails(GetLastCheckInCheckOutDetailsRequest $request)
    {
        try {
            $attandacne =  AssetOutletAuditVisitAttandance::where(['user_id' => $request->UserId, 'user_type' => $request->Type, 'outlet_id' => $request->OutletId])->latest()->first();
            if (!$attandacne) {
                return $this->errorReponse([], "Checkin and Checkout details not available!");
            }
            $attandaceData = [
                'attandance_id' => $attandacne->id,
                'LastCheckinDateTime' => $attandacne->checkin_time,
                'LastCheckoutDateTime' => $attandacne->checkout_time,
            ];
            return $this->response($attandaceData, "Last Outlet Checkin and Checkout details get successfully !");
        } catch (\Exception $exception) {
            Log::error('Error : Failed to  GetLastCheckInCheckOutDetails at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get last checkin details', 500);
        }
    }
}
