<?php

namespace App\Http\Controllers\API\AssetPlacement;

use App\Http\Controllers\Controller;
use App\Http\Requests\AssetPlacement\CreateNewAssetPlacementRequest;
use App\Http\Requests\AssetPlacement\GetAPRDetailsRequest;
use App\Http\Requests\AssetPlacement\GetAsseetPlacementRequestListRequest;
use App\Http\Requests\UpdatePlacementRequest;
use App\Models\AssetImage;
use App\Models\AssetPlacementRequest;
use App\Models\AsssetInventory;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\AssetInventoryService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class AssetPlacementRequestController extends Controller
{

    protected $taskStatusService;
    public function __construct(TaskStatusService $taskStatusService)
    {
        $this->middleware('validateUserId');
        $this->taskStatusService = $taskStatusService;
    }

    public function createNewAssetPlacement(createNewAssetPlacementRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $heirarchy = RetailerOutlet::where('id', $validatedData['OutletId'])
                ->with(['user' => function ($query) {
                    $query->select('id', 'user_id', 'ae_id', 'rsm_id', 'asm_id', 'so_id', 'distributor_id');
                }])
                ->first();
            $heirarchyUser = $heirarchy->user ?? '';
            $distributor = $heirarchy->user->distributor ?? '';
            // Get validated data from the request
            $validAsset = AssetInventoryService::getAssetDetails($validatedData['EligibleChillerType']);
            if(empty($validAsset['asset_code'])){
                return $this->error('Invalid Asset type', 400);
            }
            $asset_unique_code = $validAsset['asset_code'];
            $asset_type = $validAsset['asset_type']??$validatedData['EligibleChillerType'];
            // Map request fields to database columns
            $mappedData = [
                'user_id' => $validatedData['UserId'],
                'outlet_id' => $validatedData['OutletId'],
                'type' => $validatedData['Type'],
                'expected_vpo' => $validatedData['ExpectedVPO'] ?? '',
                'eligible_chiller_type' => $asset_type ?? null,
                'asset_type_code' => $asset_unique_code??null, //add for asset code
                'request_type' => $validatedData['RequestType'],
                'additional_equipment' => json_encode($validatedData['AdditionalEquipment'] ?? []),
                'competitor_chiller_size' => json_encode($validatedData['CompetitorChillerSize'] ?? []),
                'competitor_company' => json_encode($validatedData['CompetitorCompany'] ?? []),
                'competitor_chiller_photo' => $validatedData['CompetitorChillerPhoto'],
                'chiller_location' => $validatedData['ChillerLocation'],
                'chiller_location_photo' => $validatedData['ChillerLocationPhoto'],
                'address_proof' => $validatedData['AddressProof'],
                'retailer_photo' => $validatedData['RetailerPhoto'],
                'signature' => asset($validatedData['Signature']),
                'mobile_number' => $validatedData['MobileNumber'],
                'customer_address' => $validatedData['CustomerAddress'],
                'pincode' => $validatedData['Pincode'],
                'customer_location' => $validatedData['CustomerLocation'],
                'current_location' => $validatedData['CurrentLocation'],
                'latitude' => $validatedData['Latitude'],
                'longitude' => $validatedData['Longitude'],
                'correct_location' => $validatedData['CorrectLocation'],
                'distance' => $validatedData['Distance'],
                'remarks' => $validatedData['Remarks'],
                'consent_status' => $validatedData['ConsentStatus'],
                'is_bolt_town' => $request->IsBoltTown??0,
                'vpo_target' => $request->VPOTarget ?? 0,
                "outlet_code" => $request->OutletCode ?? $heirarchyUser->user_id ?? '',
                "ae_code" => $request->AE_Code ?? $heirarchyUser->ae_id ?? '',
                "rsm_code" => $request->RSM_Code ?? $heirarchyUser->rsm_id ?? '',
                "asm_code" => $request->ASM_Code ?? $heirarchyUser->asm_id ?? '',
                "so_code" => $request->So_Code ?? $heirarchyUser->so_id ?? '',
                "db_code" => $request->DB_Code ?? $heirarchyUser->distributor_id ?? '',
                "dsr_code" => $request->DSRCode ?? $heirarchy->salesman_code ?? '',
                'cfa_code' => $request->CFA_Code ?? $distributor->cfa_code ?? '',
                'pending_from' => 'ASM'
            ];
            // Generate the asset placement request number
            $assetRequestNumber = $this->generateAssetPlacementRequestNumber();

            // Add the request number to the mapped data
            $mappedData['request_number'] = $assetRequestNumber;

            // Create a new AssetPlacementRequest directly using create()
            $res = AssetPlacementRequest::create($mappedData);
            if (!$res) {
                return $this->error('Asset Request not created', 404);
            }
            return $this->response(["RequestNumber" => $assetRequestNumber], 'Asset Placement Requst submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : createNewAssetPlacement data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to create asset placement request', 500);
        }
    }

    public function updateAssetPlacementRequest(UpdatePlacementRequest $request)
    {
        try {
            $whereClause = ['user_id' => $request->UserId, "type" => $request->Type, "request_number" => $request->RequestId];
            $assetPlacementRequest = AssetPlacementRequest::where($whereClause)->first();
            if (!$assetPlacementRequest) {
                return $this->error('Asset placement request not found', 404);
            }
            $updateData = [
                'mobile_number' => $request->MobileNumber,
                'customer_address' => $request->CustomerAddress,
                'pincode' => $request->Pincode
            ];
            $updateStatus =  AssetPlacementRequest::where($whereClause)->update($updateData);
            if (!$updateStatus) {
                return $this->error('Asset placement request not updated', 404);
            }

            return $this->response([], 'Asset Placement Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : Failed to  updateAssetPlacementRequest update at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to  updateAssetPlacementRequest update', 500);
        }
    }

    public function getAssetPlacementRequestDetails(GetAPRDetailsRequest $request)
    {
        try {
            $assetPlacementRequest = AssetPlacementRequest::where(['user_id' => $request->UserId, "type" => $request->Type, "request_number" => $request->RequestId])->first();
            if (!$assetPlacementRequest) {
                return $this->error('Asset placement request not found', 404);
            }
            // dd($assetPlacementRequest->toArray());
            $approvalData = $this->taskStatusService->placemenStatus($assetPlacementRequest);
            $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
            $message = !empty($approvalData['status']) ? $approvalData['message'] : null;
            $response = [
                'RequestId' => $assetPlacementRequest->id,
                'RequestNumber' => $assetPlacementRequest->request_number,
                'DateTime' => Carbon::parse($assetPlacementRequest->created_at)->toDateTimeString(),
                'ExpectedVPO' => $assetPlacementRequest->expected_vpo,
                'EligibleChillerType' => $assetPlacementRequest->eligible_chiller_type,
                'RequestType' => $assetPlacementRequest->request_type,
                'AdditionalEquipment' => json_decode($assetPlacementRequest->additional_equipment),
                'CompetitorChillerSize' => json_decode($assetPlacementRequest->competitor_chiller_size),
                'CompetitorCompany' => json_decode($assetPlacementRequest->competitor_company),
                'CompetitorChillerPhoto' => asset($assetPlacementRequest->competitor_chiller_photo),
                'ChillerLocation' => $assetPlacementRequest->chiller_location,
                'ChillerLocationPhoto' => asset($assetPlacementRequest->chiller_location_photo),
                'AddressProof' => asset($assetPlacementRequest->address_proof),
                'RetailerPhoto' => asset($assetPlacementRequest->retailer_photo),
                'Signature' => asset($assetPlacementRequest->signature),
                'ConsentStatus' => $assetPlacementRequest->consent_status,
                'MobileNumber' => $assetPlacementRequest->mobile_number,
                'CustomerAddress' => $assetPlacementRequest->customer_address,
                'Pincode' => $assetPlacementRequest->pincode,
                'CustomerLocation' => $assetPlacementRequest->customer_location,
                'CurrentLocation' => $assetPlacementRequest->current_location,
                'Latitude' => $assetPlacementRequest->latitude,
                'Longitude' => $assetPlacementRequest->longitude,
                'CorrectLocation' => $assetPlacementRequest->correct_location,
                'Distance' => $assetPlacementRequest->distance,
                'Remarks' => $assetPlacementRequest->remarks,
                'Status' => $approvalStatus ?? null,
                'ApprovalMessage' => $message ?? null,
                'PendingAt' => $assetPlacementRequest->pending_from,
                'VPOTarget' => $assetPlacementRequest->vpo_target,
                'IsBoltTown' => $assetPlacementRequest->is_bolt_town

            ];
            return $this->response($response, 'Asset Placement Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : getAssetPlacementRequestDetails data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get asset placement request details', 500);
        }
    }



    public function getAsseetPlacementRequestList(GetAsseetPlacementRequestListRequest $request)
    {
        try {
            $userId = $request->input('UserId');
            $type = $request->input('Type');
            $fromDate = $request->input('FromDate');
            $toDate = $request->input('ToDate');
            $status = $request->input('Status');
            //            $OutletId = $request->input('OutletId');
            //            $query = AssetPlacementRequest::where("outlet_id", $OutletId)->with(['outlet.user']);
            $query = AssetPlacementRequest::with(['outlet.user'])->latest();
            if ($userId) {
                $query->where('user_id', $userId);
            }

            if ($type) {
                $query->where('type', $type);
            }


            if ($fromDate) {
                $query->whereDate('created_at', '>=', Carbon::parse($fromDate));
            }

            if ($toDate) {
                $query->whereDate('created_at', '<=', Carbon::parse($toDate));
            }

            if ($status && $status !== 'All') {
                $query->where('asset_assigned_status', $status);
            }

            $assetPlacementList = $query->get();
            $responseData = $assetPlacementList->map(function ($item) {

                $approvalData = $this->taskStatusService->placemenStatus($item);
                $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
                $message = !empty($approvalData['message']) ? $approvalData['message'] : null;
                return [
                    'RequestId' => $item->id,
                    'RequestNumber' => $item->request_number,
                    'OutletName' => $item->outlet->user->name ?? '', // Assuming the outlet relationship is defined
                    'RouteName' => $item->outlet->route_name,
                    'DSRName' => $item->outlet->salesman_name,
                    'OutletAddress' => $item->outlet->user->address_1 ?? '', // Assuming the outlet relationship is defined
                    'DateTime' => $item->created_at->toDateTimeString(),
                    'Status' => $approvalStatus ?? null,
                    'ApprovalMessage' => $message ?? null,
                    'PendingAt' => $item->pending_from,
                    'IsBoltTown' => $item->is_bolt_town
                ];
            });
            //            if ($assetPlacementList->isEmpty()) {
            //                return $this->error('Asset Placement Request List not available', 404);
            //            }
            return $this->response($responseData, 'Asset Placement Requst list get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : getAsseetPlacementRequestList data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get asset placement request list', 500);
        }
    }


    private function generateAssetPlacementRequestNumber()
    {
        $timestampComponent = substr(date('YmdHis'), -8);
        $newRequestNumber = 'APR' . $timestampComponent;

        return $newRequestNumber;
    }

    public function update_apr_hierarchy()
    {
        $assetPlacementRequests = AssetPlacementRequest::select('request_number', 'outlet_id')->
        with('outlet.user',function($query){
            $query->select('id', 'user_id', 'ae_id', 'rsm_id', 'asm_id', 'so_id', 'distributor_id');
        })
        ->get();
        foreach ($assetPlacementRequests as $key => $apr) {
           $userData= $apr->outlet;
           $distributor = $userData->user->distributor ?? '';
            // $heirarchyUser = $heirarchy->user ?? '';
            // $distributor = $heirarchy->user->distributor ?? '';
            $updateData=[
                "outlet_code" => $userData->user->user_id ?? '',
                "ae_code" => $userData->user->ae_id ?? '',
                "rsm_code" => $userData->user->rsm_id ?? '',
                "asm_code" => $userData->user->asm_id ?? '',
                "so_code" => $userData->user->so_id ?? '',
                "db_code" => $userData->user->distributor_id ?? '',
                "dsr_code" => $userData->salesman_code ?? '',
                'cfa_code' => $distributor->cfa_code ?? '',
            ];
            $res=AssetPlacementRequest::where('request_number',$apr->request_number)->update($updateData);
            if($res){
                echo "updated . ".$apr->request_number."\n";
            }else{
                echo "not updated . ".$apr->request_number."\n";
            }
        }
    }
}
