<?php

namespace App\Http\Controllers\API\AssetImage;

use App\Http\Controllers\Controller;
use App\Http\Requests\AssetImageRequest;
use App\Models\AssetImage;
use Illuminate\Http\Request;
use App\Traits\UploadImage;
use Illuminate\Support\Facades\Log;

class AssetImageUploadController extends Controller
{
    use UploadImage;
    public function __construct()
    {
        $this->middleware('validateUserId');
    }


    public function uploadassetImage(AssetImageRequest $request)
    {
        try {
            if ($request->hasFile('asset_image')) {
                $image = $request->file('asset_image');
                $imageName = $this->saveImage($image, 'images/assetimage/');
                $imagePath = 'images/assetimage/' . $imageName;
                $assetImage = AssetImage::create([
                    "type" => $request->type,
                    "image_url" => $imagePath
                ]);
                if (!$assetImage) {
                    return response()->json(['message' => 'Image file not uploaded.'], 400);
                }
                return $this->response(['ImageId'=>($assetImage->id),'Type'=>$assetImage->type,"ImageUrl"=>$assetImage->image_url], 'Asset Image uploaded successfully');
            } else {
                return response()->json(['message' => 'Image file not provided.'], 400);
            }
        } catch (\Exception $exception) {
            Log::error('ERROR: uploadassetImage Asset image  data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Asset image not uploaded', 500);
        }
    }
}
