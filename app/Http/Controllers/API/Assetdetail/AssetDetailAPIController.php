<?php

namespace App\Http\Controllers\API\Assetdetail;

use App\Http\Controllers\Controller;
use App\Http\Requests\Asset\AssetOutletDetailsRequest;
use App\Models\OutletAssetDetailsModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AssetDetailAPIController extends Controller
{
    public function getAssetOutletDetailsList(AssetOutletDetailsRequest $request)
    {
        try {
            //            $query = OutletAssetDetailsModel::where('outlet_code', $request->OutletId)->with('placement')->latest();
            $assetOutlet = OutletAssetDetailsModel::where('outlet_code', $request->OutletId)
                // ->whereHas('inventory', function ($query) {
                //     $query->where('assigned_status', 'yes');
                // })
                ->with(['placement', 'inventory' => function ($query) {
                    $query->where('assigned_status', 'yes');
                }])
                ->latest()
                ->get();
            // $assetOutlet = $query->get();
            $formattedResponse = $assetOutlet->map(function ($asset) {
                $placement = $asset->placement;
                $chillerPhotoUrl = !empty($placement->chiller_location_photo) && file_exists(public_path($placement->chiller_location_photo)) ? asset($placement->chiller_location_photo) : '';

                return [
                    "AssetType" => "Chiller",
                    'OutletId' => $asset->outlet_code ?? '',
                    'AssetId' => $asset->id,
                    'AssetNumber' => $asset->asset_number ?? '',
                    'AssetBarCode' => $asset->asset_barcode ?? '',
                    'ChillerType' => $placement->eligible_chiller_type ?? '',
                    'ChillerTypeCode' => $placement->asset_type_code ?? '',
                    'ChillerLocation' => $placement->chiller_location ?? '',
                    'ChillerPhotoUrl' => $chillerPhotoUrl,
                    'LastAuditDateTime' => $asset->last_audit_time ?? '',
                ];
            });
            // Return the response
            return $this->response($formattedResponse, 'Asset Audit Request list get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : Failed to  getAssetOutletDetailsList at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to  get Asset outlet details', 500);
        }
    }
}
