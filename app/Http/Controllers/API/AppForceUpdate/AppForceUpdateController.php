<?php

namespace App\Http\Controllers\API\AppForceUpdate;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AppForceUpdateController extends Controller
{
    public function AssetAppVersionForceUpdate(Request $request)
    {
        try {
            if (!$request->app_type) {
                return $this->error('app_type field is mandatory');
            }
            if (!in_array($request->app_type, ['asset', 'vendor'])) {
                return $this->error('Invalid App type');
            }

            $suceesMessage = $request->app_type === 'vendor' ? 'Vendor App force  update successfully' : 'Asset App force  update successfully';
            $appVersion = [
                "appVersion" => $request->app_type === 'vendor' ? '1.0.6' : '1.0.4',
                "forceUpdate" => true,
            ];
            return  $this->response($appVersion, $suceesMessage);
        } catch (\Throwable $e) {
            $ErrorsMessage = $request->app_type === 'vendor' ? 'Vendor force update Failed' : 'Vendor force update Failed';

            Log::error($request->app_type . "  Failed to force update: " . $e->getMessage());
            return $this->error($ErrorsMessage, 500);
        }
    }
}
