<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateDeviceDetailsRequest;
// use App\Models\DeviceDetail;
use App\Models\DeviceDetail;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
class DeviceDetailController extends Controller
{
    public function __construct()
{
    $this->middleware('validateUserId');
}
    /**
     * Update or create device details for a user.
     *
     * @param  UpdateDeviceDetailsRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserDeviceInfo(UpdateDeviceDetailsRequest $request)
    {
        try {
            // $decryptedId = Crypt::decrypt($request->UserId);
           $user_id = $request->UserId;
            $deviceDetailsData = [
                'type' => $request->Type,
                'app_version' => $request->AppVersion,
                'version_code' => $request->VersionCode,
                'os_version' => $request->OSVersion,
                'device' => $request->Device,
            ];

            $updateStatus = DeviceDetail::updateOrCreate(
                ['user_id' => $user_id],
                $deviceDetailsData
            );
            if (!$updateStatus) {
                return $this->error('Oops , Device details not updated.', 400);
            }
            return $this->response($updateStatus,"Device details updated successfully");
        } catch (\Exception $e) {
            Log::error("Failed to update device details: " . $e->getMessage());
            return $this->error('Failed to update device details', 500);
        }
    }


}
