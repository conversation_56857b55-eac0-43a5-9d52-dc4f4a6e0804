<?php

namespace App\Http\Controllers\API\Vendor\Pullout;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\AssetTaskPlacement\AssetPlacementTaskDeploymentRequest;
use App\Http\Requests\Vendor\AssetTaskPlacement\CompletePlacemementDetailsRequest;
use App\Http\Requests\Vendor\AssetTaskPlacement\VendorVerifyChillerSerialNumberRequest;
use App\Http\Requests\Vendor\AssetTaskPullout\AssetPulloutTaskDeploymentRequest;
use App\Http\Requests\Vendor\AssetTaskPullout\CompletePulloutDetailsRequest;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPulloutRequest;
use App\Models\AssetTaskPlacement\AssetPlacementTaskDeploymentModel;
use App\Models\AssetTaskPullout\AssetPulloutTaskDeploymentModel;
use App\Models\AsssetInventory;
use App\Models\OutletAssetDetailsModel;
use App\Services\API\AssetRequestService;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VendorPulloutTaskController extends Controller
{

    public function VendorPulloutTaskList(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $assetPlacementList = AssetRequestService::assetRequestList($request, $user, 'pullout');
            $responseData = $assetPlacementList->map(function ($item) {
                #assigned asset
                $vendor_approval = $item->approvalHistory->filter(function ($record) {
                    return $record['user_role'] === 'VENDOR';
                })->first();
                $vendor_approval_created_at = $vendor_approval ? $vendor_approval->action_updated_time : null;
                return [
                    'OutletId' => $item->outlet->id ?? '',
                    'OutletCode' => $item->outlet_code ?? '',
                    'RequestNo' => $item->pullout_request_number,
                    'CustomerName' => $item->outlet->user->name ?? '',
                    'CustomerCode' => $item->outlet->user->user_id ?? '',
                    'CustomerAddress' => $item->outlet->user->address_1 ?? '',
                    'CustomerEmail' => $item->outlet->user->email ?? '',
                    'ContactNumber' => $item->outlet->user->mobile_number ?? '',
                    'ContactPerson' => $item->outlet->contact_name,
                    'ContactEmail' => $item->email ?? '',
                    'DSRName' => $item->outlet->salesman_name,
                    'RouteName' => $item->outlet->route_name,
                    'AssetNumber' => $item->asset_serial_number ?? null,
                    'AssetBarcode' => $item->asset_barcode ?? null,
                    'ChillerType' => $item->pullout_asset_type,
                    'AssetTypeCode' => $item->asset_type_code,
                    'PulloutReason' => $item->pullout_reason,
                    'CompletionDate' => $item->pullout_complete_date ? date("Y-m-d", strtotime($item->pullout_complete_date)) : null,
                    'Status' => $item->task_status,
                    'CreatedDate' => $item->created_at,
                    'ExpectedDate' => $item->expected_pullout_date ?? '',
                    'ChillerPlaced' => $item->pullout_placed,
                    'Latitude' => $item->latitude,
                    'Longitude' => $item->longitude,
                    'CustomerLocation' => $item->customer_location,
                    'AssignedDate' => $vendor_approval_created_at,
                ];
            });
            return $this->response($responseData, 'Pullout Task Requst list get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : VendorPulloutTaskController data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('VendorPulloutTaskController Failed to get Task placement request list', 500);
        }
    }

    public function submitVendorPulloutTask(AssetPulloutTaskDeploymentRequest $request)
    {
        DB::beginTransaction();
        try {
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $validatedData['completion_date'] = now()->toDateString();
            $existingRecord = AssetPulloutTaskDeploymentModel::where('user_id', $validatedData['user_id'])
                ->where('pullout_request_number', $validatedData['pullout_request_number'])
                ->first();
            if ($existingRecord) {
                return $this->error('Task Pullout Request already completed of this Placement Request',409);
            }


            #handle Hierarchy
            $heirarchy = PlacementRequestService::outletHeirarchy($validatedData['outlet_id']);
            $heirarchyUser = $heirarchy->user ?? '';
            $distributor = $heirarchy->user->distributor ?? '';
            $validatedData['ae_code'] = $heirarchyUser->ae_id ?? '';
            $validatedData['rsm_code'] = $heirarchyUser->rsm_id ?? '';
            $validatedData['asm_code'] = $heirarchyUser->asm_id ?? '';
            $validatedData['so_code'] = $heirarchyUser->so_id ?? '';
            $validatedData['db_code'] = $heirarchyUser->distributor_id ?? '';
            $validatedData['dsr_code'] = $heirarchy->salesman_code ?? '';
            $validatedData['cfa_code'] = $distributor->cfa_code ?? '';
            $validatedData['scanned_bar_code'] = $validatedData['asset_barcode'] ?? '';

            $responseData = AssetPulloutTaskDeploymentModel::create($validatedData);
            if (!$responseData) {
                return $this->error('Failed to store Task Pullout Request, please try again');
            }
            #Updatr Asset Pullout Request data
            $this->updateAPRData($validatedData, $user);

            #assignes asset fot this  request if chiller place YES;
            if ($validatedData['pullout_placed'] == "Yes") {
                AsssetInventory::where('serial_number', $validatedData['asset_number'])->update(['assigned_status' => 'No']);
            }
            $status = $validatedData['pullout_placed'] == "Yes" ? "Approved" : "Rejected";
            $TaskStatusService = app(TaskStatusService::class);
            $TaskStatusService->approvalHistoryUpdate($validatedData['pullout_request_number'], $status, 'api');
            DB::commit(); // Commit the transaction
            return $this->response([], 'Task Completed successfully');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : submitPlacementTask data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to store Task placement', 400);
        }
    }

    public function completePulloutTaskDetails(CompletePulloutDetailsRequest $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $RequestNumber = $validatedData['RequestNumber'];
            $pullout = AssetPulloutRequest::where([
                'assigned_organization' => $user->user_id,
                'pullout_request_number' => $RequestNumber,
            ])
            ->with(['outlet','outletUser', 'taskDeploy'])
            ->first();
            if (!$pullout) {
                return $this->error('Pullout request not found or not assigned to your organization.', 400);
            }
            $placementData = [
                'OutletId' => $pullout->outlet_id ?? '',
                'RequestNo' => $pullout->pullout_request_number,
                'CompletionDate' => $pullout->pullout_complete_date ? date("Y-m-d", strtotime($pullout->pullout_complete_date)) : null,
                'Status' => $pullout->task_status,
                'PulloutPlaced' => $pullout->pullout_placed,
                'CustomerCode' => $pullout->outlet_code ?? '',
                'CustomerName' => $pullout->outletUser->name ?? '',
                'ContactPerson' => $pullout->outlet->contact_name??'',
                'ContactNumber' => $pullout->outletUser->mobile_number ?? '',
                'PulledAssetBarcode' => $pullout->asset_barcode ?? null,
                'PulledoutAssetNumber' => $pullout->asset_serial_number ?? null,
                'AssetBarcode' => $pullout->taskDeploy->scanned_bar_code ?? null,
                'AssetNumber' => $pullout->taskDeploy->asset_number ?? null,
                'PulloutReason' => $pullout->pullout_reason ?? '',
                'ContactEmail' => $pullout->email ?? '',
                'OutletPhoto' => !empty($pullout->taskDeploy->outlet_photo) ? asset($pullout->taskDeploy->outlet_photo) : null,
                'RetailerPhoto' => !empty($pullout->taskDeploy->retailer_photo) ? asset($pullout->taskDeploy->retailer_photo) : null,
                'PulloutChillerType' => $pullout->pullout_asset_type,
                'ChillerImage' => !empty($pullout->taskDeploy->chiller_image) ? asset($pullout->taskDeploy->chiller_image) : null,
                'CustomerLocation' => $pullout->customer_location,
                'CurrentLocation' => $pullout->current_location,
                'Remarks' => $pullout->taskDeploy->remarks ?? '',
                'Distance' => $pullout->taskDeploy->distance ?? '',
            ];
            return $this->response($placementData, 'Task Pullout Requst details get successfully');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : completePulloutTaskDetails data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('completePulloutTaskDetails Failed to get completed placement details', 500);
        }
    }

    private function updateAPRData($validatedData, $user)
    {
        $is_deploy = $validatedData['pullout_placed'] == "Yes" ? true : false;
        $res = AssetPulloutRequest::where('pullout_request_number', $validatedData['pullout_request_number'])->update(
            [
                'approved_by_user_role' => $user->user_type,
                'approved_by_user_id' => $user->user_id,
                'pullout_placed' => $validatedData['pullout_placed'],
                'is_pullout' => $is_deploy,
                'pullout_complete_date' => $validatedData['completion_date'],
                'pullout_request_complete' => strtolower($validatedData['pullout_placed']),
                'task_status' => 'Completed',
            ]
        );
        return $res;
    }
}
