<?php

namespace App\Http\Controllers\API\Vendor\Placement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\AssetTaskPlacement\AssetPlacementTaskDeploymentRequest;
use App\Http\Requests\Vendor\AssetTaskPlacement\CompletePlacemementDetailsRequest;
use App\Http\Requests\Vendor\AssetTaskPlacement\VendorVerifyChillerSerialNumberRequest;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\AssetPulloutRequest;
use App\Models\AssetReplacementRequest;
use App\Models\AssetTaskPlacement\AssetPlacementTaskDeploymentModel;
use App\Models\AsssetInventory;
use App\Models\MaintenanceRequest;
use App\Models\OutletAssetDetailsModel;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VendorPlacementTaskController extends Controller
{

    public function VendorPlacementTaskList(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $fromDate = $request->input('FromDate');
            $toDate = $request->input('ToDate');
            $searchKeyword = $request->input('Keyword');
            $taskStatus = $request->input('task_status') ? strtolower($request->input('task_status')) : '';
            $offset = $request->input('Offset', 0);
            $limit = $request->input('Limit', 10);

            $query = AssetPlacementRequest::where('assigned_organization', $user->user_id)->where('asset_assigned_status', '!=', 'Rejected')
                ->with(['outlet.user', 'approvalHistory', 'placementAssets', 'taskDeploy']);

            if ($taskStatus === 'completed') {
                $query->whereHas('taskDeploy', function ($query) {
                    $query->latest('completion_date');
                });
            } else {
                $query->latest('created_at');
            }

            if ($fromDate) {
                $query->whereDate('created_at', '>=', Carbon::parse($fromDate));
            }
            if (!empty($taskStatus)) {
                $placementStatus = $taskStatus == 'completed' ? "Completed" : "Pending";
                // $query->where('is_deploy', $placementStatus);
                $query->where('task_status', $placementStatus);
            }
            if ($toDate) {
                $query->whereDate('created_at', '<=', Carbon::parse($toDate));
            }

            if ($searchKeyword) {
                $query->where(function ($query) use ($searchKeyword) {
                    $query->whereHas('outlet.user', function ($query) use ($searchKeyword) {
                        $query->where('name', 'like', '%' . $searchKeyword . '%')
                            ->orWhere('user_id', 'like', '%' . $searchKeyword . '%');
                    })
                        ->orWhere('request_number', 'like', '%' . $searchKeyword . '%');
                });
            }

            $assetPlacementList = $query->latest()->offset($offset)
                ->limit($limit)->get();
            $responseData = $assetPlacementList->map(function ($item) {
                #assigned asset
                $asset_detail = $item->placementAssets;
                $vendor_approval = $item->approvalHistory->filter(function ($record) {
                    return $record['user_role'] === 'VENDOR';
                })->first();
                $vendor_approval_created_at = $vendor_approval ? $vendor_approval->action_updated_time : null;
                return [
                    'RequestId' => $item->id,
                    'RequestNo' => $item->request_number,
                    'OutletId' => $item->outlet->id ?? '',
                    'CustomerName' => $item->outlet->user->name ?? '',
                    'CustomerNumber' => $item->outlet->user->mobile_number ?? '',
                    'CustomerEmail' => $item->outlet->user->email ?? '',
                    'CustomerCode' => $item->outlet->user->user_id ?? '',
                    'CustomerAddress' => $item->outlet->invoiceaddress,
                    'DSRName' => $item->outlet->salesman_name,
                    'RouteName' => $item->outlet->route_name,
                    'ChillerType' => $item->eligible_chiller_type,
                    "AssetTypeCode" => $item->asset_type_code ?? '',
                    'ExpectedDate' => $item->expected_deployment_date??$item->updated_at,
                    'Status' => $item->task_status,
                    'ChillerPlaced' => $item->chiller_placed,
                    'CompletionDate' => $item->deployment_date ? date("Y-m-d", strtotime($item->deployment_date)) : null,
                    'AssetBarcode' => $asset_detail->asset_barcode ?? null,
                    'AssetNumber' => $asset_detail->asset_number ?? null,
                    'Latitude' => $item->latitude,
                    'Longitude' => $item->longitude,
                    'CustomerLocation' => $item->customer_location,
                    'AssignedDate' => $vendor_approval_created_at,
                ];
            });
            return $this->response($responseData, 'Task Placement Requst list get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : getAsseetPlacementRequestList data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get Task placement request list', 500);
        }
    }

    public function submitPlacementTask(AssetPlacementTaskDeploymentRequest $request)
    {
        DB::beginTransaction(); // Start a database transaction
        try {
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $validatedData['completion_date'] = now()->toDateString();
            $existingRecord = AssetPlacementTaskDeploymentModel::where('user_id', $validatedData['user_id'])
                ->where('request_number', $validatedData['request_number'])
                ->first();
            // If a record already exists, return an error response
            if ($existingRecord) {
                return $this->error('Task Placement Request already completed of this Placement Request');
            }


            #handle Hierarchy
            $heirarchy = PlacementRequestService::outletHeirarchy($validatedData['outlet_id']);
            $heirarchyUser = $heirarchy->user ?? '';
            $distributor = $heirarchy->user->distributor ?? '';
            $validatedData['ae_code'] = $heirarchyUser->ae_id ?? '';
            $validatedData['rsm_code'] = $heirarchyUser->rsm_id ?? '';
            $validatedData['asm_code'] = $heirarchyUser->asm_id ?? '';
            $validatedData['so_code'] = $heirarchyUser->so_id ?? '';
            $validatedData['db_code'] = $heirarchyUser->distributor_id ?? '';
            $validatedData['dsr_code'] = $heirarchy->salesman_code ?? '';
            $validatedData['cfa_code'] = $distributor->cfa_code ?? '';

            $responseData = AssetPlacementTaskDeploymentModel::create($validatedData);
            if (!$responseData) {
                return $this->error('Failed to store Task placement, please try again');
            }
            #Updatr APR data
            $this->updateAPRData($validatedData, $user);

            #assignes asset fot this  request if chiller place YES;
            if ($validatedData['chiller_placed'] == "Yes") {
                AsssetInventory::where('serial_number', $validatedData['asset_number'])->update(['assigned_status' => 'yes']);
                #assigned asset
                $this->assignAssetOutletHistory($existingRecord, $validatedData, $user);
            }
            $status = $validatedData['chiller_placed'] == "Yes" ? "Approved" : "Rejected";
            $TaskStatusService = app(TaskStatusService::class);
            $TaskStatusService->approvalHistoryUpdate($validatedData['request_number'], $status, 'api');
            DB::commit(); // Commit the transaction
            return $this->response([], 'Task Completed successfully');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : submitPlacementTask data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to store Task placement', 500);
        }
    }

    public function completePlacementTaskDetails(CompletePlacemementDetailsRequest $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $RequestNumber = $validatedData['RequestNumber'];
            $placement = AssetPlacementRequest::where(['assigned_organization' => $user->user_id, 'request_number' => $RequestNumber])
                ->with(['outlet.user', 'approvalHistory', 'placementAssets', 'taskDeploy'])
                ->first();
            $asset_detail = $placement->placementAssets;
            $vendor_approval = $placement->approvalHistory->filter(function ($record) {
                return $record['user_role'] === 'VENDOR';
            })->first();
            $vendor_approval_created_at = $vendor_approval ? $vendor_approval->action_updated_time : null;
            $placementData = [
                'RequestId' => $placement->id,
                'RequestNo' => $placement->request_number,
                'OutletId' => $placement->outlet->id ?? '',
                'ContactEmail' => $placement->outlet->user->email ?? '',
                'CustomerName' => $placement->outlet->user->name ?? '',
                'CustomerCode' => $placement->outlet->user->user_id ?? '',
                'ContactNumber' => $placement->outlet->user->mobile_number ?? '',
                'ContactPerson' => $placement->outlet->contact_name,
                'CustomerAddress' => $placement->outlet->invoiceaddress,
                'DSRName' => $placement->outlet->salesman_name,
                'RouteName' => $placement->outlet->route_name,
                'ChillerType' => $placement->eligible_chiller_type,
                'ExpectedDate' => $placement->expected_deployment_date??$placement->updated_at,
                'Status' => $placement->task_status,
                'ChillerPlaced' => $placement->chiller_placed,
                'CompletionDate' => $placement->deployment_date ? date("Y-m-d", strtotime($placement->deployment_date)) : null,
                'AssetBarcode' => $asset_detail->asset_barcode ?? null,
                'AssetNumber' => $asset_detail->asset_number ?? null,
                'Latitude' => $placement->taskDeploy->latitude ?? '',
                'Longitude' => $placement->taskDeploy->longitude ?? '',
                'CustomerLocation' => $placement->customer_location,
                'CurrentLocation' => $placement->taskDeploy->current_location,
                'Remarks' => $placement->taskDeploy->remarks ?? '',
                'Distance' => $placement->taskDeploy->distance ?? '',

                'AssignedDate' => $vendor_approval_created_at,
                'OutletPhoto' => !empty($placement->taskDeploy->outlet_photo) ? asset($placement->taskDeploy->outlet_photo) : null,
                'RetailerPhoto' => !empty($placement->taskDeploy->retailer_photo) ? asset($placement->taskDeploy->retailer_photo) : null,
                'ChillerImage' => !empty($placement->taskDeploy->chiller_image) ? asset($placement->taskDeploy->chiller_image) : null,
                'InstallationReceipt' => !empty($placement->taskDeploy->installation_receipt) ? asset($placement->taskDeploy->installation_receipt) : null,
            ];
            return $this->response($placementData, 'Task Placement Requst list get successfully');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : completePlacementTaskDetails data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get completed placement details', 500);
        }
    }

    public function GetVendorDashboardDetails()
    {
        try {
            $user = Auth::guard('api')->user();
            $PendingPlacements = AssetPlacementRequest::selectRaw('
            SUM(CASE WHEN task_status = "Pending" AND asset_assigned_status != "Rejected" THEN 1 ELSE 0 END) as pending_task
        ')->where('assigned_organization', $user->user_id)->first();

            $PendingPull = AssetPulloutRequest::selectRaw('
            SUM(CASE WHEN task_status = "Pending" AND asset_assigned_status != "Rejected" THEN 1 ELSE 0 END) as pending_task
        ')->where('assigned_organization', $user->user_id)->first();

        $MaintenanceRequest = MaintenanceRequest::selectRaw('
            SUM(CASE WHEN task_status = "Pending" AND asset_assigned_status != "Rejected" THEN 1 ELSE 0 END) as pending_task
        ')->where('assigned_organization', $user->user_id)->first();

        $replacemmentRequest = AssetReplacementRequest::selectRaw('
            SUM(CASE WHEN task_status = "Pending" AND asset_assigned_status != "Rejected" THEN 1 ELSE 0 END) as pending_task
        ')->where('assigned_organization', $user->user_id)->first();

            $dashboardCount = [
                'PendingPlacements' => $PendingPlacements->pending_task ?? 0,
                'PendingReplacements' => $replacemmentRequest->pending_task,
                'PendingPull' => $PendingPull->pending_task ?? 0,
                'PendingMaintenance' => $MaintenanceRequest->pending_task ?? 0,
                'PendingTransfer' => 0
            ];
            return $this->response($dashboardCount, 'Vendor Dashboard details get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : GetVendorDashboardDetails data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to Get Vendor Dashboard details', 500);
        }
    }


    public function VendorVerifyChillerSerialNumber(VendorVerifyChillerSerialNumberRequest $request)
    {
        try {
            $serialNumber = $request->input('serialNumber');
            $chillerType = $request->input('chillerType');
            $user = Auth::guard('api')->user();
            $assetInventory = AsssetInventory::where(
                [
                    'serial_number' => $serialNumber,
                    'warehouse_code' => $user->cfa_code,
                    'asset_type' => $chillerType,
                    'assigned_status' => 'no',
                    'asset_approval_status' => 'approved',
                ]
            )->first();
            $VerifyStatus = $assetInventory ? true : false;
            return $this->response(['serialNumberstatus' => $VerifyStatus], 'Asset Serial number verification');
        } catch (\Exception $exception) {
            Log::error('Error : VendorVerifyChillerSerialNumber data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get VendorVerifyChillerSerialNumber request list', 500);
        }
    }

    private function assignAssetOutletHistory($existingRecord, $validatedData, $user)
    {
        $apr = AssetPlacementRequest::where('request_number', $validatedData['request_number'])->first();
        $assignedAssetData = [
            'asset_description' => $apr->eligible_chiller_type ?? '',
            'asset_barcode' => $validatedData['asset_barcode'] ?? '',
            'asset_number' => $validatedData['asset_number'] ?? '',
            'outlet_code' => $validatedData['outlet_code'],
            'placement_request_number' => $validatedData['request_number'],
            'date_of_placement' => $apr->created_at ?? '',
            'asset_assigned_by' => $user->user_id,
            'asset_assigned_time' => now(),
            'last_audit_time' => now(),
        ];
        $res = OutletAssetDetailsModel::updateOrCreate(
            [
                'asset_number' => $validatedData['asset_number'],
                'placement_request_number' => $validatedData['request_number']
            ],
            $assignedAssetData
        );

        return $res;
    }

    private function updateAPRData($validatedData, $user)
    {
        $is_deploy = $validatedData['chiller_placed'] == "Yes" ? true : false;
        $res = AssetPlacementRequest::where('request_number', $validatedData['request_number'])->update(
            [
                'approved_by_user_role' => $user->user_type,
                'approved_by_user_id' => $user->user_id,
                'is_deploy' => $is_deploy,
                'deployment_date' => $validatedData['completion_date'],
                'chiller_placed' => $validatedData['chiller_placed'],
                'task_status' => 'Completed',
                'asset_barcode' => $validatedData['asset_barcode'] ?? '',
                'asset_serial_number' => $validatedData['asset_number'] ?? '',
            ]
        );
        return $res;
    }
}
