<?php

namespace App\Http\Controllers\API\Vendor\Maintenance;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\Maintenance\MaintenanceTaskDetailsRequest;
use App\Http\Requests\Vendor\Maintenance\StoreMaintenanceRequest;
use App\Models\AssetMaintenanceTaskRepairModel;
use App\Models\AssetPulloutRequest;
use App\Models\MaintenanceRequest;
use App\Services\API\AssetRequestService;
use App\Services\API\Maintenance\MaintenanceTaskAPIService;
use App\Services\HeirarchyService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class VendorMaintenanceTaskController extends Controller
{

    public function submitVendorMaintenanceTask(StoreMaintenanceRequest $request)
    {
        DB::beginTransaction();
        try {
            Log::info('RequestData : submitVendorMaintenanceTask data', $request->all());
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $validatedData['completion_date'] = now()->toDateString();
            $existingRecord = AssetMaintenanceTaskRepairModel::where('user_id', $validatedData['user_id'])
                ->where('request_number', $validatedData['request_number'])
                ->first();
            if ($existingRecord) {
                return $this->error('Repaire Maintenance Request already completed', 409);
            }

            #handle Hierarchy
            $heirarchy = HeirarchyService::gelRetailterHeirarchyList($validatedData['outlet_id']);

            $validatedData['scanned_bar_code'] = $validatedData['asset_barcode'] ?? '';
            $validatedData['correct_location'] = $validatedData['correct_location'] ?? 'No';

            $validatedData = array_merge($validatedData, $heirarchy);
            Log::info('RequestData : submitVendorMaintenanceTask stored data', $validatedData);
            $responseData = AssetMaintenanceTaskRepairModel::create($validatedData);
            if (!$responseData) {
                return $this->error('Failed to store Maintenance Task Complete Request, please try again');
            }
            #Updatr Asset Pullout Request data
            $this->updateAPRData($validatedData, $user);

            $status = $validatedData['maintenance_asset'] == "Yes" ? "Approved" : "Rejected";
            $TaskStatusService = app(TaskStatusService::class);
            $TaskStatusService->approvalHistoryUpdate($validatedData['request_number'], $status, 'api');
            DB::commit(); // Commit the transaction
            return $this->response([], 'Task Completed successfully');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : submitMaintenanceTask data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to submitMaintenanceTask', 400);
        }
    }

    public function VendorMaintenanceTaskList(Request $request)
    { {
            try {
                Log::info('RequestData : VendorMaintenanceTaskList Request data', $request->all());
                $user = Auth::guard('api')->user();
                $assetPlacementList = MaintenanceTaskAPIService::maintenanceTaskRequestList($request, $user);
                return $this->response($assetPlacementList, 'Maintenance Repair Task Requst list get successfully');
            } catch (\Exception $exception) {
                Log::error('Error : VendorMaintenanceTaskList data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
                return $this->error('VendorMaintenanceTaskList Failed to get Task placement request list', 500);
            }
        }
    }

    public function VendorMaintenanceTaskDetails(MaintenanceTaskDetailsRequest $request)
    {
        DB::beginTransaction();
        try {
            Log::info('RequestData : VendorMaintenanceTaskDetails request data', $request->all());
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $assetPlacementList = MaintenanceTaskAPIService::maintenanceTaskRequestDetails($validatedData, $user);
            return $this->response($assetPlacementList, 'Vendor Maintenance Request details');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : VendorMaintenanceTaskDetails data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get Vendor Maintenance Task Details', 400);
        }
    }

    private function updateAPRData($validatedData, $user)
    {
        $is_maintenance = $validatedData['maintenance_asset'] == "Yes" ? true : false;

        $updateData = [
            'approved_by_user_role' => $user->user_type,
            'approved_by_user_id' => $user->user_id,
            'is_maintenance' => $is_maintenance,
            'repair_status' => $validatedData['maintenance_asset'],
            'maintenance_complete_date' => $validatedData['completion_date'],
            'task_status' => 'Completed',
        ];
        // Only add images to the update data if they exist in the validated data
        if (!empty($validatedData['chiller_image'])) {
            $updateData['chiller_image'] = $validatedData['chiller_image'];
        }

        if (!empty($validatedData['retailer_photo'])) {
            $updateData['retailer_photo'] = $validatedData['retailer_photo'];
        }

        if (!empty($validatedData['outlet_photo'])) {
            $updateData['outlet_photo'] = $validatedData['outlet_photo'];
        }
        $res = MaintenanceRequest::where('maintenance_request_number', $validatedData['request_number'])->update($updateData);
        return $res;
    }
}
