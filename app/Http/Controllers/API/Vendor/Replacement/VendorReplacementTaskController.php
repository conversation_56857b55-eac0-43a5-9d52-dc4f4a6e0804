<?php

namespace App\Http\Controllers\API\Vendor\Replacement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\Replacement\ReplacementTaskDetailsRequest;
use App\Http\Requests\Vendor\Replacement\StoreReplacementSubmitRequest;
use App\Models\AssetPlacementRequest;
use App\Models\AssetReplacementRequest;
use App\Models\AssetTaskRePlacement;
use App\Models\AsssetInventory;
use App\Models\OutletAssetDetailsModel;
use App\Services\API\Replacement\ReplacementRequestAPIService;
use App\Services\HeirarchyService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class VendorReplacementTaskController extends Controller
{

    public function submitVendorReplacementTask(StoreReplacementSubmitRequest $request)
    {
        DB::beginTransaction();
        try {
            Log::info('RequestData : submitVendorReplacementTask data', $request->all());
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $validatedData['completion_date'] = now()->toDateString();
            $validatedData['replacement_complete_date'] =  $validatedData['completion_date'];
            $existingRecord = AssetTaskRePlacement::where('user_id', $validatedData['user_id'])
                ->where('request_number', $validatedData['request_number'])
                ->first();
            if ($existingRecord) {
                return $this->error('Asset Replacement Request already completed', 409);
            }
            $heirarchy = HeirarchyService::gelRetailterHeirarchyList($validatedData['outlet_id']);
            $validatedData['replaced_asset_barcode'] = $validatedData['scanned_barcode'] ?? 'No';
            $validatedData = array_merge($validatedData, $heirarchy);

            #create a log
            Log::info('RequestData : submitVendorReplacementTask stored data', $validatedData);

            $responseData = AssetTaskRePlacement::create($validatedData);
            if (!$responseData) {
                return $this->error('Failed to store Replaceent Task Complete Request, please try again');
            }
            $this->updateAPRData($validatedData, $user);
            if ($validatedData['chiller_placed'] == "Yes") {
                #previous asset reoved
                AsssetInventory::where('serial_number', $validatedData['prev_assigned_asset_number'])->update(['assigned_status' => 'no']);

                #update Asset Placeent Request
                AssetPlacementRequest::where('asset_serial_number', $validatedData['prev_assigned_asset_number'])->update([
                    'asset_barcode' => $validatedData['scanned_barcode'] ?? '',
                    'asset_serial_number' => $validatedData['replaced_asset_number'] ?? '',
                ]);

                #new Asset assigned
                AsssetInventory::where('serial_number', $validatedData['replaced_asset_number'])->update(['assigned_status' => 'yes']);
                #assigned asset
                $this->assignAssetOutletHistory($existingRecord, $validatedData, $user);
            }
            $status = $validatedData['chiller_placed'] == "Yes" ? "Approved" : "Rejected";
            $TaskStatusService = app(TaskStatusService::class);
            $TaskStatusService->approvalHistoryUpdate($validatedData['request_number'], $status, 'api');
            DB::commit(); // Commit the transaction
            return $this->response([], 'Asset Replacement Task Completed successfully');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : submitVendorReplacementTask data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to submitVendorReplacementTask', 400);
        }
    }

    private function updateAPRData($validatedData, $user)
    {
        $is_deploy = $validatedData['chiller_placed'] == "Yes" ? true : false;
        $res = AssetReplacementRequest::where('request_number', $validatedData['request_number'])->update(
            [
                'approved_by_user_role' => $user->user_type,
                'approved_by_user_id' => $user->user_id,
                'is_deploy' => $is_deploy,
                'deployment_date' => $validatedData['completion_date'],
                'replacement_complete_date' => $validatedData['completion_date'],
                'chiller_placed' => $validatedData['chiller_placed'],
                'task_status' => 'Completed',
            ]
        );
        return $res;
    }

    private function assignAssetOutletHistory($existingRecord, $validatedData, $user)
    {
        $apr = AssetReplacementRequest::where('request_number', $validatedData['request_number'])->first();
        $assignedAssetData = [
            'asset_description' => $apr->request_chiller_type ?? '',
            'asset_barcode' => $validatedData['scanned_barcode'] ?? '',
            'asset_number' => $validatedData['replaced_asset_number'] ?? '',
            'outlet_code' => $validatedData['outlet_code'],
            'placement_request_number' => $validatedData['placement_request_number'],
            'date_of_placement' => $apr->created_at ?? '',
            'asset_assigned_by' => $user->user_id,
            'asset_assigned_time' => now(),
            'last_audit_time' => now(),
        ];
        $res = OutletAssetDetailsModel::updateOrCreate(
            [
                'asset_number' => $validatedData['replaced_asset_number'],
                'placement_request_number' => $validatedData['placement_request_number']
            ],
            $assignedAssetData
        );

        return $res;
    }

    public function VendorReplacementTaskList(Request $request)
    {
        try {
            Log::info('RequestData : VendorReplacementTaskList Request data', $request->all());
            $user = Auth::guard('api')->user();
            $assetPlacementList = ReplacementRequestAPIService::replacementTaskRequestList($request, $user);
            return $this->response($assetPlacementList, 'Replacement Task Requst list get successfully');
        } catch (\Exception $exception) {
            Log::error('Error : VendorReplacementTaskList data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('VendorReplacementTaskList Failed to get Task Re-placement request list', 500);
        }
    }

    public function VendorReplacementTaskListDetails(ReplacementTaskDetailsRequest $request)
    {
        DB::beginTransaction();
        try {
            Log::info('RequestData : VendorReplacementTaskListDetails request data', $request->all());
            $user = Auth::guard('api')->user();
            $validatedData = $request->validated();
            $assetPlacementList = ReplacementRequestAPIService::ReplacementTaskRequestDetails($validatedData, $user);
            return $this->response($assetPlacementList, 'Vendor Replacement Request details');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : VendorReplacementTaskListDetails data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to get Vendor Replacement Task Details', 400);
        }
    }
}
