<?php

namespace App\Http\Controllers\API\Maintenance;

use App\Http\Controllers\Controller;
use App\Http\Requests\Maintenance\MaintenanceRequestDetails;
use App\Http\Requests\Maintenance\MaintenanceRequestStoreRequest;
use App\Models\MaintenanceRequest;
use App\Services\API\Maintenance\MaintenanceRequestAPIService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MaintenanceRequestController extends Controller
{
    public function storeMaintenanceRequest(MaintenanceRequestStoreRequest $request)
    {
        try {
            Log::info('RequestData : storeMaintenanceRequest data', $request->all());

            $validatedData = $request->validated();

            $hierarchyList = PlacementRequestService::outletHeirarchy($validatedData['outlet_id']);
            $maintenance_request_number = PlacementRequestService::generateRequestNumber('AMR');
            $hierarchyUser = $hierarchyList->user ?? '';
            $distributor = $hierarchyList->user->distributor ?? '';

            #Hierarchy User
            $validatedData['outlet_code'] = $validatedData['customer_code'];
            $validatedData['ae_code'] = $hierarchyUser->ae_id ?? '';
            $validatedData['rsm_code'] = $hierarchyUser->rsm_id ?? '';
            $validatedData['asm_code'] = $hierarchyUser->asm_id ?? '';
            $validatedData['so_code'] = $hierarchyUser->so_id ?? '';
            $validatedData['db_code'] = $hierarchyUser->distributor_id ?? '';
            $validatedData['dsr_code'] = $hierarchyList->salesman_code ?? '';
            $validatedData['cfa_code'] = $distributor->cfa_code ?? '';
            $validatedData['pending_from'] = 'ASM';
            $validatedData['maintenance_request_number'] = $maintenance_request_number;
            Log::info('storeMaintenanceRequest stored data', $validatedData);
            $maintenanceRequest = MaintenanceRequest::create($validatedData);
            if (!$maintenanceRequest) {
                return $this->error('Failed to create asset maintenance request, please try again', 400);
            }
            return $this->response(["MaintenanceRequestNumber" => $maintenance_request_number], 'Asset Maintenance Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : storeMaintenanceRequest data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to create asset Maintenance request', 400);
        }
    }

    public function GetAssetMaintenanceRequestList(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            Log::info("GetAssetMaintenanceRequest", [$request->all()]);

            $userId = $request->input('user_id');
            $type = $request->input('type');
            $status = $request->input('status');
            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 10);
            $fromDate = $this->parseDate($request->input('from_date'));
            $toDate = $this->parseDate($request->input('to_date'));

            $query = MaintenanceRequestAPIService::buildQuery($userId, $type, $status, $fromDate, $toDate);

            $maintenanceRequests = $query->orderBy('created_at', 'DESC')
                ->offset($offset)
                ->limit($limit)
                ->get();

            $data = MaintenanceRequestAPIService::formatMaintenanceRequests($maintenanceRequests);
            return $this->response($data, 'Asset Maintenance Request list.');
        } catch (\Throwable $th) {
            Log::error("Error in GetAssetMaintenanceRequest", [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString()
            ]);
            return $this->error("Something went wrong in GetAssetMaintenanceRequest");
        }
    }

    public function GetAssetMaintenanceRequestListDetails(MaintenanceRequestDetails $request): \Illuminate\Http\JsonResponse
    {
        try {
            Log::info("GetAssetMaintenanceRequestListDetails", [$request->all()]);
            $validatedData = $request->validated();
            $maintenanceRequestListDetails = MaintenanceRequestAPIService::getMaintenanceRequestDetails($validatedData);
            return $this->response($maintenanceRequestListDetails, 'Asset Maintenance Request list.');
        } catch (\Throwable $th) {
            Log::error("Error in GetAssetMaintenanceRequestListDetails", [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString()
            ]);
            return $this->error("Something went wrong in GetAssetMaintenanceRequestListDetails");
        }
    }
    private function parseDate($date)
    {
        return $date ? Carbon::createFromFormat('Y-m-d', $date) : null;
    }


}
