<?php

namespace App\Http\Controllers\API\Maintenance;

use App\Http\Controllers\Controller;
use App\Http\Requests\Maintenance\MaintenanceRequestStoreRequest;
use App\Models\MaintenanceRequest;
use App\Models\RetailerOutlet;
use App\Services\PlacementRequestService\PlacementRequestService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MaintenanceRequestController extends Controller
{
    public function storeMaintenanceRequest(MaintenanceRequestStoreRequest $request)
    {
        try {
            Log::info('RequestData : storeMaintenanceRequest data', $request->all());

            $validatedData = $request->validated();

            $hierarchyList = PlacementRequestService::outletHeirarchy($validatedData['outlet_id']);
            $maintenance_request_number = PlacementRequestService::generateRequestNumber('AMR');
            $hierarchyUser = $hierarchyList->user ?? '';
            $distributor = $hierarchyList->user->distributor ?? '';

            #Hierarchy User
            $validatedData['outlet_code'] = $validatedData['customer_code'];
            $validatedData['ae_code'] = $hierarchyUser->ae_id ?? '';
            $validatedData['rsm_code'] = $hierarchyUser->rsm_id ?? '';
            $validatedData['asm_code'] = $hierarchyUser->asm_code ?? '';
            $validatedData['so_code'] = $hierarchyUser->so_code ?? '';
            $validatedData['db_code'] = $hierarchyUser->distributor_id ?? '';
            $validatedData['dsr_code'] = $hierarchyUser->salesman_code ?? '';
            $validatedData['cfa_code'] = $distributor->cfa_code ?? '';
            $validatedData['pending_from'] = 'ASM';
            $validatedData['maintenance_request_number'] = $maintenance_request_number;
            $maintenanceRequest = MaintenanceRequest::create($validatedData);
            if (!$maintenanceRequest) {
                return $this->error('Failed to create asset maintenance request, please try again', 400);
            }
            return $this->response(["MaintenanceRequestNumber" => $maintenance_request_number], 'Asset Maintenance Request submitted successfully');
        } catch (\Exception $exception) {
            Log::error('Error : storeMaintenanceRequest data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return $this->error('Failed to create asset Maintenance request', 400);
        }
    }

    public function GetAssetMaintenanceRequest(Request $request){
        try {
            Log::info("GetAssetMaintenanceRequest", [$request->all()]);

            $userId = $request->input('user_id');
            $type = $request->input('type');
            $status = $request->input('status');
            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 10);
            $fromDate = $request->input('from_date');
            $toDate = $request->input('to_date');

            $fromDate = $fromDate ? Carbon::createFromFormat('Y-m-d', $fromDate) : null;
            $toDate = $toDate ? Carbon::createFromFormat('Y-m-d', $toDate) : null;

            $query = MaintenanceRequest::where('user_id', $userId)
                ->with(['retaileroutlets:id,route_name,salesman_name'])
                ->where('type', $type)
                ->when($status !== 'All' && $status !== '', function ($query) use ($status) {
                    return $query->where('asset_assigned_status', $status);
                })
                ->when($fromDate && $toDate, function ($query) use ($fromDate, $toDate) {
                    if ($fromDate->eq($toDate)) {
                        $query->whereDate('created_at', $fromDate);
                    } else {
                        $query->whereBetween('created_at', [$fromDate, $toDate]);
                    }
                });

            $posts = $query->orderBy('created_at', 'DESC')
                ->offset($offset)
                ->limit($limit)
                ->get();
            $datas = [];
            if (!empty($posts)) {
                foreach ($posts as $data) {
                    $datas[] = [
//                        "RequestId" => $data->id ?? '',
                        "RequestNumber" => $data->maintenance_request_number ?? '',
                        "AssetNumber" => $data->asset_number ?? '',
                        "CustomerName" => $data->asset_barcode ?? '',
                        "PulloutReason" => $data->maintenance_reason ?? '',
                        "CustomerCode" => $data->outlet_code ?? '',
                        "CustomerAddress" => $data->customer_address ?? '',
                        "DSRName" => $data->retaileroutlets->salesman_name ?? '',
                        "RouteName" => $data->retaileroutlets->route_name ?? '',
                        "ChillerType" => $data->chiller_type ?? '',
                        "DateTime" => $data->created_at->format("Y-m-d H:i:s"),
                        "Status" => $data->asset_assigned_status ?? '',
                    ];
                }
            }
            return $this->response($datas, 'Asset Maintenance Request list.');
        } catch (\Throwable $th) {
            Log::error("Error in GetAssetMaintenanceRequest", ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString()]);
            return $this->error("Something went wrong GetAssetMaintenanceRequest");
        }
    }
}
