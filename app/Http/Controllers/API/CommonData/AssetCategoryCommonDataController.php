<?php

namespace App\Http\Controllers\API\CommonData;

use App\Http\Controllers\Controller;
use App\Models\AssetCommonCategory;
use App\Models\AsssetInventory;
use App\Services\AssetInventoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class AssetCategoryCommonDataController extends Controller
{
    public function __construct()
    {
        $this->middleware('validateUserId');
    }
    public function index()
    {
        try {
            $commonData = AssetCommonCategory::with('assetInventory')->get()->groupBy('type');

            $result = $commonData->map(function ($data) {
                return $data->map(function ($assetCommonCategory) {
                    return ["id" => $assetCommonCategory->id, 'name' => $assetCommonCategory->name, 'type' => $assetCommonCategory->type, 'vpo_target' => $assetCommonCategory->vpo_target, 'inventory_quantity' => $assetCommonCategory->asset_inventory_quantity ?? 0];
                });
            });
            $chillerSizeData = $this->getInventoryList();
            $result['chiller_size'] = $chillerSizeData;
            return $this->response($result, 'Asset Common data retrieved successfully');
        } catch (\Exception $exception) {
            Log::error('Failed to retrieve Asset Common data: ' . $exception->getMessage());
            return $this->error('Failed to retrieve Asset Common data', 500);
        }
    }

    public function getInventoryList()
    {
        $user = Auth::guard('api')->user();
        // Fetch the static asset list
        $validAssets = AssetInventoryService::getAssetList();
        $inventoryList = [];
        foreach ($validAssets as $asset) {
            $assetType = $asset['asset_code'];
            $assetName = $asset['asset_type'];
            $inventoryCount =  DB::table('assets_inventory')->where(['asset_approval_status' => 'approved', 'asset_type_code' => $assetType])->count();

            $inventoryList[] = [
                'id' => $asset['asset_code'], // Asset code used as unique ID
                'name' => $assetName,
                'asset_type_code' => $asset['asset_code'],
                'type' => 'chiller_size', // Static value for type
                'vpo_target' => $asset['vpo'],
                'inventory_quantity' => $inventoryCount, // Dynamic quantity
            ];
        }
        return $inventoryList;
    }
    // public function getInventoryList()
    // {
    //     $user = Auth::guard('api')->user();
    //     // Fetch the static asset list
    //     $validAssets = AssetInventoryService::getAssetList();

    //     // Fetch the inventory counts from the database for approved assets
    //     $inventoryCounts = DB::table('assets_inventory')
    //         ->where('asset_approval_status', 'approved')
    //         ->select('asset_type_code', DB::raw('count(*) as inventory_quantity'))
    //         ->groupBy('asset_type_code')
    //         ->get()
    //         ->keyBy('asset_type_code')
    //         ->toArray();

    //     // Prepare the inventory list combining static assets and inventory counts
    //     $inventoryList = array_map(function ($asset) use ($inventoryCounts) {
    //         $assetType = $asset['asset_code'];

    //         // Find the matching inventory count for this asset type
    //         $inventoryCount = $inventoryCounts[$assetType]->inventory_quantity ?? 0;

    //         return [
    //             'id' => $asset['asset_code'], // Asset code used as unique ID
    //             'name' => $assetType,
    //             'asset_type_code' => $asset['asset_code'],
    //             'type' => 'chiller_size', // Static value for type
    //             'vpo_target' => $asset['vpo'],
    //             'inventory_quantity' => $inventoryCount, // Dynamic quantity
    //         ];
    //     }, $validAssets);

    //     return $inventoryList;
    // }
}
