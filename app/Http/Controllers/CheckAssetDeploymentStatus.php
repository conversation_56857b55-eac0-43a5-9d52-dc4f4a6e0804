<?php
namespace App\Http\Controllers;

use App\Models\AsssetInventory;
use Illuminate\Http\Request;

class CheckAssetDeploymentStatus extends Controller
{
    public function CheckAssetStatus(Request $request) {
        $static_access_token = '66e1ee9824a84';

        // Access the custom access token (whether it's from GET or POST)
        $token = $request->_access_token;

        // Validate the custom access token
        if ($token != $static_access_token) {
            return response('<h1>Invalid Access Token</h1>', 403);
        }

        // Handle POST request for form submission
        if ($request->isMethod('post')) {
            $serial_number = $request->serial_number;

            // Validate serial number
            if (empty($serial_number)) {
                return redirect()->back()->with('message', 'Please Provide Serial Number')->with('status', 'error');
            }

            // Find the asset in the database
            $asset = AsssetInventory::where('serial_number', $serial_number)
                ->with('assetPlacementRequest')
                ->first();

            if (!$asset) {
                return redirect()->back()->with('message', 'Invalid Asset Serial Number')->with('status', 'error');
            }

            // Return asset status view if found
            return view('asset_status', ['asset' => $asset]);
        }

        // Default GET request: show form only
        return view('asset_status');
    }
}

