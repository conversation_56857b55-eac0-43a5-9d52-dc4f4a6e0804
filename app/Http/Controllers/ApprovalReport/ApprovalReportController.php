<?php

namespace App\Http\Controllers\ApprovalReport;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ApprovalReportController extends Controller
{
    public function approvalPlacementReport()
    {
        return view('approvalReport.placement_report');
    }
    public function approvalReplacementReport()
    {
        return view('approvalReport.replacement_report');
    }
    public function approvalPulloutReport()
    {
        return view('approvalReport.pullout_report');
    }
    public function approvalMaintenanceReport()
    {
        return view('approvalReport.maintenance_report');
    }
    public function approvalAuditReport()
    {
        return view('approvalReport.audit_report');
    }
    public function approvalMasterMappingReport()
    {
        return view('approvalReport.asset_master_mapping_report');
    }
    public function approvalCfaWiseReport()
    {
        return view('approvalReport.cfa_wise_inventory_report');
    }
}