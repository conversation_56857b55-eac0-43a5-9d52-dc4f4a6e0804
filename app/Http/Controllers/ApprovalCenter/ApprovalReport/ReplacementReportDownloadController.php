<?php

namespace App\Http\Controllers\ApprovalCenter\ApprovalReport;

use App\Http\Controllers\Controller;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ReplacementReportDownloadController extends Controller
{
    public function RePlacementRequestReportExport(Request $request)
    {
        try {
            $user = Auth::user();

            // Get the query from the service with applied filters
            $assetPlacementListQuery = PlacementRequestService::placementRequestList($request, $user, 'replacement')->latest();
            if (!$assetPlacementListQuery->exists()) {
                return redirect()->back()->with('error', 'No RePlacement Data available');
            }

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="RePlacementRequestReport_' . now()->format('Y_m_d_H_i_s') . '.csv"',
            ];

            $TaskStatusService = app(TaskStatusService::class);

            return response()->stream(function () use ($assetPlacementListQuery, $TaskStatusService) {
                $handle = fopen('php://output', 'w');

                // Output the CSV header
                fputcsv($handle, $this->getRePlacementCsvHeaders());

                // Stream the data from the assetPlacementList query
                $assetPlacementListQuery->chunk(1000, function ($requests) use ($handle, $TaskStatusService) {
                    foreach ($requests as $asset) {
                        fputcsv($handle, $this->RePlacementFormatCsvRow($asset, $TaskStatusService));
                    }
                });

                fclose($handle);
            }, 200, $headers);
        } catch (\Throwable $th) {
            Log::error('Failed to RePlacementRequestReportExport: ' . $th->getMessage());
            return redirect()->back()->with('error', 'Something Went Wrong, Please try again later');
        }
    }

    private function getRePlacementCsvHeaders()
    {
        return [
            'Region',
            'ASM Territory',
            'SO Territory',
            'CFA',
            'Submit Date',
            'Request No',
            'Chiller Type',
            'Applied Chiller Type',
            'Request Status',
            'Request Type',
            'Current VPO',
            'Expected VPO',
            'ASM Name',
            'SO Name',
            'Distributor Name',
            'Distributor Code',
            'Route Name',
            'Customer Code',
            'Customer Name',
            'Customer City Name',
            'Customer Address',
            'Pin Code',
            'Remarks',
            'Deployment date',
            'BOLT Town'
        ];
    }

    private function RePlacementFormatCsvRow($asset, $TaskStatusService)
    {
        $outlet = $asset->outlet;
        $retailer = $asset->retailer;
        $approval_history = $asset->approvalHistory;

        // Filtering approval history
        $asm_approval = $approval_history->firstWhere('user_role', 'ASM');
        $rsm_approval = $approval_history->firstWhere('user_role', 'RSM');

        $distributor = $asset->distributor;
        $so = $asset->so ?? '';
        $asm = $asset->asm ?? '';
        $rsm = $asset->rsm ?? '';
        $taskStatus = $TaskStatusService->getTaskStatus($asset);
        $deploymentStatus = $TaskStatusService->deploymentStatus($asset, 'placement');

        return [
            $rsm->region ?? '',                  // 'Region'
            $asm->area_code ?? '',               // 'ASM Territory'
            $so->teritory_code ?? '',            // 'SO Territory'
            $asset->cfa_code ?? '',              // 'CFA'
            date('Y-m-d H:i:s', strtotime($asset->created_at)) ?? '', // 'Submit Date'
            $asset->request_number ?? '',        // 'Request No'
            $asset->chiller_type ?? '',          // 'Chiller Type'
            $asset->request_chiller_type ?? '',  // 'Applied Chiller Type'
            $taskStatus ?? '',                   // 'Request Status'
            $asset->request_type ?? '',          // 'Request Type'
            $asset->current_vpo ?? '',           // 'Current VPO'
            $asset->expected_vpo ?? '',          // 'Expected VPO'
            $asm->name ?? '',                    // 'ASM Name'
            $so->name ?? '',                     // 'SO Name'
            $distributor->name ?? '',            // 'Distributor Name'
            $asset->db_code ?? '',               // 'Distributor Code'
            $outlet->route_name ?? '',           // 'Route Name'
            $retailer->user_id ?? '',            // 'Customer Code'
            $retailer->name ?? '',               // 'Customer Name'
            $retailer->city ?? '',               // 'Customer City Name'
            $retailer->address_1 ?? '',          // 'Customer Address'
            $asset->pincode,      // 'Pin Code'
            $asset->remarks ?? '',            // 'Remarks'
            $asset->is_deploy == true ? $asset->deployment_date : '', // 'Deployment date'
            $asset->is_bolt_town == 1 ? "Yes" : 'No',
        ];
    }
}
