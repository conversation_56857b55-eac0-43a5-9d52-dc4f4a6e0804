<?php

namespace App\Http\Controllers\ApprovalCenter\ApprovalReport;
use App\Http\Controllers\Controller;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MaintenanceReportDownloadController extends Controller
{
    public function MaintenanceRequestReportExport(Request $request)
    {
        try {
            $user = Auth::user();

            // Get the query from the service with applied filters
            $request['repair_status'] = 'No';
            $assetPlacementListQuery = PlacementRequestService::placementRequestList($request, $user,'maintenance')->latest();
            if (!$assetPlacementListQuery->exists()) {
                return redirect()->back()->with('error', 'No MaintenanceRequestReport Data available');
            }

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="MaintenanceRequestReport_' . now()->format('Y_m_d_H_i_s') . '.csv"',
            ];

            $TaskStatusService = app(TaskStatusService::class);

            return response()->stream(function () use ($assetPlacementListQuery, $TaskStatusService) {
                $handle = fopen('php://output', 'w');

                // Output the CSV header
                fputcsv($handle, $this->getPlacementCsvHeaders());

                // Stream the data from the assetPlacementList query
                $assetPlacementListQuery->chunk(1000, function ($requests) use ($handle, $TaskStatusService) {
                    foreach ($requests as $asset) {
                        fputcsv($handle, $this->PlacementFormatCsvRow($asset, $TaskStatusService));
                    }
                });

                fclose($handle);
            }, 200, $headers);
        } catch (\Throwable $th) {
            Log::error('Failed to MaintenanceRequestReport: ' . $th->getMessage());
            return redirect()->back()->with('error', 'Something Went Wrong,Please try again later');
        }
    }

    private function getPlacementCsvHeaders()
    {
        return [
            'Date',
            'Region',
            'ASM Name',
            'ASM Code',
            'SO Name',
            'SO Code',
            'CFA Code',
            'Request Number',
            'Task Status',
            'Expected Maintenance Date',
            'Completed Date',
            'Distributor Name',
            'Distributor Code',
            'Customer Code',
            'Customer Name',
            'Customer Address 1',
            'Pin Code',
            'Contact Person',
            'Contact Number',
            'Chiller Type',
            'Asset Number',
            'Asset Barcode',
            'Reason for No Maintenance',
            'DSR Name',
            'DSR Code',
            'Customer City',
            'State',
            'Maintenance Complete Date',
        ];
    }

    private function PlacementFormatCsvRow($asset, $TaskStatusService)
{
    $outlet = $asset->outlet;
    $retailer = $asset->retailer;
    $approval_history = $asset->approvalHistory;

    // Filtering approval history
    $asm_approval = $approval_history->firstWhere('user_role', 'ASM');
    $rsm_approval = $approval_history->firstWhere('user_role', 'RSM');

    $distributor = $asset->distributor;
    $so = $asset->so ?? '';
    $asm = $asset->asm ?? '';
    $rsm = $asset->rsm ?? '';
    $taskStatus = $TaskStatusService->getTaskStatus($asset);

    return [
        date('Y-m-d H:i:s', strtotime($asset->created_at)) ?? '',
        $rsm->region ?? '',
        $asm->name ?? '',
        $asset->asm_code ?? '',
        $so->name ?? '',
        $asset->so_code ?? '',
        $asset->cfa_code ?? '',
        $asset->maintenance_request_number ?? '',
        $taskStatus ?? '',
        $asset->expected_maintenance_date ?? '',
        $asset->maintenance_complete_date ?? '',
        $distributor->name ?? '',
        $asset->db_code ?? '',
        $asset->outlet_code ?? '',
        $retailer->name ?? '',
        $retailer->address_1 ?? '',
        $asset->pincode ?? '',
        $asset->contact_person ?? '',
        $asset->contact_number ?? '',
        $asset->chiller_type ?? '',
        $asset->asset_number ?? '',
        $asset->asset_barcode ?? '',
        $asset->maintenance_reason ?? '',
        $outlet->salesman_name ?? '',
        $asset->dsr_code ?? '',
        $retailer->city ?? '',
        $retailer->state ?? '',
        $asset->maintenance_complete_date ?? '',
        // Action column placeholder or data if needed
    ];
}
}
