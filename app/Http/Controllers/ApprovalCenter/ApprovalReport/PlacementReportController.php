<?php

namespace App\Http\Controllers\ApprovalCenter\ApprovalReport;

use App\Http\Controllers\Controller;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PlacementReportController extends Controller
{
    public function PlacementRequestReportExport(Request $request)
    {
        try {
            $user = Auth::user();

            // Get the query from the service with applied filters
            // $request['chiller_placed'] = 'No';
            $assetPlacementListQuery = PlacementRequestService::placementRequestList($request, $user)
                ->with(['outlet', 'retailer', 'distributor', 'approvalHistory'])
                ->latest();

            if (!$assetPlacementListQuery->exists()) {
                return redirect()->back()->with('error', 'No Placement Data available');
            }

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="PlacementRequestReport_' . now()->format('Y_m_d_H_i_s') . '.csv"',
            ];

            $TaskStatusService = app(TaskStatusService::class);

            return response()->stream(function () use ($assetPlacementListQuery, $TaskStatusService) {
                $handle = fopen('php://output', 'w');

                // Output the CSV header
                fputcsv($handle, $this->getPlacementCsvHeaders());

                // Stream the data from the assetPlacementList query
                $assetPlacementListQuery->chunk(500, function ($requests) use ($handle, $TaskStatusService) {
                    foreach ($requests as $asset) {
                        fputcsv($handle, $this->PlacementFormatCsvRow($asset, $TaskStatusService));
                    }
                });

                fclose($handle);
            }, 200, $headers);
        } catch (\Throwable $th) {
            Log::error('Failed to PlacementRequestReportExport: ' . $th->getMessage());
            return redirect()->back()->with('error', 'Something Went Wrong,Please try again later');
        }
    }

    private function getPlacementCsvHeaders()
    {
        return [
            'Region',
            'Request Number',
            'Submit Date',
            'Request Status',
            'Request Type',
            'Chiller Type',
            'Expected VPO',
            'Customer Code',
            'Customer Name',
            'Contact Number',
            'RTMM Name',
            'ASM Name',
            'ASM Territory',
            'SO Name',
            'SO Territory',
            'Distributor Name',
            'Distributor Code',
            'DSR Name',
            'DSR Code',
            'CFA',
            'Assigned VE',
            'Channel Code',
            'Outlet classification',
            'Customer Category',
            'Route Name',
            'Customer City',
            'Address 1',
            'Address 2',
            'Pin Code',
            'RTMM Approval Time',
            'ASM Approval Time',
            'SO Placement Time',
            'Deployment date',
            'Deployment Status',
            'Asset Serial Number',
            'Asset Barcode',
            'BOLT Town'
        ];
    }

    private function PlacementFormatCsvRow($asset, $TaskStatusService)
    {
        $outlet = $asset->outlet;
        $retailer = $asset->retailer;
        $approval_history = $asset->approvalHistory;

        // Filtering approval history
        $asm_approval = $approval_history->firstWhere('user_role', 'ASM');
        $rsm_approval = $approval_history->firstWhere('user_role', 'RSM');

        $distributor = $asset->distributor;
        $so = $asset->so ?? '';
        $asm = $asset->asm ?? '';
        $rsm = $asset->rsm ?? '';
        $dsr = $asset->dsr ?? '';
        $taskStatus = $TaskStatusService->getTaskStatus($asset);
        $deploymentStatus = $TaskStatusService->deploymentStatus($asset, 'placement');

        return [
            $retailer->region ?? '',
            $asset->request_number ?? '',
            date('Y-m-d H:i:s', strtotime($asset->created_at)) ?? '',
            $taskStatus ?? '',
            $asset->request_type ?? '',
            $asset->eligible_chiller_type ?? '',
            $asset->expected_vpo ?? '',
            $retailer->user_id ?? '',
            $retailer->name ?? '',
            $asset->mobile_number ?? $retailer->mobile_number ?? '',
            $rsm->name ?? '',
            $asm->name ?? '',
            $asm->area_code ?? '',
            $so->name ?? '',
            $so->teritory_code ?? '',
            $distributor->name ?? '',
            $asset->db_code ?? '',
            $outlet->salesman_name ?? '',
            $asset->dsr_code ?? '',
            $asset->cfa_code ?? '',
            $asset->assigned_organization ?? '',
            $outlet->channel_code ?? '',
            $outlet->class_name ?? '',
            $outlet->category_name ?? '',
            $outlet->route_name ?? '',
            $retailer->city ?? '',
            $retailer->address_1 ?? '',
            $asset->customer_address,
            $retailer->pin_code ?? '',
            $rsm_approval->action_updated_time ?? '',
            $asm_approval->action_updated_time ?? '',
            $asset->created_at ?? '',
            $asset->is_deploy == true ? $asset->deployment_date : '',
            $deploymentStatus ?? '',
            $asset->asset_serial_number ?? '',
            $asset->asset_barcode ?? '',
            $asset->is_bolt_town==1?"Yes":'No',
        ];
    }
}
