<?php

namespace App\Http\Controllers\ApprovalCenter;

use App\Http\Controllers\Controller;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\AsssetInventory;
use App\Models\OutletAssetDetailsModel;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\AssetInventoryService;
use App\Services\AsssetInventory\AsssetInventoryService;
use App\Services\GenerateUniqueNumberService;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;

class ApprovalPlacementController extends Controller
{
    function __construct()
    {
        $this->middleware('role_or_permission:AssetPlacementAccess|AssetPlacementCreateAccess|AssetPlacementEditAccess|AssetPlacementDeleteAccess', ['only' => ['approval_center.index', 'approval_center.show']]);
        $this->middleware('role_or_permission:AssetPlacementCreateAccess', ['only' => ['create', 'store']]);
        $this->middleware('role_or_permission:AssetPlacementEditAccess', ['only' => ['edit', 'update']]);
        $this->middleware('role_or_permission:AssetPlacementDeleteAccess', ['only' => ['destroy']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */


    public function index(Request $request)
    {
        $user = Auth::user();
        #filter value
        $request['chiller_placed']='No';
        $filterData = PlacementRequestService::PlacementFilter($user);

        #get asset Type list
        $asset_type = AssetInventoryService::getAssetList();
        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);
        $assetPlacementList = PlacementRequestService::placementRequestList($request, $user)
            ->latest()
            ->paginate($limit, ['*'], 'page', $currentPage);

        $assetPlacementListView = PlacementRequestService::placementRequestTableData($assetPlacementList);

        return view('approvalCentre.placement.index', [
            "asset_type" => $asset_type,
            "assetPlacementList" => $assetPlacementList,
            "assetPlacementListView" => $assetPlacementListView,
            "user" => $user,
            'so_territory_list' => $filterData['so_territory_list'] ?? '',
            'asm_territory_list' => $filterData['asm_territory_list'] ?? '',
            'city_list' => $filterData['city_list'] ?? '',
            'db_name_list' => $filterData['db_name_list'] ?? '',
            'db_code_list' => $filterData['db_code_list'] ?? '',
            'placement_request_list' => $filterData['placement_request_list'] ?? '',
            // 'placement_task_list' => $filterData['placement_task_list'],
            // 'vendorExecutiveList' => $filterData['vendorExecutiveList'],
        ]);
    }

    #show deployed Chiller asset list
    public function AssetMappingMaster(Request $request){
        $user = Auth::user();
        #filter value
        $filterData = PlacementRequestService::PlacementFilter($user);

        #get asset Type list
        $asset_type = AssetInventoryService::getAssetList();
        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);
        $request['chiller_placed']='Yes';
        $assetPlacementList = PlacementRequestService::placementRequestList($request, $user)
            ->latest()
            ->paginate($limit, ['*'], 'page', $currentPage);

        $assetPlacementListView = PlacementRequestService::placementRequestTableData($assetPlacementList);

        return view('approvalReport.asset_master_mapping_report', [
            "asset_type" => $asset_type,
            "assetPlacementList" => $assetPlacementList,
            "assetPlacementListView" => $assetPlacementListView,
            "user" => $user,
            'so_territory_list' => $filterData['so_territory_list'] ?? '',
            'asm_territory_list' => $filterData['asm_territory_list'] ?? '',
            'city_list' => $filterData['city_list'] ?? '',
            'db_name_list' => $filterData['db_name_list'] ?? '',
            'db_code_list' => $filterData['db_code_list'] ?? '',
            'placement_request_list' => $filterData['placement_request_list'] ?? '',
            // 'placement_task_list' => $filterData['placement_task_list'],
            // 'vendorExecutiveList' => $filterData['vendorExecutiveList'],
        ]);
    }

    private function getNextHeirarchyRole($role, $model = null): string
    {
        $nextHeirarchyRole = [
            'ASM' => 'RSM',
            'RSM' => 'AE',
            'AE' => 'VENDOR',
            'VENDOR' => 'VE'
        ];
        if ($model === 'pullout' || $model === 'maintenance') {
            $pulloutNextHeirarchyRole = [
                'ASM' => 'AE'
            ];
            if (isset($pulloutNextHeirarchyRole[$role])) {
                return $pulloutNextHeirarchyRole[$role];
            }
        }

        return isset($nextHeirarchyRole[$role]) ? $nextHeirarchyRole[$role] : '';
    }

    public function Approval($type, $status, $request, $user)
    {
        $timestamps = [
            'placement' => [
                'approved' => 'placement_approved_time',
                'rejected' => 'placement_rejected_time'
            ],
            'pullout' => [
                'approved' => 'pullout_approved_time',
                'rejected' => 'pullout_rejected_time'
            ],
            'maintenance' => [
                'approved' => 'maintenance_approved_time',
                'rejected' => 'maintenance_rejected_time'
            ],
            'replacement' => [
                'approved' => 'replacement_approved_time',
                'rejected' => 'replacement_rejected_time'
            ]
        ];

        $updateData = [
            'asset_assigned_status' => $request->input('status'),
            'approved_by_user_role' => $status == 'Approved' ? $user->user_type : null,
            'pending_from' => $status == 'Approved' ? $this->getNextHeirarchyRole($user->user_type, $type) : null,
            'approved_by_user_id' => $status == 'Approved' ? $user->user_id : null,
            $timestamps[$type]['approved'] => $status == 'Approved' ? now() : null,
            'rejection_reason' => $status != 'Approved' ? $request->rejection_reason : null,
            'rejected_by_user_id' => $status != 'Approved' ? $user->user_id : null,
            'rejected_by_user_role' => $status != 'Approved' ? $user->user_type : null,
            $timestamps[$type]['rejected'] => $status != 'Approved' ? now() : null,
        ];

        return $updateData;
    }

    public function RePlacementApproval($status, $request, $user)
    {
        return $this->Approval('replacement', $status, $request, $user);
    }
    public function PlacementApproval($status, $request, $user)
    {
        return $this->Approval('placement', $status, $request, $user);
    }

    public function MaintenanceApproval($status, $request, $user)
    {
        return $this->Approval('maintenance', $status, $request, $user);
    }

    public function PulloutApproval($status, $request, $user)
    {
        return $this->Approval('pullout', $status, $request, $user);
    }

    private function placementApprovalModel($model, $status, $request, $user)
    {
        $placementApprovalData = [
            'pullout' => $this->PulloutApproval($status, $request, $user),
            'placement' => $this->PlacementApproval($status, $request, $user),
            'maintenance' => $this->MaintenanceApproval($status, $request, $user),
            'replacement' => $this->RePlacementApproval($status, $request, $user),
        ];
        return $placementApprovalData[$model] ?? $this->PlacementApproval($status, $request, $user);
    }

    public function ApprovalReplacementRequestStatusUpdate(Request $request)
    {
        try {
            $user = Auth::user();
            $model = $request->approvalType ?? 'placement';
            $modelClassList = PlacementRequestService::getModelWithRequestNumber($model);
            $modelClass = $modelClassList['model'];
            $requestNumber = $modelClassList['request_number'] ?? 'request_number';
            $assetPlacement = $modelClass::whereIn($requestNumber, $request->input('selectedIds'))->get();
            $responseMessage = '';
            $success = false;
            $status = $request->input('status');
            foreach ($assetPlacement as $placement) {


                $success = true;
                $updateData = $this->placementApprovalModel($model, $status, $request, $user);
                $success = true; // Flag indicating at least one placement was successful

                $responseMessage .= "Placement status updated.\n";

                $placement->update($updateData);
                $actionUpdatedTime = now();
                $placementRequestNumber = $placement[$modelClassList['request_number']];
                // $requestNummber = $modelClassList['request_number'] ?? 'request_number';
                AssetApprovalPlacementRequestHistory::updateOrCreate(
                    [
                        'user_id' => $user->user_id,
                        // 'action' => $request->input('status'),
                        'user_role' => $user->user_type,
                        'asset_placement_request_id' =>  $placementRequestNumber,
                    ],
                    [
                        'action_updated_time' => $actionUpdatedTime,
                        'action' => $request->input('status'),
                    ]
                );
                $success = true;
                $responseMessage .= "Placement status updated for request number: {$placementRequestNumber}.\n";
            }

            if (!$success) {
                return response()->json(['error' => 'Placement request not approved.']);
            }
            return response()->json(['success' => 'Placement Status Updated.']);
        } catch (\Throwable $th) {
            Log::error('Failed to RequestPlacementAsset: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to Update placement status.'], 500);
        }
    }

    public function edit($id)
    {
        // echo "hello";die;
        return view('approvalCentre.placement.edit');
    }

    public function UpdateRequestAssetType(Request $request)
    {
        try {
            $model = $request->request_type ?? 'placement';
            $modelClassList = PlacementRequestService::getModelWithRequestNumber($model);
            $modelClass = $modelClassList['model'];
            $asset_type = $modelClassList['asset_type'];
            $requestNumber = $modelClassList['request_number'] ?? 'request_number';
            $asset = AssetInventoryService::placementAssetTypeCode($request->update_asset_type);

            $update = $modelClass::where($requestNumber, $request->request_number)->update([
                $asset_type => $asset['asset_type'], // Assuming you want to update 'asset_type' column
                'asset_type_code' => $asset['asset_code'], // Assuming you want to update 'asset_type' column
            ]);

            if (!$update) {
                return redirect()->back()->with('error', 'Chiller Type not update for this Request Number => ' . $request->request_number);
            }
            return redirect()->back()->withSuccess('Asset Type  Updated Succesfully for this Request Number => ' . $request->request_number);
        } catch (\Exception $e) {
            Log::error('Error updating request asset type: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while updating the asset type. Please try again later.');
        }
    }
}
