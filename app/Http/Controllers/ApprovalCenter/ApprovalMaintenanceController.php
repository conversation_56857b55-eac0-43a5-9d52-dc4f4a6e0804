<?php

namespace App\Http\Controllers\ApprovalCenter;

use App\Http\Controllers\Controller;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApprovalMaintenanceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    const MAINTENANCE = 'maintenance';
    public function index(Request $request)
    {
        $user = Auth::user();
        $filterData = PlacementRequestService::PlacementFilter($user, self::MAINTENANCE);
        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);
        $assetPlacementList = PlacementRequestService::placementRequestList($request, $user, self::MAINTENANCE)
            ->latest()
            ->paginate($limit, ['*'], 'page', $currentPage);
        $assetPlacementListView = PlacementRequestService::placementRequestTableData($assetPlacementList, self::MAINTENANCE);
        return view('approvalCentre.maintenance.index', [
            "assetPlacementList" => $assetPlacementList,
            "assetPlacementListView" => $assetPlacementListView,
            "user" => $user,
            'so_territory_list' => $filterData['so_territory_list'] ?? '',
            'asm_territory_list' => $filterData['asm_territory_list'] ?? '',
            'city_list' => $filterData['city_list'] ?? '',
            'db_name_list' => $filterData['db_name_list'] ?? '',
            'db_code_list' => $filterData['db_code_list'] ?? '',
            'placement_request_list' => $filterData['placement_request_list'] ?? '',
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return view('approvalCentre.maintenance.edit');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
