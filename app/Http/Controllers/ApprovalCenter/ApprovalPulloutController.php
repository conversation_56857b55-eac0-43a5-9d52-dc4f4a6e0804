<?php

namespace App\Http\Controllers\ApprovalCenter;

use App\Exports\AssetPullout\AssetPulloutExport;
use App\Exports\Inventory\InwardOutWardReport;
use App\Http\Controllers\Controller;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class ApprovalPulloutController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $filterData = PlacementRequestService::PlacementFilter($user, 'pullout');
        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);
        $assetPlacementList = PlacementRequestService::placementRequestList($request, $user, 'pullout')
            ->latest()
            ->paginate($limit, ['*'], 'page', $currentPage);
        $assetPlacementListView = PlacementRequestService::placementRequestTableData($assetPlacementList, 'pullout');
        return view('approvalCentre.pullout.index', [
            "assetPlacementList" => $assetPlacementList,
            "assetPlacementListView" => $assetPlacementListView,
            "user" => $user,
            'so_territory_list' => $filterData['so_territory_list'] ?? '',
            'asm_territory_list' => $filterData['asm_territory_list'] ?? '',
            'city_list' => $filterData['city_list'] ?? '',
            'db_name_list' => $filterData['db_name_list'] ?? '',
            'db_code_list' => $filterData['db_code_list'] ?? '',
            'placement_request_list' => $filterData['placement_request_list'] ?? '',
        ]);
    }


    public function PulloutRequestReportExport(Request $request)
    {
        try {
            $user = Auth::user();
            $request['pullout_placed'] = 'No';
            $assetPlacementList = PlacementRequestService::placementRequestList($request, $user, 'pullout')->latest()->get();
            $assetPlacementListView = PlacementRequestService::placementRequestTableData($assetPlacementList, 'pullout');
            if (!$assetPlacementListView) {
                return redirect()->back()->with('error', 'No Data available');
            }
            return Excel::download(new AssetPulloutExport($assetPlacementListView), 'PulloutRequestReport' . now() . '.xlsx');
        } catch (\Throwable $th) {
            Log::error('Failed to PulloutRequestReportExport: ' . $th->getMessage());
            return redirect()->back()->with('error', 'Something Went Wrong,Please try again later');
        }

    }
}
