<?php

namespace App\Http\Controllers\UploadUtility;

use App\Http\Controllers\Controller;
use App\Models\AsssetInventory;
use App\Services\AsssetInventory\AsssetInventoryService;
use App\Services\UploadHistoryDetails\UploadHistoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
class CfaWiseInventoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $limit = $request->query('limit', 10);
        $assetType = $request->query('act') ? decrypt($request->query('act')) : '';
        $currentPage = $request->query('page', 1);
        $approval_status = strtolower($request->query('status', 'all'));
        $assetChillerType = AsssetInventory::distinct()->pluck('asset_type')->toArray();
        $warehouse_code = AsssetInventory::distinct()->pluck('warehouse_code')->toArray();
        $batchID = Session::get('upload_batch_id');
        $uploadHistory=UploadHistoryService::getLatestHistorybyBatchID($batchID,$user->user_id);
        $inventoryList = AsssetInventoryService::getInventoryList($user, $limit, $assetType, $currentPage, $approval_status='pending');
        return view('uploadUtility.cfa_wise_inventory', compact('inventoryList', 'user', 'assetChillerType','uploadHistory','warehouse_code'));
    }

    public function assetInventoryStatusApprovalByAE(Request $request)
    {
        try {
            Log::info('assetInventoryStatusApprovalByAE Request:', ['data' => $request->all()]);
            $user = Auth::user();
            if ($user->user_type != "AE") {
                return response()->json(['error' => 'You do not have access to this action.']);
            }

            $userType = $user->user_type;
            $userID = $user->user_id;
            $selectedIds = $request->input('selectedIds');
            $rejection_reason = $request->input('rejection_reason') ?? '';
            $status = strtolower($request->input('status'));

            // Validate the status
            if (empty($status) || !in_array($status, ['approved', 'rejected'])) {
                return response()->json(['error' => 'Invalid status provided.']);
            }
            $updateData = [
                'asset_approval_status' => $status,
                'approved_by_user_type' => $userType,
                'approved_by_user' => $userID,
                'approved_by_user' => $userID,
                'remarks' => $rejection_reason,
                'approval_time' => now(),
            ];

            $assets = AsssetInventory::whereIn('id', $selectedIds)->get();

            if ($assets->isEmpty()) {
                return response()->json(['error' => 'No matching asset inventory found.']);
            }

            foreach ($assets as $asset) {
                $asset->update($updateData);
            }

            return response()->json(['success' => 'Asset Inventory status updated successfully.']);
        } catch (\Throwable $th) {
            Log::error('Failed to update Asset Inventory status: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to update Asset Inventory status.'], 500);
        }
    }
}
