<?php

namespace App\Http\Controllers\Admin;

use App\Constants\RolePermissionConstants;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateUserRequest;
use App\Models\CFABusiness;
use App\Models\UploadHistory;
use App\Services\UploadHistoryDetails\UploadHistoryService;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\User;
use App\Services\AdminUserCreateValidationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Services\UserIDGeneratorService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    //    protected $userService;
    function __construct(UserService $userService)
    {
        $this->middleware('role_or_permission:UserAccess|UserCreate|UserEdit|UserDelete', ['only' => ['index', 'show']]);
        $this->middleware('role_or_permission:UserCreate', ['only' => ['create', 'store']]);
        $this->middleware('role_or_permission:UserEdit', ['only' => ['edit', 'update']]);
        $this->middleware('role_or_permission:UserDelete', ['only' => ['destroy']]);
        $this->userService = $userService;
    }

    public function index(Request $request)
    {
        $data = $this->userService->getUsers($request);
        return view('setting.user.index', $data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function create()
    {
        // $roles = Role::get();
        $auth = Auth::user();
        $batchID = Session::get('upload_batch_id');
        $uploadHistory = UploadHistoryService::getLatestHistorybyBatchID($batchID, $auth->user_id);
        $roles = Role::whereIn('name', ['RSM', 'ASM', 'SO', 'DSR', 'DISTRIBUTOR', 'VENDOR', 'VE', 'RETAILER'])->get();
        $uploadFormats = RolePermissionConstants::UPLOADFORMAT;
        return view('setting.user.new', ['roles' => $roles, 'uploadFormats' => $uploadFormats, 'uploadHistory' => $uploadHistory]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    private function prepareUserData($row, $role)
    {
        $userData = [];

        $fieldMappings = app(AdminUserCreateValidationService::class)->getUserTypeFields()[$role]['form'] ?? [];

        foreach ($fieldMappings as $excelField) {
            $databaseField = app(AdminUserCreateValidationService::class)->mapExcelToDatabaseFields($role, $excelField);
            if ($databaseField && isset($row[$excelField])) {
                if (in_array($databaseField, ['effective_from', 'effective_to', 'retailer_created_on', 'modified_date'])) {
                    $userData[$databaseField] = Carbon::createFromFormat('Y-m-d', $row[$excelField])->format('Y-m-d');
                } else {
                    $userData[$databaseField] = $row[$excelField];
                }
            }
        }
        return $userData;
    }

    public function store(CreateUserRequest $request)
    {
        dd("validatiion done");
        try {
            #Generate UserID with prefix or without prefix
            // $userID = UserIDGeneratorService::generateUserID($request->selectedRole);
            $userID = $request->user_code;
            $role = $request->user_type;
            if (in_array($role, ["RBDM_GTMS", "RSM", "ASM", "DSR", "DISTRIBUTOR", "SO"])) {
                $user = User::create([
                    'name' => $request->name ?? '',
                    'email' => $request->email ?? '',
                    'channel_type' => $request->channel_code ?? '',
                    'teritory_code' => $request->teritory_code ?? '',
                    'mobile_number' => $request->mobile_number ?? '',
                    'region' => $request->region_name ?? '',
                    'region_code' => $request->region_code ?? '',
                    'area_code' => $request->area_code ?? '',
                    'cfa_code' => $request->cfa_code ?? '',
                    'city' => $request->city ?? '',
                    'state' => $request->state ?? '',
                    'pin_code' => $request->pincode ?? '',
                    'distributor_id' => $request->distributor_code ?? '',
                    'so_id' => $request->so_code ?? '',
                    'asm_id' => $request->asm_code ?? '',
                    'rsm_id' => $request->rsm_code ?? '',
                    'rbdm_id' => $request->rbdm_code ?? '',
                    'user_id' => $userID,
                    'user_type' => $request->user_type,
                    'password' => bcrypt($userID),
                    'address_1' => $request->address_1 ?? '',
                    'status' => $request->status ?? ''
                ]);
                $user->syncRoles($request->user_type);
            } elseif ($role == "CFA") {
                $prepareUserData = $this->prepareUserData($request, $role);
                $user = CFABusiness::firstOrCreate(
                    ['cfa_plant_code' => $prepareUserData['cfa_plant_code'], 'user_type' => $role],
                    $prepareUserData
                );
            } elseif ($role == "CFA") {
            }
            // return redirect()->route('admin.users.index')->withSuccess('User created successfully !!!');
            return redirect()->route('admin.users.index')->withSuccess('User created !!!');
        } catch (\Exception $exception) {
            Log::error('Error : CreaterUser data at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return redirect()->route('admin.users.index')->withErrors(['error' => 'Failed to create asset placement request']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($view_id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(User $user)
    {
        $role = Role::get();
        return view('setting.user.edit', ['user' => $user, 'roles' => $role]);
    }
    public function user_edit_page($user_code)
    {
        $auth = Auth::user();
        $user_details = User::where(['user_id' => $user_code, 'user_type' => 'SO'])->first();
        return view('setting.user.user_edit', ['user_details' => $user_details,'auth'=>$auth]);
    }


    public function UpdateUserInfo(Request $request)
    {
        DB::beginTransaction();
        try {
            $user_details = User::where(['user_id' => $request->old_user_id, 'user_type' => 'SO'])->first();
            if (!$user_details) {
                return redirect()->back()->withErrors(['error' => 'Invalid User Details']);
            }
            $updateData = [
                'name' => $request->name,
                'user_id' => $request->user_id,
                'email' => $request->email,
                'mobile_number' => $request->mobile_number,
                'status' => $request->status,
            ];

            $updateStatus = $user_details->update($updateData);
            if ($updateStatus) {
                User::where(['so_id' => $request->old_user_id])->update(['so_id' => $request->user_id]);
            } else {
                throw new \Exception('User not updated');
            }
            DB::commit();
            return redirect()->route('admin.users.index')->with('success', 'User details updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating user details: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'An error occurred while updating user details. Please try again.']);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(CreateUserRequest $request, User $user)
    {
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'user_type' => implode(',', $request->roles),
            'mobile_number' => $request->mobile_number,
            'address_1' => $request->address_1,
            'city' => $request->city,
            'state' => $request->state,
            'pin_code' => $request->pin_code,
            'region' => $request->region,
            'status' => $request->status
        ]);

        $user->syncRoles($request->roles);
        return redirect()->route('admin.users.index')->withSuccess('User updated !!!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, User $user)
    {
        // Remove the user's roles and permissions before deleting
        $user->roles()->detach();

        // Delete the user
        $user->delete();
        return redirect()->back()->withSuccess('User deleted successfully!!!');
    }

    public function generateUserID($userType, $modelName)
    {
        $modelClass = '\App\Models\\' . $modelName;
        $nextUserId = $userType . str_pad($modelClass::max('id') + 1, 5, '0', STR_PAD_LEFT);
        return $nextUserId;
    }
}
