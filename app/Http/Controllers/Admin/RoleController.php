<?php

namespace App\Http\Controllers\Admin;

use App\Constants\RolePermissionConstants;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Gate;


class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('role_or_permission:Role access|Role create|Role edit|Role delete', ['only' => ['index','show']]);
        $this->middleware('role_or_permission:Role create', ['only' => ['create','store']]);
        $this->middleware('role_or_permission:Role edit', ['only' => ['edit','update']]);
        $this->middleware('role_or_permission:Role delete', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $role= Role::latest()->paginate(4);

        return view('setting.role.index',['roles'=>$role]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $permissions = Permission::get();
        $hierarchy= RolePermissionConstants::HIERARCHY;
        return view('setting.role.new',['permissions'=>$permissions,'hierarchy'=>$hierarchy]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validateRole($request);

        $role = Role::create(['name'=>$request->name,'hierarchy_view'=>$request->hierarchy_view]);

        $role->syncPermissions($request->permissions);

        return redirect()->back()->withSuccess('Role created !!!');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Role $role)
    {

        $permission = Permission::get();
        $role->permissions;
        $hierarchy= RolePermissionConstants::HIERARCHY;
       return view('setting.role.edit',['role'=>$role,'permissions' => $permission,'hierarchy'=>$hierarchy]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Role $role)
    {
        $this->validateRole($request);
        $role->update(['name'=>$request->name,'hierarchy_view'=>$request->hierarchy_view]);
        $role->syncPermissions($request->permissions);
        return redirect()->route('admin.roles.index')->withSuccess('Role updated !!!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Role $role)
    {
        $role->delete();
        return redirect()->back()->withSuccess('User deleted !!!');
    }

    protected function validateRole(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'hierarchy_view' => 'required',
        ]);
    }
}
