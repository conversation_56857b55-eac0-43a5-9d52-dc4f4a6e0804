<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $userCounts = User::selectRaw('
        COUNT(*) as total_users,
        SUM(CASE WHEN user_type = "RSM" THEN 1 ELSE 0 END) as rsm_count,
        SUM(CASE WHEN user_type = "ASM" THEN 1 ELSE 0 END) as asm_count,
        SUM(CASE WHEN user_type = "SO" THEN 1 ELSE 0 END) as so_count,
        SUM(CASE WHEN user_type = "RETAILER" THEN 1 ELSE 0 END) as retailer_count,
        SUM(CASE WHEN user_type = "DISTRIBUTOR" THEN 1 ELSE 0 END) as db_count
    ')
            ->first();
        return view('dashboard.index', compact('userCounts'));
    }
}
