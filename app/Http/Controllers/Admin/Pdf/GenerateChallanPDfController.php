<?php

namespace App\Http\Controllers\Admin\Pdf;

use App\Http\Controllers\Controller;
use App\Models\AssetPlacementRequest;
use App\Services\ChallanPDF\ChallanPdfService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\File;
use PDF;
use Symfony\Component\HttpFoundation\Response;

class GenerateChallanPDfController extends Controller
{
    protected $challanPdfService;

    public function __construct(ChallanPdfService $challanPdfService)
    {
        $this->challanPdfService = $challanPdfService;
    }


    public function generateChallanPDFDownload($id, Request $request)
    {
        try {
            $requestNumber = decrypt($id);
            $request_module = $request->request_module??'placement';
            $challanPdf = strtotime(now()) . "deliveryChallan.pdf";
            $pdf = $this->challanPdfService->generateChillerInstallationChallanPDF($requestNumber,$request_module);

            if ($pdf === null) {
                return redirect()->back()->with('error', 'Failed to generate challan PDF,Please try again later');
            }

            return $pdf->download($challanPdf);
        } catch (\Exception $exception) {
            Log::error('Error : ChallanPdfService generateChallanPDF controller at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return redirect()->back()->with('error', 'Failed to download challan');
        }
    }
    #Download multiple Challan pdf basis of request number
    public function generateMultipleRequestChallanPDF(Request $request)
    {
        try {
            if (!$request->aprID) {
                return Redirect::back()->withErrors(['error' => 'INVALID Request']);
            }
            $requestNumberArray = explode('#$', $request->aprID);
            $request_type=$request->request_type??'placement';
            $prefix = ucfirst($request_type);
            // $challanPdf = strtotime(now()) . "deliveryChallan.pdf";
            $challanPdf = strtotime(now()) .$prefix. "deliveryChallan.pdf";
            $pdf = $this->challanPdfService->generateChillerInstallationChallanPDF($requestNumberArray,$request_type);
            if ($pdf === null) {
                return redirect()->back()->with('error', 'Failed to generate challan PDF,Please try again later');
            }
            return $pdf->download($challanPdf);
        } catch (\Exception $exception) {
            Log::error('Error : ChallanPdfService generateChallanPDF controller at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return redirect()->back()->with('error', 'Failed to download challan');
        }
    }
}
