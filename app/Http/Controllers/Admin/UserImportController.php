<?php

namespace App\Http\Controllers\Admin;

use App\Exports\UploadHistory\AssetASMErrorLogExport;
use App\Exports\UploadHistory\AssetDBErrorLogExport;
use App\Exports\UploadHistory\AssetDbToCfaMappingErrorLogExport;
use App\Exports\UploadHistory\AssetDSRErrorLogExport;
use App\Exports\UploadHistory\AssetInventoryErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerGSTupdateErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerLatLongErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerMappingErrorLogExport;
use App\Exports\UploadHistory\AssetRSMErrorLogExport;
use App\Exports\UploadHistory\AssetSOErrorLogExport;
use App\Exports\UploadHistory\AssetVEErrorLogExport;
use App\Exports\UploadHistory\AssetVendorErrorLogExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\UserMasterUploadRequest;
use App\Imports\UserMasterImport;
use App\Imports\UsersDemoImport;
use App\Imports\UsersHierarchyImport;
use App\Jobs\ImportHierarchyFileJob;
use App\Jobs\PoductJob;
use App\Jobs\ProcessUploaderImport;
use App\Jobs\ProcessLargeFileImport;
use App\Models\AssetErrorLog;
use App\Models\UploadHistory;
use App\Models\User;
use App\Services\UploadHistoryDetails\UploadHistoryService;
use Illuminate\Http\Request;
use App\Services\UserImportHierarchyService;
use App\Services\UserImportService;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\support\Facades\Bus;
use Illuminate\Queue\WithBatchId;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class UserImportController extends Controller
{
    protected $UserImportHierarchyService;
    protected $UserImportService;

    public function __construct(UserImportHierarchyService $UserImportHierarchyService, UserImportService $UserImportService)
    {
        $this->UserImportHierarchyService = $UserImportHierarchyService;
        $this->UserImportService = $UserImportService;
        $this->auth= Auth::user();
    }
    public function index()
    {
        return view('setting.user.hierarchy_upload');
    }


    public function uploadHierarchyFile(Request $request)
    {
        try {
            // Check if a file is present in the request
            if (!$request->hasFile('hierarchy_file')) {
                return response()->json(['error' => 'empty_file', 'message' => "Please upload hierarchy file"], 422);
            }

            // Excel::queueImport(new UsersDemoImport, $request->hierarchy_file);
            Excel::import(new UsersHierarchyImport, $request->hierarchy_file);
            // $filePath = Storage::putFile('hierarchy_files', $request->file('hierarchy_file'));
            // ImportHierarchyFileJob::dispatch($filePath);
            return response()->json(['success' => 'File imported successfully!']);
        } catch (ValidationException $e) {
            $errors = $e->validator->getMessageBag()->all();
            return response()->json(['validation_error' => $errors], 422);
        } catch (\Throwable $th) {
            \Log::error('Failed to import users: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to import users Hierarchy. Please try again.'], 500);
        }
    }


    public function userImportPage()
    {
        $roles = Role::whereNotIn("name", ["SUPERADMIN", "ADMIN"])->get();
        return view('setting.user.user_master_upload', ['roles' => $roles]);
    }
    public function UploadUserMasterDetails(UserMasterUploadRequest $request)
    {
        try {
            if (!$request->hasFile('user_import_file')) {
                return response()->json(['error' => 'empty_file', 'message' => "Please upload user master excel file"], 422);
            }

            $userRole = $request->user_type;
            $requestIP = $request->ip();
            $batchID = uniqid('batch_') . '_' . time() . '_' . bin2hex(random_bytes(4));

            Session::put('upload_batch_id', $batchID);

            // Get file information for optimization
            $file = $request->file('user_import_file');
            $fileSize = $file->getSize();
            $fileName = $file->getClientOriginalName();

            Log::info("Starting large file import", [
                'file_name' => $fileName,
                'file_size' => $this->formatBytes($fileSize),
                'user_role' => $userRole,
                'batch_id' => $batchID,
                'estimated_rows' => $this->estimateRowCount($fileSize)
            ]);

            // Create upload history with enhanced tracking
            $this->uploadHistoryCreate($batchID, $userRole, $requestIP, $fileSize, $fileName);

            // Set memory and time limits for large files
            $this->setOptimalLimits($fileSize);

            // Use queue for very large files (>50MB or estimated >100k rows)
            if ($fileSize > 52428800 || $this->estimateRowCount($fileSize) > 100000) {
                return $this->handleLargeFileImport($request, $batchID, $userRole, $fileSize);
            }

            // Process normally for smaller files
            Excel::import(new UserMasterImport($userRole, $batchID), $file);

            return response()->json([
                'success' => 'File processed successfully!',
                'batch_id' => $batchID,
                'redirectRoute' => $request->redirectRoute ?? ''
            ]);

        } catch (ValidationException $e) {
            $errors = $e->validator->getMessageBag()->all();
            Log::error('Validation error during import', ['errors' => $errors, 'batch_id' => $batchID ?? null]);
            return response()->json(['validation_error' => $errors], 422);
        } catch (\Throwable $th) {
            Log::error('Failed to import users: ' . $th->getMessage(), [
                'batch_id' => $batchID ?? null,
                'user_role' => $userRole ?? null,
                'stack_trace' => $th->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to import users. Please try again.' . $th->getMessage()], 500);
        }
    }

    /**
     * Handle large file import using queue
     */
    private function handleLargeFileImport($request, $batchID, $userRole, $fileSize)
    {
        try {
            // Store file temporarily for queue processing
            $file = $request->file('user_import_file');
            $filePath = Storage::putFile('temp_imports', $file);

            // Determine which job to use based on file size
            $estimatedRows = $this->estimateRowCount($fileSize);

            if ($fileSize > 104857600 || $estimatedRows > 200000) { // >100MB or >200k rows
                // Use specialized large file job
                ProcessLargeFileImport::dispatch($filePath, $userRole, $batchID)
                    ->onQueue('high-priority')
                    ->delay(now()->addSeconds(10));

                $message = 'Very large file queued for processing. This may take several hours.';
            } else {
                // Use standard queue job
                ProcessUploaderImport::dispatch($filePath, $userRole, '', $batchID)
                    ->onQueue('high-priority')
                    ->delay(now()->addSeconds(5));

                $message = 'Large file queued for processing. You will be notified when complete.';
            }

            Log::info("Large file queued for processing", [
                'batch_id' => $batchID,
                'file_path' => $filePath,
                'user_role' => $userRole,
                'file_size' => $this->formatBytes($fileSize),
                'estimated_rows' => $estimatedRows,
                'job_type' => $fileSize > 104857600 ? 'ProcessLargeFileImport' : 'ProcessUploaderImport'
            ]);

            return response()->json([
                'success' => $message,
                'batch_id' => $batchID,
                'status' => 'queued',
                'estimated_rows' => $estimatedRows,
                'file_size' => $this->formatBytes($fileSize),
                'message' => 'Your file is being processed in the background. Check the upload history for progress.',
                'redirectRoute' => $request->redirectRoute ?? ''
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to queue large file import', [
                'batch_id' => $batchID,
                'error' => $e->getMessage(),
                'file_size' => $fileSize ?? 'unknown'
            ]);

            return response()->json([
                'error' => 'Failed to queue large file for processing. Please try again.'
            ], 500);
        }
    }

    /**
     * Estimate row count based on file size
     */
    private function estimateRowCount($fileSize)
    {
        // Rough estimation: average 150 bytes per row for Excel files
        return intval($fileSize / 150);
    }

    /**
     * Format bytes for display
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Set optimal memory and time limits based on file size
     */
    private function setOptimalLimits($fileSize)
    {
        // Set time limit based on file size
        if ($fileSize > 104857600) { // > 100MB
            set_time_limit(3600); // 1 hour
        } elseif ($fileSize > 52428800) { // > 50MB
            set_time_limit(1800); // 30 minutes
        } elseif ($fileSize > 10485760) { // > 10MB
            set_time_limit(600); // 10 minutes
        } else {
            set_time_limit(300); // 5 minutes
        }

        // Increase memory limit if needed
        $currentLimit = $this->getMemoryLimitInBytes();
        $recommendedMemory = max($fileSize * 3, 512 * 1024 * 1024); // 3x file size or 512MB minimum

        if ($currentLimit < $recommendedMemory && $currentLimit != -1) {
            ini_set('memory_limit', $this->formatBytes($recommendedMemory, 0));
            Log::info("Memory limit increased", [
                'old_limit' => $this->formatBytes($currentLimit),
                'new_limit' => $this->formatBytes($recommendedMemory),
                'file_size' => $this->formatBytes($fileSize)
            ]);
        }
    }

    /**
     * Get memory limit in bytes
     */
    private function getMemoryLimitInBytes()
    {
        $memoryLimit = ini_get('memory_limit');

        if ($memoryLimit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) $memoryLimit;

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }

    public function BatchUploadHistoryDownload(Request $request)
    {
        $rules = [
            'batchID' => 'required|exists:asset_error_log,batch_id' // 'batches' is the table name, 'id' is the column
        ];
        $validator = Validator::make($request->all(), $rules);

        // If validation fails, redirect back with errors
        if ($validator->fails()) {
            return redirect()->back()->with('error', 'Invalid BatchID');
        }
        $batchID = $request->batchID;
        $uploadDetails=UploadHistoryService::getLatestHistorybyBatchID($batchID);
//        $uploadDetailslist=UploadHistoryService::getAssetLogDetailsByBatchID($batchID);
//        dd($uploadDetailslist->toArray());
        if ($uploadDetails->uploadType == 'RSM') {
            return Excel::download(new AssetRSMErrorLogExport($batchID), 'Upload_data_RSM_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'ASM') {
            return Excel::download(new AssetASMErrorLogExport($batchID), 'Upload_data_ASM_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'SO') {
            return Excel::download(new AssetSOErrorLogExport($batchID), 'Upload_data_SO_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'DISTRIBUTOR') {
            return Excel::download(new AssetDBErrorLogExport($batchID), 'Upload_data_DISTRIBUTOR_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'RETAILER') {
            return Excel::download(new AssetRetailerErrorLogExport($batchID), 'Upload_data_RETAILER_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'VENDOR') {
            return Excel::download(new AssetVendorErrorLogExport($batchID), 'Upload_data_VENDOR_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'VE') {
            return Excel::download(new AssetVEErrorLogExport($batchID), 'Upload_data_VE_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'DSR') {
            return Excel::download(new AssetDSRErrorLogExport($batchID), 'Upload_data_DSR_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'retailer_gst_update') {
            return Excel::download(new AssetRetailerGSTupdateErrorLogExport($batchID), 'Upload_data_retailer_gst_update_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'retailer_lat_long') {
            return Excel::download(new AssetRetailerLatLongErrorLogExport($batchID), 'Upload_data_retailer_lat_long_' . now() . $batchID . '.xlsx');
        }
        #pending
        elseif ($uploadDetails->uploadType == 'db_to_cfa_mapping') {
            return Excel::download(new AssetDbToCfaMappingErrorLogExport($batchID), 'Upload_data_db_to_cfa_mapping_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'asset_retailer_mapping') {
            return Excel::download(new AssetRetailerMappingErrorLogExport($batchID), 'Upload_data_asset_retailer_mapping_' . now() . $batchID . '.xlsx');
        }
       elseif ($uploadDetails->uploadType == 'asset_inventory_upload') {
           return Excel::download(new AssetInventoryErrorLogExport($batchID), 'Upload_data_asset_inventory_upload_' . now() . $batchID . '.xlsx');
       }
        else{
            return redirect()->back()->with('error', 'Invalid BatchID');
        }
    }


    public function uploadHistoryCreate($batchID, $userRole, $ip, $fileSize = null, $fileName = null)
    {
        $user = Auth::user();

        return UploadHistory::create([
            'batchID' => $batchID,
            'uploadBy' => $user->user_id,
            'requestIP' => $ip,
            'uploadByName' => $user->name,
            'userType' => $user->user_type,
            'uploadDateTime' => now(),
            'uploadType' => $userRole,
            'totalRecordscount' => 0,
            'successCount' => 0,
            'failedCount' => 0,
            'failedMessage' => '',
            'failedData' => [],
            'file_size' => $fileSize,
            'file_name' => $fileName,
            'estimated_rows' => $fileSize ? $this->estimateRowCount($fileSize) : 0,
            'status' => 'processing'
        ]);
    }
}
