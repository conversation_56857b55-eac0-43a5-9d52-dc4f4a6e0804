<?php

namespace App\Http\Middleware;

use App\Http\Controllers\Controller;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Cache\RateLimiter;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\Response;

class IVYAssetThrottleRequests
{
    protected $limiter;

    public function __construct(RateLimiter $limiter)
    {
        $this->limiter = $limiter;
    }

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
     * @param int $maxAttempts
     * @param int $decayMinutes
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $maxAttempts = 1, $decayMinutes = 1440)
    {
        $key = $this->resolveRequestSignature($request);

        if ($this->limiter->tooManyAttempts($key, $maxAttempts)) {
            return $this->buildResponse($key, $maxAttempts);
        }

        $this->limiter->hit($key, $decayMinutes);

        return $next($request);
    }

    protected function resolveRequestSignature(Request $request)
    {
        return sha1($request->ip());
    }

    protected function buildResponse($key, $maxAttempts)
    {
        $response = response()->json([
            'message' => 'You have exceeded the rate limit. Please try again later.',
        ], 429);

        $retryAfter = $this->limiter->availableIn($key);
        $response->headers->set('Retry-After', $retryAfter);
        return app(Controller::class)->error('You have exceeded the rate limit. Please try again later', Response::HTTP_TOO_MANY_REQUESTS);
    }
}
