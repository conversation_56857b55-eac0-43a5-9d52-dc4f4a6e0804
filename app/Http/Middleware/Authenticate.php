<?php

namespace App\Http\Middleware;

use App\Models\ApiLog;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Closure;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (!$request->expectsJson()) {
            return route('login');
        }
    }
    public function handle($request, Closure $next, ...$guards)
    {
        // If the user is not authenticated

        if ($request->is('api/*')) {
            // If the user is not authenticated for API requests
            if (!Auth::guard('api')->check()) {
                // Return JSON response for API requests
                return response()->json(["success" => false, 'message' => 'Invalid Authentication token.'], 401);
            }
        } else {
            // If the user is not authenticated for web requests
            if (!$request->user() || !auth()->check()) {
                // Check if the request expects JSON response
                if ($request->expectsJson()) {
                    // Return JSON response for API requests
                    return response()->json(["success" => false, 'message' => 'Invalid Authentication token.'], 401);
                } else {
                    // Redirect to the login page for web requests
                    return redirect()->route('admin.login');
                }
            }
        }
        return $next($request);
    }

    //     public function handle($request, Closure $next, ...$guards)
    // {
    //     // If the user is not authenticated
    //     if (! $request->user() || ! auth()->check()) {
    //         // Check if the request expects JSON response
    //         if ($request->expectsJson()) {
    //             // Return JSON response for API requests
    //             return response()->json(["success" => false, 'message' => 'Invalid Authentication token.'], 401);
    //         } else {
    //             // Redirect to the login page for web requests
    //             return redirect()->route('admin.login');
    //         }
    //     }

    //     // User is authenticated, continue with the request
    //     return $next($request);
    // }

    protected function unauthenticated($request, array $guards)
    {
        if ($request->expectsJson()) {
            // Return JSON response for API requests
            abort(response()->json(["success" => false, 'message' => 'Invalid Authentication token.'], 401));
        } else {
            // Redirect to the login page for web requests
            $this->redirectTo($request);
        }
    }

    public function terminate($request, $response)
    {
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            $this->logAction($request, $response);
        }
    }

    protected function logAction(Request $request, $response)
    {
        $actionType = $request->method() === 'POST' ? 'create_action' : 'update_action';

        ApiLog::create([
            'action' => $actionType,
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'request_data' => $request->all(),
            'response_data' => json_decode($response->getContent(), true),
            'status_code' => $response->status(),
            'client_ip' => $request->ip(),
            'logged_at' => now(),
        ]);
    }
}
