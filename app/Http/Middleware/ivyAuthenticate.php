<?php

namespace App\Http\Middleware;

use App\Constants\IVYConstant;
use App\Http\Controllers\Controller;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ivyAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {

        if ($request->hasHeader('Authorization')) {
            $authenticate = IVYConstant::IVYASSETRETAILERAUTHENTICATION;
            $username = $authenticate['user'] ?? '';
            $password = $authenticate['password'] ?? '';
            $authHeader = $request->header('Authorization');
            if (preg_match('/Basic\s+(.*)$/i', $authHeader, $matches)) {
                $auth = base64_decode($matches[1]);
                list($user, $pass) = explode(':', $auth);
                if ($user == $username && $pass == $password) {
                    return $next($request);
                }
            }
        }
        return app(Controller::class)->error('Invalid Authentication token', Response::HTTP_UNAUTHORIZED);
    }
}
