<?php
namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Contracts\Encryption\DecryptException;

class ValidateUserId
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Get user ID from request header
        try {
            // Attempt to decrypt the user ID
            $userId = $request->header('userID');
            $user = User::where('user_id',$userId)->first();
            // $user = User::find($userId);
            if (!$user) {
                abort(response()->json(["success" => false, 'message' => 'Invalid Authentication token.'], 401));
            }
            // if($user->type !='SO'){
            //     abort(response()->json(["success" => false, 'message' => 'Unauthorized access token.'], 401));
            // }
            // Proceed with the request
            return $next($request);
        } catch (DecryptException $e) {
            abort(response()->json(["success" => false, 'message' => 'Invalid Authentication token.'], 401));
        }
    }


}
