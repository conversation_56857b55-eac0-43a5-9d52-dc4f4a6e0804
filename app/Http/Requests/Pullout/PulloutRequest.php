<?php

namespace App\Http\Requests\Pullout;

use Illuminate\Foundation\Http\FormRequest;

class PulloutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request .
     *
     * @return array<string, mixed>
     */
    public function rules()

    {
        return [
            "UserId" => "required|exists:users,user_id",
            'Type' => 'required',
            "OutletId" => "required",
            "ChillerType" => "required",
            "AssetNumber" => "required",
            "AssetBarCode" => "required",
            "ScannedBarcode" => "required",
            "CurrentVPO" => "nullable",
            "CustomerName" => "required",
            "CustomerCode" => "required",
            "PulloutAssetType" => "required",
            "ConsentStatus" => "required",
            "Signature" => "required",
            "Email" => "nullable",
            "MobileNo" => "required",
            "CustomerAddress" => "required",
            "PinCode" => "required",
            "PulloutReason" => "required",
            "CustomerLocation" => "required",
            "CurrentLocation" => "nullable",
            "Distance" => "required",
            "Latitude" => "required",
            "Longitude" => "required",
            "Status" => "required",
        ];
    }
}
