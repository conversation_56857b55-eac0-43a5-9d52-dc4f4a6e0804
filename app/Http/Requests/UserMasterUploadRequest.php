<?php

namespace App\Http\Requests;

use App\Imports\UserMasterImport;
use Illuminate\Foundation\Http\FormRequest;
use Maatwebsite\Excel\Facades\Excel;

class UserMasterUploadRequest extends FormRequest
{
    private $maxRows = 10000;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'user_import_file' => ['mimes:xlsx,csv'],
            'user_type' => ['required'],
        ];

        // Additional custom validation for file content, only if file is present
        if ($this->hasFile('user_import_file')) {

            $rules['user_import_file'][] = function ($attribute, $value, $onFailure) {
                $userType = $this->input('user_type');
                if ($userType == "RSM") {
                    $requiredHeaders = [
                        'ae_code', 'region_name', 'rsmrbdm_code', 'rsmrbdm_region_code', 'rsm_name', 'rbdm_name', 'email_id', 'mobile_number', 'rbdm_email', 'rbdm_mobile_number'
                    ];
                } elseif ($userType == "ASM") {
                    $requiredHeaders =  ['ae_code', 'rsm_code', 'rtmm_code', 'asm_area_code', 'asm_name', 'asm_code', 'email_id', 'mobile_number'];
                }
                elseif ($userType == "SO") {
                    $requiredHeaders =
                    ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_teritory_code', 'so_user_code', 'so_name','email_id', 'mobile_number'];
                }
                elseif ($userType == "DISTRIBUTOR") {
                    $requiredHeaders =
                    ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_code', 'channel', 'cfa_code', 'distributor_code', 'distributor_name','city','email_id', 'mobile_number'];
                }
                elseif ($userType == "DSR") {
                    $requiredHeaders =
                    ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_code', 'db_code', 'dsr_code', 'dsr_name', 'mobile_number','email_id'];
                }
                elseif ($userType == "VENDOR") {
                    $requiredHeaders =
                    ['region', 'cfaplant_code', 'cfa_name', 'address_1', 'address_2','mobile_number','email_id', 'city', 'state', 'country', 'pin_code', 'effective_from', 'effective_to', 'gst_no'];
                }
                elseif ($userType == "VE") {
                    $requiredHeaders = [
                        'region', 'name', 'user_code', 'cfa_code', 'address_1','city','mobile_number','email_id', 'state', 'country', 'pin_code'
                    ];
                }
                elseif ($userType == "RETAILER") {
                    // $requiredHeaders = [
                    //     'region', 'ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'retailercode', 'retailername', 'salesman_code', 'salesman_name',
                    //     //  'salesman_code','salesman_name','route_code','route_name','category_code','category_name','sub_channel_code','sub_channel_name','channel_code','channel_name','class_name','is_tcs','address1','invoiceaddress','pin_code','town','city','state','lat','long','contact_name','mobile','email','location_code','location_name'
                    // ];
                    $requiredHeaders =['region', 'ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'retailercode', 'retailername', 'salesman_code', 'salesman_name', 'route_code', 'route_name', 'category_code', 'category_name', 'sub_channel_code', 'sub_channel_name', 'channel_code', 'channel_name', 'class_name', 'is_tcs', 'address1', 'address2', 'address3', 'invoiceaddress', 'pin_code', 'town', 'city', 'state', 'lat', 'long', 'contact_name', 'mobile', 'email', 'aadhar', 'credit_limit', 'credit_period', 'location_code', 'location_name', 'return_qty', 'return_value', 'drug_license_no', 'drug_license_exp_date', 'fssai_no', 'fssai_exp_date', 'retailer_created_on', 'modified_date', 'retailer_modified_on', 'retailer_reference_code','gst_no', 'active'];
                }

                 elseif ($userType == "db_to_cfa_mapping") {
                    $requiredHeaders = [
                        'distributor_code', 'city', 'state', 'cfa_code'
                    ];
                }
                 elseif ($userType == "retailer_lat_long") {
                    $requiredHeaders = [
                        'retailercode', 'lat', 'long', 'address1', 'address2', 'address3', 'pin_code'
                    ];
                }
                 elseif ($userType == "asset_inventory_upload") {
                    $requiredHeaders = [
                        'assetdescription', 'assettype', 'modelname', 'vendor', 'quantity', 'warehousecode', 'manufacturedyear', 'price', 'asset_serial_number', 'asset_barcode', 'vpo'
                    ];
                }
                 elseif ($userType == "retailer_gst_update") {
                    $requiredHeaders = [
                        'retailercode', 'gst'
                    ];
                }
                 elseif ($userType == "asset_retailer_mapping") {
                    $requiredHeaders = [
                        // 'ae_code', 'rsm_code', 'asm_code', 'so_code', 'cfa_code', 'distributor_code', 'salesman_code', 'retailer_code', 'contact_person', 'customer_address', 'asset_number', 'asset_barcode', 'chiller_type_name', 'vpo','request_pending_from','request_status'
                        'ae_code', 'rsm_code', 'asm_code', 'so_code', 'cfa_code', 'distributor_code', 'salesman_code', 'retailer_code', 'contact_person', 'customer_address', 'chiller_type_name', 'vpo','request_pending_from','request_status'
                    ];
                }
                else {
                    $onFailure("Please Upload Valid User Master Excel Sheet Format, as the user type is not $userType");
                    return;
                }
                $reader = Excel::toArray(new UserMasterImport($this->input('user_type'), 'validation_batch'), $value);
                // Check for row limit
                if ($this->checkRowCount($reader) > $this->maxRows) {
                    $onFailure("The file contains more than {$this->maxRows} rows. Please upload a file with less than {$this->maxRows} rows.");
                    return;
                }
                $headingRow = $reader[0][0] ?? [];
                $missingHeaders = array_diff($requiredHeaders, array_keys($headingRow));
                if (!empty($missingHeaders)) {
                    $onFailure("Please Upload Valid User Master Excel Sheet Format , in this sheet the some headers are missing " . implode(', ', $missingHeaders));
                    // $onFailure("The following headers are missing: " . implode(', ', $missingHeaders));
                }
            };
        }

        return $rules;
    }

    private function checkRowCount(array $reader): int
    {
        $sheetData = $reader[0] ?? [];
        return count($sheetData);
    }
}
