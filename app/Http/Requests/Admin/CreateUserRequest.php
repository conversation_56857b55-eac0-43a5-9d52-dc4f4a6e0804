<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\AdminUserCreateValidationService; // Import the AdminUserCreateValidationService class

class CreateUserRequest extends FormRequest
{
    protected $adminUserCreateValidationService;

    // Correct the type hinting in the constructor
    public function __construct(AdminUserCreateValidationService $adminUserCreateValidationService)
    {
        $this->adminUserCreateValidationService = $adminUserCreateValidationService;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $userType = $this->input('user_type');
        // dd($userType);die;
        // Retrieve the validation fields from the service
        $fields = $this->adminUserCreateValidationService->getAdminUserTypeFields()[$userType]['validate'] ?? [];

        $validationRules = [];
        foreach ($fields as $field) {
            $validationRules[$field] = 'required'; // Assuming all fields are required, you can adjust this as needed
        }
        return $validationRules;
    }
}
