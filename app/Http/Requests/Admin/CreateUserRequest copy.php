<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $userId = $this->route('user');
        $isCreating = $this->isMethod('post');
        $role = $this->input('user_type');
        $emailRule = Rule::unique('users', 'email')->ignore($userId);

        // Check if the provided email is different from the current user's email during an update
        if (!$isCreating && $this->email == $this->user()->email) {
            $emailRule = '';
        }

        $validationRules = [
            'user_type' => 'required',
            'user_code' => 'required|unique:users,user_id',
            // 'region_code' => 'required',

            'name' => 'required',

            // 'status' => 'required',
        ];
        if ($role === 'RSM') {
            $validationRules['region_name'] = 'required';
            $validationRules['region_code'] = 'required';
            $validationRules['rbdm_code'] = 'required';
        }
        if ($role === 'ASM') {
            $validationRules['teritory_code'] = 'required';
            $validationRules['rsm_code'] = 'required';
            $validationRules['rbdm_code'] = 'required';
            $validationRules['region_code'] = 'required';
            $validationRules['area_code'] = 'required';

        } elseif ($role === 'SO') {
            $validationRules['teritory_code'] = 'required';
            $validationRules['asm_code'] = 'required';
            $validationRules['rsm_code'] = 'required';
            $validationRules['rbdm_code'] = 'required';

        } elseif ($role === 'Distributor') {
            $validationRules['channel_code'] = 'required';
            $validationRules['rbdm_code'] = 'required';
            $validationRules['rsm_code'] = 'required';
            $validationRules['asm_code'] = 'required';
            $validationRules['so_code'] = 'required'; // Add validation rule for Distributor Code

        } elseif ($role === 'DSR') {
            $validationRules['rbdm_code'] = 'required';
            $validationRules['rsm_code'] = 'required';
            $validationRules['asm_code'] = 'required';
            $validationRules['so_code'] = 'required'; // Add validation rule for Distributor Code
            $validationRules['distributor_code'] = 'required'; // Add validation rule for DSR Code

        }

        return $validationRules;
    }
}
