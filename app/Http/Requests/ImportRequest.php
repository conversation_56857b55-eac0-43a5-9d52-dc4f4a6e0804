<?php

namespace App\Http\Requests;

use App\Imports\UsersHierarchyImport;
use Illuminate\Foundation\Http\FormRequest;
use Maatwebsite\Excel\Facades\Excel;
class ImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function rules()
{
    $rules = [
        'hierarchy_file' => ['required'],
    ];

    // Check if hierarchy_file is present and not empty
    if ($this->hasFile('hierarchy_file')) {
        $rules['hierarchy_file'][] = 'mimes:xlsx,csv'; // File type validation

        // Custom validation for file content
        $rules['hierarchy_file'][] = function ($attribute, $value, $onFailure) {
            $requiredHeaders = [
                'region_name',
                'rsmrbdm_name',
                'rsm_code',
//                'rsm_region_code',
                'asm_area_code',
                'asm_code',
                'asm_name',
                'so_teritory_code',
                'so_user_code',
                'so_name',
                'so_contact_number',
                'channel',
                'cfa_code',
                'distributor_code',
                'distributor_name',
                'dsr_code',
                'dsr_name',
                'dsr_contact_no',
                'city',
                'state',
                'pincode',
            ];

            $reader = Excel::toArray(new UsersHierarchyImport(), $value);
            $headingRow = $reader[0][0] ?? [];
            $missingHeaders = array_diff($requiredHeaders, array_keys($headingRow));

            if (!empty($missingHeaders)) {
                $onFailure("The following headers are missing: " . implode(', ', $missingHeaders));
            }
        };
    }

    return $rules;
}

}
