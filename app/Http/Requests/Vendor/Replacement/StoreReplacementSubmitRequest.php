<?php

namespace App\Http\Requests\Vendor\Replacement;

use Illuminate\Foundation\Http\FormRequest;

class StoreReplacementSubmitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $replacedAsset = $this->input('chiller_placed');
        return [
            'user_id' => 'required|exists:users,user_id',
            'outlet_id' => 'required|exists:retaileroutlets,id',
            'chiller_placed' => 'required|string|in:Yes,No',
            'request_number' => 'required|exists:asset_replacement_requests,request_number',
            'reason_no_replacement' => 'nullable|string',
            'remarks' => 'nullable|string',
            'outlet_photo' => 'nullable|string',
            'retailer_photo' => 'nullable|string',
            'replaced_asset_number' => $replacedAsset == 'No' ? 'nullable' : 'required',
            'scanned_barcode' => $replacedAsset == 'No' ? 'nullable' : 'required',
            'prev_assigned_asset_number' => $replacedAsset == 'No' ? 'nullable' : 'required',
            'placement_request_number' => $replacedAsset == 'No' ? 'nullable' : 'required',
            'chiller_image' => 'nullable|string',
            'correct_location' => 'nullable|string',
            'distance' => 'nullable|numeric',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'current_location' => 'nullable|string',
        ];
    }
}
