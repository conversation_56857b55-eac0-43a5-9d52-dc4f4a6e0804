<?php

namespace App\Http\Requests\Vendor\AssetTaskPullout;

use Illuminate\Foundation\Http\FormRequest;

class AssetPulloutTaskDeploymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,user_id',
            'outlet_id' => 'required|exists:retaileroutlets,id',
            'outlet_code' => 'required',
            'pullout_request_number' => 'required|exists:asset_pullout_requets,pullout_request_number',
            'pullout_placed' => 'required|in:Yes,No',
            'reason_no_deployment' => 'nullable|string',
            'remarks' => 'nullable|string',
            'outlet_photo' => 'nullable|string',
            'retailer_photo' => 'nullable|string',
            'asset_number' => 'required_if:pullout_placed,Yes',
            'asset_barcode' => 'required_if:pullout_placed,Yes',
            'chiller_image' => 'nullable|string',
            'installation_receipt' => 'nullable|string',
            'correct_location' => 'nullable|string',
            'distance' => 'nullable|numeric',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'current_location' => 'nullable|string',
        ];
    }
}
