<?php

namespace App\Http\Requests\Vendor\Maintenance;

use Illuminate\Foundation\Http\FormRequest;

class MaintenanceTaskDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'RequestNumber' => 'required|exists:asset_maintenance_requests,maintenance_request_number',
            'UserId' => 'required|exists:users,user_id',
        ];

    }
}
