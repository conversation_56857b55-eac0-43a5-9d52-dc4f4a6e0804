<?php

namespace App\Http\Requests\Vendor\Maintenance;

use Illuminate\Foundation\Http\FormRequest;

class StoreMaintenanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $maintenanceAsset = $this->input('maintenance_asset');
        return [
            'user_id' => 'required|exists:users,user_id',
            'outlet_id' => 'required|exists:retaileroutlets,id',
            'maintenance_asset' => 'required|string|in:Yes,No',
            'request_number' => 'required|exists:asset_maintenance_requests,maintenance_request_number',
            'reason_no_maintenance' => 'nullable|string',
            'remarks' => 'nullable|string',
            'outlet_photo' => 'nullable|string',
            'retailer_photo' => 'nullable|string',
            'asset_number' => $maintenanceAsset == 'No' ? 'nullable' : 'required',
            'asset_barcode' => $maintenanceAsset == 'No' ? 'nullable' : 'required',
            'scanned_barcode' => $maintenanceAsset == 'No' ? 'nullable' : 'required',
            'chiller_image' => 'nullable|string',
            'correct_location' => 'nullable|string',
            'distance' => 'nullable|numeric',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'current_location' => 'nullable|string',
        ];
    }
}
