<?php

namespace App\Http\Requests\Audit;

use Illuminate\Foundation\Http\FormRequest;

class AssetAuditListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'UserId' => 'required|exists:users,user_id',
            'OutletId' => 'required|exists:retaileroutlets,id',
            'Type' => 'required|string|in:SO,DSR',
            'FromDate' => 'nullable|date',
            'ToDate' => 'nullable|date|after_or_equal:FromDate',
        ];
    }
}
