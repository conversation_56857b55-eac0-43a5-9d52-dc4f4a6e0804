<?php

namespace App\Http\Requests\Audit;

use Illuminate\Foundation\Http\FormRequest;

class AssetAuditRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,user_id',
            'type' => 'required|string|in:SO,DSR',
            'outlet_id' => 'required|exists:retaileroutlets,id',
            'asset_id' => 'required|integer',
            'asset_number' => 'required|string',
            'is_chiller_available' => 'required',
            'is_barcode_available' => 'nullable|string',
            'barcode_no_scan_reason' => 'nullable|string',
            'scanned_barcode' => 'nullable|string',
            'barcode_match_status' => 'nullable|string',
            'customer_address' => 'nullable|string',
            'contact_number' => 'nullable',
            'retailer_name' => 'nullable',
            'pincode' => 'nullable|integer',
            'current_location' => 'required|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'distance' => 'required|numeric',
            'audit_status' => 'required|string',
            'remarks' => 'nullable|string',
        ];
    }
}
