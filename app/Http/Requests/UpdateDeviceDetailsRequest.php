<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDeviceDetailsRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'UserId' => 'required',
            // 'UserId' => 'required|exists:users,id',
            'Type' => 'required|string',
            'AppVersion' => 'required|string',
            'VersionCode' => 'required|integer',
            'OSVersion' => 'required|string',
            'Device' => 'required|string',
        ];
    }
}
