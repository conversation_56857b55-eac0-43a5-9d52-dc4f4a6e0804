<?php

namespace App\Http\Requests\IVY;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\JsonResponse;
class AssetRetailerMasterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $requestData = $this->all();

        // Ensure we have an array of data and validate each entry
        return [
            '*.Region' => 'required',
            // '*.AECode' => 'required|string',
            // '*.RSMCode' => 'required|string',
            '*.ASMCode' => 'required|string',
            '*.SOCode' => 'required|string',
            '*.DistributorCode' => 'required|string',
            '*.RetailerCode' => 'required|string',
            '*.RetailerName' => 'required|string',
            '*.SalesManCode' => 'required|string',
            '*.SalesManName' => 'required|string',
            '*.SalesManEmpCode' => 'required|string',
            '*.RouteCode' => 'nullable|string',
            '*.RouteName' => 'nullable|string',
            '*.CategoryCode' => 'required|string',
            '*.CategoryName' => 'required|string',
            '*.SubChannelCode' => 'nullable|string',
            '*.SubChannelName' => 'nullable|string',
            '*.ChannelCode' => 'nullable|string',
            '*.ChannelName' => 'required|string',
            '*.ClassName' => 'required|string',
            '*.PANNumber' => 'nullable|string',
            '*.GSTNumber' => 'nullable|string',
            '*.isTcs' => 'nullable|string',
            '*.Address1' => 'required|string',
            '*.Address2' => 'nullable|string',
            '*.Address3' => 'nullable|string',
            '*.InvoiceAddress' => 'nullable|string',
            '*.PinCode' => 'required|string',
            '*.Town' => 'nullable|string',
            '*.City' => 'required|string',
            '*.State' => 'required|string',
            '*.Lat' => 'required|numeric',
            '*.Long' => 'required|numeric',
            '*.ContactName' => 'nullable|string',
            '*.Mobile' => 'nullable|string',
            '*.Email' => 'nullable|string|email',
            '*.Aadhar' => 'nullable|string',
            '*.CreditLimit' => 'nullable|numeric',
            '*.CreditPeriod' => 'nullable|numeric',
            '*.LocationCode' => 'required|string',
            '*.LocationName' => 'required|string',
            '*.ReturnQty' => 'required|integer',
            '*.ReturnValue' => 'required|numeric',
            '*.DrugLicenseNo' => 'nullable|string',
            '*.DrugLicenseExpDate' => 'nullable|date',
            '*.FSSAINo' => 'nullable|string',
            '*.FSSAIExpDate' => 'nullable|date',
            '*.RetailerCreatedOn' => 'required|date',
            '*.ModifiedDate' => 'nullable|date',
            '*.RetailerModifiedOn' => 'nullable|date',
            '*.RetailerReferenceCode' => 'nullable|string',
            '*.CreatedBy' => 'required|string',
            '*.CreatedTime' => 'required|date_format:Y-m-d H:i:s',
            '*.UpdatedTime' => 'required|date_format:Y-m-d H:i:s',
            '*.UpdatedBy' => 'required|string',
            '*.Active' => 'required|in:yes,no',
        ];
    }
    // protected function failedValidation(Validator $validator)
    // {
    //     $firstError = $validator->errors()->first();
    //     $controller = app()->make(Controller::class);
    //     $response = $controller->errorReponse([ 'errors' => $validator->errors()], $firstError, $status = false, 422);
    //     // $response = new JsonResponse([
    //     //     'message' => 'The given data was invalid.',
    //     //     'errors' => $validator->errors(),
    //     // ], 422);

    //     throw new \Illuminate\Validation\ValidationException($validator, $response);
    // }
}
