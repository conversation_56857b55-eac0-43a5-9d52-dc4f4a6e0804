<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssetImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'asset_image' => 'required|image|mimes:jpeg,png,jpg,gif',
            'type' => 'required|integer|in:1,2,3,4,5,6,7,8',
        ];
    }
}
