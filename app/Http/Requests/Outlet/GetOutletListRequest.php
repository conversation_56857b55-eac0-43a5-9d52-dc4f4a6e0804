<?php

namespace App\Http\Requests\Outlet;

use Illuminate\Foundation\Http\FormRequest;

class GetOutletListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'UserId' => 'required',
            'Type' => 'required|string|in:SO,DSR', // Assuming Type should be either 'SO' or 'PO'
            'Offset' => 'required|integer',
            'Limit' => 'required|integer',
            'AssignedStatus' => 'required|string|in:All,Assigned,Unassigned'
            // Assuming AssignedStatus can be null or one of 'All', 'Assigned', or 'Unassigned'
        ];
    }
}
