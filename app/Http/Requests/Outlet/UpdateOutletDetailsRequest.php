<?php

namespace App\Http\Requests\Outlet;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOutletDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'UserId' => 'required|exists:users,user_id',
            'Type' => 'required|string|in:SO,DSR',
            'OutletId' => 'required|integer|exists:retaileroutlets,id',
            // 'Image' => 'required|file|image|mimes:jpeg,png,jpg,gif',
            'ContactNumber' => 'sometimes',
            'DBName' => 'sometimes',
            'City' => 'sometimes',
        ];
    }
}
