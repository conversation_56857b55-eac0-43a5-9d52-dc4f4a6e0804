<?php

namespace App\Http\Requests\Maintenance;

use Illuminate\Foundation\Http\FormRequest;

class MaintenanceRequestStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,user_id',
            'type' => 'required|string|in:SO,DSR',
            'outlet_id' => 'required|exists:retaileroutlets,id',
            'asset_number' => 'required|string|max:255',
            'asset_barcode' => 'required|string|max:255',
            'current_vpo' => 'nullable',
            'chiller_type' => 'required|string|max:255',
            'customer_code' => 'required|string|max:255',
            'customer_name' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'contact_number' => 'nullable|string|max:255',
            'consent_status' => 'required|in:Confirmed',
            'signature' => 'required|string',
            'email' => 'nullable|email|max:255',
            'mobile_number' => 'nullable|string|max:255',
            'customer_address' => 'nullable|string',
            'pincode' => 'nullable|string|max:255',
            'maintenance_reason' => 'required|string|max:255',
            'customer_location' => 'nullable|string|max:255',
            'current_location' => 'nullable|string|max:255',
            'correct_location' => 'nullable|string|max:255',
            'distance' => 'nullable|numeric',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'remarks' => 'nullable|string',
            'status_remarks' => 'nullable|string',
        ];
    }
}
