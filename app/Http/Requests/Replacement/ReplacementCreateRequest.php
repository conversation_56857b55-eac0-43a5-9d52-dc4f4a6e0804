<?php

namespace App\Http\Requests\Replacement;

use Illuminate\Foundation\Http\FormRequest;

class ReplacementCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,user_id',
            'placement_request_number' => 'required|exists:asset_placement_requests,request_number',
            'outlet_code' => 'required|exists:users,user_id',
            'type' => 'required|string|in:SO,DSR',
            'outlet_id' => 'required|exists:retaileroutlets,id',
            'asset_barcode' => 'required|string',
            'asset_number' => 'required|string',
            'chiller_type' => 'required|string',
            'current_vpo' => 'nullable',
            'expected_vpo' => 'required',
            'request_chiller_type' => 'required|string',
            'request_type' => 'required|in:Normal,Exceptional',
            'additional_equipment' => 'nullable|array',
            'chiller_location' => 'required|string',
            'chiller_location_photo' => 'required|string',
            'consent_status' => 'required|string',
            'retailer_photo' => 'required|string',
            'signature' => 'required|string',
            'address_proof' => 'required|string',
            'mobile_number' => 'required|string',
            'customer_address' => 'required|string',
            'pincode' => 'required|string',
            'replacement_reason' => 'required|string',
            'customer_location' => 'required',
            'current_location' => 'nullable',
            // 'is_bolt_town' => 'nullable|boolean',
            'distance' => 'required',
            'remarks' => 'nullable|string',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'correct_location' => 'nullable|in:Yes,No',
            'IsBoltTown' => 'required|in:0,1',
        ];
    }
}
