<?php

namespace App\Http\Requests\Replacement;

use Illuminate\Foundation\Http\FormRequest;

class ReplacementRequestDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,user_id', // Assuming user_id is an integer
            'type' => 'required|string|in:SO,DSR', // Assuming type can be 'SO' or 'DSR'
            'request_number'=>'required|exists:asset_replacement_requests,request_number'
        ];
    }
}
