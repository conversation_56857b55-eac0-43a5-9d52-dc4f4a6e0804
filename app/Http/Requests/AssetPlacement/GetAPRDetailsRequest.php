<?php

namespace App\Http\Requests\AssetPlacement;

use Illuminate\Foundation\Http\FormRequest;
// use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Rule;
class GetAPRDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
{
    return [
        'UserId' => 'required|exists:users,user_id',
        'Type' => 'required|string|in:SO,DSR',
        'RequestId' => 'required|string|exists:asset_placement_requests,request_number',
    ];
}

}
