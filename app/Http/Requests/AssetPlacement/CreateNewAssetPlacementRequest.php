<?php

namespace App\Http\Requests\AssetPlacement;

use Illuminate\Foundation\Http\FormRequest;

class CreateNewAssetPlacementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'UserId' => 'required|exists:users,user_id',
            'OutletId' => 'required|integer|exists:retaileroutlets,id',
            'Type' => 'required|string|in:SO,DSR',
            'ExpectedVPO' => 'required|string',
            'EligibleChillerType' => 'nullable|string', // Optional field
            'RequestType' => 'required|string|in:Normal,Exceptional',
            'AdditionalEquipment' => 'sometimes|array',
            'CompetitorChillerSize' => 'sometimes|array',
            'CompetitorCompany' => 'sometimes|array',
            'CompetitorChillerPhoto' => 'nullable|string',
            'ChillerLocation' => 'required|string',
            'ChillerLocationPhoto' => 'required|string',
            'AddressProof' => 'required|string',
            'RetailerPhoto' => 'required|string',
            'Signature' => 'required|string',
            'MobileNumber' => 'required|string|regex:/^\d{10}$/',
            'CustomerAddress' => 'required|string',
            'Pincode' => 'required|string',//set min and max=6
            'CustomerLocation' => 'nullable|string', // Optional field
            'CurrentLocation' => 'nullable|string', // Optional field
            'Latitude' => 'nullable|numeric', // Optional numeric field
            'Longitude' => 'nullable|numeric', // Optional numeric field
            'CorrectLocation' => 'sometimes|in:Yes,No', // Optional field
            'Distance' => 'numeric|nullable', // Optional numeric field
            'Remarks' => 'nullable|string', // Optional field
            'ConsentStatus' => 'required|string|in:Confirmed', // Must be "Confirmed"
            'IsBoltTown' => 'required|in:0,1',
        ];
    }
}
