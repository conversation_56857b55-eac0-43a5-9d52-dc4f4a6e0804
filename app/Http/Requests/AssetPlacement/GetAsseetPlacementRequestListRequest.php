<?php

namespace App\Http\Requests\AssetPlacement;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class GetAsseetPlacementRequestListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        // Convert the "Status" parameter to have the first character uppercase
        if ($this->has('Status')) {
            $this->merge([
                'Status' => Str::ucfirst($this->input('Status'))
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'UserId' => 'required|exists:users,user_id',
//            'OutletId' => 'required|exists:retaileroutlets,id',
            'Type' => 'required|string|in:SO,DSR',
            'FromDate' => 'nullable|date_format:Y-m-d',
            'ToDate' => 'nullable|date_format:Y-m-d|after_or_equal:FromDate',
            'Status' => 'nullable|string|in:All,Approved,Rejected,Pending',
        ];
    }
}
