<?php

namespace App\Library\Utils;

use App\Models\AssetAudit;
use Carbon\Carbon;

class AssetAuditFilter
{
    public static function applyFilters($query, $request, $user)
    {
        $filters = [
            'so_name', 'asm_name', 'db_code', 'distributor_name',
            'customer_name', 'customer_city', 'customer_code',
            'route_name', 'request_no'
        ];

        foreach ($filters as $filter) {
            if ($request->has($filter)) {
                $value = $request->input($filter);

                switch ($filter) {
                    case 'FromDate':
                        $query->whereDate('created_at', '>=', Carbon::parse($value));
                        break;
                    case 'ToDate':
                        $query->whereDate('created_at', '<=', Carbon::parse($value));
                        break;
                    case 'so_name':
                        $query->whereHas('outlet.dsr.distributor.so', function ($query) use ($value) {
                            $query->where('name', $value);
                        });
                        break;
                    case 'asm_name':
                        $query->whereHas('outlet.dsr.distributor.so.asm', function ($query) use ($value) {
                            $query->where('name', $value);
                        });
                        break;
                    case 'db_code':
                    case 'distributor_name':
                        $query->whereHas('outlet.dsr.distributor', function ($query) use ($value, $filter) {
                            $query->where(function ($query) use ($value, $filter) {
                                if ($filter === 'db_code') {
                                    $query->where('user_id', $value);
                                }
                                if ($filter === 'distributor_name') {
                                    $query->where('name', $value);
                                }
                            });
                        });
                        break;
                    case 'customer_name':
                    case 'customer_city':
                    case 'customer_code':
                        $query->whereHas('outlet.retailer', function ($query) use ($value, $filter) {
                            $query->where(function ($query) use ($value, $filter) {
                                if ($filter === 'customer_city') {
                                    $query->where('city', $value);
                                }
                                if ($filter === 'customer_name') {
                                    $query->where('name', $value);
                                }
                                if ($filter === 'customer_code') {
                                    $query->where('user_id', $value);
                                }
                            });
                        });
                        break;
                    case 'route_name':
                        $query->whereHas('outlet', function ($query) use ($value) {
                            $query->where('route_name', $value);
                        });
                        break;
                    case 'request_no':
                        $query->where('request_number', $value);
                        break;
                }
            }
        }

        // Additional filter for user types
        $userTypes = ['ASM', 'RSM', 'SO', 'AE', 'VENDOR'];
        if (in_array($user->user_type, $userTypes)) {
            $key = $user->user_type == 'ASM' ? 'asm_id' : (
                $user->user_type == 'RSM' ? 'rsm_id' : (
                    $user->user_type == 'SO' ? 'so_id' : (
                        $user->user_type == 'AE' ? 'ae_id' : 'vendor_id'
                    )
                )
            );
            $query->whereHas('outlet.retailer', function ($query) use ($key, $user) {
                $query->where($key, $user->user_id);
            });
        }

        return $query;
    }
}
