<?php

namespace App\Library\Utils;

use Carbon\Carbon;

class DateFormate
{
    public static function convertDbFormat($excelField,$format = 'Y-m-d')
    {
        // Attempt to parse the date using strtotime
        $timestamp = strtotime($excelField);

        // If strtotime returns a valid timestamp, convert it to the desired format
        if ($timestamp !== false) {
            $date = Carbon::createFromTimestamp($timestamp);
            return $date->format($format);
        }

        // If strtotime fails, use today's date as a fallback
        $date = Carbon::now();

        return $date->format($format);
    }

    public  static function convertDateFormatToDbFormat($dateTime, $timestamp = '')
    {
        $format = $timestamp == 'time' ? 'Y-m-d H:i:s' : 'Y-m-d';
        return self::convertDbFormat($dateTime, $format);
    }
}
