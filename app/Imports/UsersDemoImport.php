<?php


// app/Imports/UsersDemoImport.php

namespace App\Imports;

use App\Models\UsersDemoImport as ModelsUsersDemoImport;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\RemembersRowNumber;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithUpserts;

// class UsersDemoImport implements ToModel, WithHeadingRow, WithUpserts,WithChunkReading
class UsersDemoImport implements ToModel, WithHeadingRow, WithUpserts,WithBatchInserts
// class UsersDemoImport implements ToModel, WithHeadingRow, WithChunkReading, WithUpserts,ShouldQueue
{
    use Importable,RemembersRowNumber;
    public function model(array $row)
    {
        return new ModelsUsersDemoImport($row);
    }

    public function chunkSize(): int
    {
        return 100;
    }
    public function uniqueBy()
    {
        return 'rsm_code'; // Replace 'email' with the actual column you want to use for upserts
    }
    public function batchSize(): int
    {
        return 500;
    }

}
