<?php

namespace App\Imports;

use App\Models\User;
use App\Services\UserIDGeneratorService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Illuminate\Contracts\Queue\ShouldBeQueued;
class UsersHierarchyImport implements ToCollection,ShouldQueue, WithHeadingRow, WithChunkReading, WithBatchInserts

// class UsersHierarchyImport implements ToCollection, WithHeadingRow, WithChunkReading,WithBatchInserts // Implement ShouldQueue interface
{

    public function collection(Collection $rows)
    {
        Log::info('Import started');
        $userTypes = [];
        $users = [];
        try {
        foreach ($rows as $row) {
            // Process each row
            $rsmCode = $row['rsm_code']??'';
            $asmCode = $row['asm_code']??'';
            $soCode = $row['so_user_code']??'';
            $distributorCode = $row['distributor_code']??'';
            $dsrCode = $row['dsr_code']??'';

            if (!empty($row['rsm_code'])) {
                if (!empty($row['rsm_code']) && !empty($row['rsmrbdm_name']) && !empty($row['region_name'])) {
                    $rsmData = $this->importRSM($row);
                    $userTypes[] = "RSM";
                    $users[] = $rsmData;
                }
                if (!empty($row['asm_code'])  && !empty($row['asm_name']) && !empty($row['asm_area_code']) && !empty($row['region_name'])) {
                    $asm = $this->importASM($row, $row['rsm_code']);
                    $userTypes[] = "ASM";
                    $users[] = $asm;

                }
                if (!empty($row['so_name']) && !empty($row['so_teritory_code']) && !empty($row['asm_area_code']) && !empty($row['region_name']) && $row['rsm_code'] && $row['asm_code']) {
                    $so = $this->importSO($row, $row['rsm_code'], $row['asm_code']);
                    $userTypes[] = "SO";
                    $users[] = $so;
                }


                if (!empty($row['distributor_name']) && !empty($row['distributor_code']) && !empty($row['so_teritory_code']) && !empty($row['asm_area_code']) && !empty($row['region_name']) && $row['rsm_code'] && $row['asm_code'] && $row['so_user_code']) {
                    $distributor = $this->importDistributor($row, $row['rsm_code'], $row['asm_code'], $row['so_user_code']);
                    $userTypes[] = "DISTRIBUTOR";
                    $users[] = $distributor;
                }

                if (!empty($row['dsr_name']) && !empty($row['dsr_code']) && $row['rsm_code'] && $row['asm_code'] && $row['so_user_code'] && $row['distributor_code']) {
                    $dsr = $this->importDSR($row, $row['rsm_code'], $row['asm_code'], $row['so_user_code'], $row['distributor_code']);
                    $userTypes[] = "DSR";
                    $users[] = $dsr;
                }
            }
        }

        // Sync roles for all users
       User::whereIn('user_type', $userTypes)->each(function ($user) {
           $user->syncRoles($user->user_type);
       });
    } catch (\Exception $e) {
        Log::error('Error UserIMport class import: ' . $e->getMessage());
        // You can handle the error here, such as logging, sending notifications, etc.
    }

        return $users;
    }
    private function isEmptyRow($row)
    {
        foreach ($row as $value) {
            if (!empty($value)) {
                return false; // If any value is not empty, return false
            }
        }
        return true; // All values are empty
    }

    private function importRSM($row)
    {
        $userId = $row['rsm_code'];
        return User::updateOrCreate(
            ['user_id' => $userId, 'user_type' => 'RSM'],
            array_merge($this->getCommonFields($row, $userId), [
                'user_id' => $userId,
                'region_code' => $row['rsm_region_code']??'',
                'name' => $row['rsmrbdm_name'],
            ])
        );
    }

    private function updateUserHierarchy($where, $userData)
    {
        try {
            $user = User::updateOrCreate($where, $userData);
            Log::info('process data: ' . print_r($userData));
            return User::where($where)->first();

            // return $user;
        } catch (\Throwable $th) {
            Log::error('Failed to import user row: ' . $th->getMessage());
            return false;
        }
    }

    private function importASM($row, $rsmId)
    {
//        Log::info('importASM user info:', $row->toArray());
        $userId = $row['asm_code'];
        return User::updateOrCreate(
            ['user_id' => $userId, 'user_type' => 'ASM'],
            array_merge($this->getCommonFields($row,$userId), [
                'rsm_id' => $rsmId,
                'user_id' => $userId,
                'area_code' => $row['asm_area_code'],
                'name' => $row['asm_name'],
            ])
        );
    }

    private function importSO($row, $rsmId, $asmIdOrCode)
    {
        $userId = $row['so_user_code'];
        return User::updateOrCreate(
            ['user_id' => $userId, 'user_type' => 'SO'],
            array_merge($this->getCommonFields($row, $userId), [
                'rsm_id' => $rsmId,
                'asm_id' => $asmIdOrCode,
                'user_id' => $userId,
                'teritory_code' => $row['so_teritory_code'],
                'mobile_number' => $row['so_contact_number'],
                'name' => $row['so_name'],
            ])
        );
    }

    private function importDistributor($row, $rsmId, $asmIdOrCode, $soIdOrCode)
    {
        $userId = $row['distributor_code'];
        return User::updateOrCreate(
            ['user_id' => $userId, 'user_type' => 'DISTRIBUTOR'],
            array_merge($this->getCommonFields($row, $userId), [
                'rsm_id' => $rsmId,
                'asm_id' => $asmIdOrCode,
                'so_id' => $soIdOrCode,
                'user_id' => $userId,
                'name' => $row['distributor_name']
            ])
        );
    }

    private function importDSR($row, $rsmId, $asmIdOrCode, $soIdOrCode, $distributorCode,)
    {
        $userId = $row['dsr_code'];
        return User::updateOrCreate(
            ['user_id' => $userId, 'user_type' => 'DSR'],
            array_merge($this->getCommonFields($row, $userId), [
                'rsm_id' => $rsmId,
                'asm_id' => $asmIdOrCode,
                'so_id' => $soIdOrCode,
                'distributor_id' => $distributorCode,
                // 'retailer_id' => $retailerCode,
                'mobile_number' => $row['dsr_contact_no'],
                'user_id' => $userId,
                'name' => $row['dsr_name'],
            ])
        );
    }

    private function getUserID($userRole)
    {
        return UserIDGeneratorService::generateUserID($userRole);
    }

    private function getCommonFields($row, $userId)
    {
        return [
            'region' => $row['region_name'] ?? '',
            'channel_type' => $row['channel'] ?? '',
            'city' => $row['city'] ?? '',
            'state' => $row['state'] ?? '',
            'pin_code' => $row['pincode'] ?? '',
            'cfa_code' => $row['cfa_code'] ?? '',
            'password' => bcrypt($userId),
            'status' => 1,
        ];
    }

    public function chunkSize(): int
    {
        return 1000; // Adjust the chunk size as per your requirement
    }
    public function batchSize(): int
    {
        return 500;
    }
}
