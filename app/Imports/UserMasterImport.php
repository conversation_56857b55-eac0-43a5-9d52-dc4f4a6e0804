<?php

namespace App\Imports;

use App\Jobs\AssetInventoryUploadJob;
use App\Jobs\AssetPlacementApprovalHistoryJob;
use App\Jobs\AssetTaskCompletePlacementJob;
use App\Jobs\AssignAssetToPlacementJob;
use App\Jobs\DBToCFAVendorMappingJob;
use App\Jobs\ProcessAssetPlacementRequestJob;
use App\Jobs\ProcessFailedUpload;
use App\Jobs\ProcessUserImportRow;
use App\Jobs\UpdateRetailer;
use App\Jobs\UpdateUserRetailerGSTupdateJob;
use App\Library\Utils\DateFormate;
use App\Models\AssetPlacementRequest;
use App\Models\User;
use App\Services\AssetInventoryService;
use App\Services\ErrorService\ErrorHandlingService;
use App\Services\HeirarchyService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Services\UserValidationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\QueryException;
use Throwable;

class UserMasterImport implements ToCollection, WithHeadingRow, WithChunkReading, WithBatchInserts, ShouldQueue
{
    protected $userRole;
    protected $batchID;
    protected $ip;

    public $totalRecords = 0;
    public $successRecords = 0;
    public $failedRecords = 0;
    public $failedData = [];
    protected $authUser;

    // Performance optimization properties
    public $timeout = 3600; // 1 hour timeout for large imports
    public $tries = 3; // Retry failed jobs 3 times
    public $maxExceptions = 10; // Allow up to 10 exceptions before failing

    // Batch processing cache
    private $validationCache = [];
    private $hierarchyCache = [];
    private $assetCache = [];

    public function __construct($userRole, $batchID = '', $ip = '')
    {
        $this->userRole = $userRole;
        $this->batchID = $batchID;
        $this->ip = $ip;
        //        $this->authUser = Auth::user();
        $this->authUser = auth()->user();
    }

    public function collection(Collection $rows)
    {
        // Initialize performance monitoring
        $startTime = microtime(true);
        $memoryStart = memory_get_usage();

        $users = [];
        $userValidationService = app(UserValidationService::class);

        // Optimized chunk size for large datasets
        $chunkSize = $this->getOptimalChunkSize($rows->count());

        Log::info("Starting import process", [
            'total_rows' => $rows->count(),
            'chunk_size' => $chunkSize,
            'user_role' => $this->userRole,
            'batch_id' => $this->batchID
        ]);

        // Pre-load validation cache for better performance
        $this->preloadValidationCache($userValidationService);

        $rows->chunk($chunkSize)->each(function ($chunk, $chunkIndex) use ($userValidationService, $startTime) {
            $this->processChunk($chunk, $chunkIndex, $userValidationService, $startTime);
        });

        // Log final statistics
        $this->logFinalStatistics($startTime, $memoryStart);
        $this->logUploadHistory($this->batchID);

        return $users;
    }

    /**
     * Process a single chunk of data
     */
    private function processChunk($chunk, $chunkIndex, $userValidationService, $startTime)
    {
        $chunkStartTime = microtime(true);
        $processedInChunk = 0;

            try {
                if ($this->isEmptyRow($row)) {
                    continue; // Skip the row if it's empty
                }

                $this->totalRecords++;
                $processedInChunk++;

                if ($this->isValidRowCached($row, $userValidationService)) {
                    $this->successRecords++;
                    $prepareUserData = $this->prepareUserDataCached($row);

                    // Dispatch jobs based on user role with optimized batching
                    $this->dispatchRoleBasedJob($prepareUserData, $row);
                } else {
                    $this->failedRecords++;
                    $this->failedData[] = $row->toArray();
                    $this->failedDataErrorLog('Validation failed', $row->toArray());
                }
            } catch (QueryException $e) {
                $this->handleRowException($e, $row);
            } catch (\Exception $e) {
                $this->handleRowException($e, $row);
            }

            $processedInChunk++;
        }

        // Log chunk completion
        $chunkTime = microtime(true) - $chunkStartTime;
        Log::info("Chunk {$chunkIndex} completed", [
            'processed_rows' => $processedInChunk,
            'chunk_time' => round($chunkTime, 2),
            'memory_usage' => $this->formatBytes(memory_get_usage())
        ]);
    }

    /**
     * Dispatch role-based jobs with optimized processing
     */
    private function dispatchRoleBasedJob($prepareUserData, $row)
    {
        if ($this->userRole == "db_to_cfa_mapping") {
            DBToCFAVendorMappingJob::dispatch($prepareUserData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
        } elseif ($this->userRole == "retailer_lat_long") {
            UpdateRetailer::dispatch($prepareUserData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
        } elseif ($this->userRole == "retailer_gst_update") {
            UpdateUserRetailerGSTupdateJob::dispatch($prepareUserData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
        } elseif ($this->userRole == "asset_inventory_upload") {
            $this->handleAssetInventoryUpload($prepareUserData);
            $this->handleAssetRetailerMapping($prepareUserData);
        } else {
            $this->handleStandardUserImport($prepareUserData, $row);
        }
    }

    /**
     * Handle row-level exceptions
     */
    private function handleRowException($exception, $row)
    {
        $this->failedRecords++;
        $this->failedData[] = $row->toArray();
        $this->failedDataErrorLog($exception->getMessage(), $row->toArray());
        Log::error('Row processing error: ' . $exception->getMessage(), ['row_data' => $row->toArray()]);
    }

    /**
     * Get optimal chunk size based on total rows and available memory
     */
    private function getOptimalChunkSize($totalRows)
    {
        $memoryLimit = $this->getMemoryLimitInBytes();
        $availableMemory = $memoryLimit - memory_get_usage();

        // Calculate optimal chunk size based on available memory
        // Assume each row takes approximately 2KB of memory
        $estimatedRowSize = 2048;
        $maxChunkByMemory = max(100, intval($availableMemory / ($estimatedRowSize * 10))); // Keep 10x buffer

        // Adaptive chunk size based on total rows
        if ($totalRows <= 1000) {
            return min(500, $maxChunkByMemory);
        } elseif ($totalRows <= 10000) {
            return min(1000, $maxChunkByMemory);
        } elseif ($totalRows <= 100000) {
            return min(2000, $maxChunkByMemory);
        } else {
            return min(5000, $maxChunkByMemory);
        }
    }

    /**
     * Pre-load validation cache for better performance
     */
    private function preloadValidationCache($userValidationService)
    {
        $this->validationCache['required_fields'] = $userValidationService->getUserTypeFields();
        $this->validationCache['user_role'] = $this->userRole;

        // Cache common validation patterns
        $this->validationCache['patterns'] = [
            'email' => '/^[^\s@]+@[^\s@]+\.[^\s@]+$/',
            'mobile' => '/^[0-9]{10}$/',
            'pincode' => '/^[0-9]{6}$/'
        ];
    }

    /**
     * Cached validation for better performance
     */
    private function isValidRowCached($row, $userValidationService)
    {
        $cacheKey = md5(serialize($row->toArray()));

        if (isset($this->validationCache['results'][$cacheKey])) {
            return $this->validationCache['results'][$cacheKey];
        }

        $result = $this->isValidRow($row, $userValidationService);
        $this->validationCache['results'][$cacheKey] = $result;

        return $result;
    }

    /**
     * Cached data preparation for better performance
     */
    private function prepareUserDataCached($row)
    {
        $cacheKey = md5(serialize($row->toArray()) . $this->userRole);

        if (isset($this->validationCache['prepared_data'][$cacheKey])) {
            return $this->validationCache['prepared_data'][$cacheKey];
        }

        $result = $this->prepareUserData($row);
        $this->validationCache['prepared_data'][$cacheKey] = $result;

        return $result;
    }

    /**
     * Handle asset inventory upload with optimizations
     */
    private function handleAssetInventoryUpload($prepareUserData)
    {
        $loginUser = Auth::user();
        $userType = $loginUser->user_type;
        $userID = $loginUser->user_id;

        $prepareUserData['batchID'] = $this->batchID;
        $prepareUserData['uploadBy'] = $userID;
        $prepareUserData['uploadByName'] = $loginUser->name;
        $prepareUserData['uploadTime'] = now();

        // Cache asset validation for better performance
        $assetValidate = $this->getCachedAssetValidation($prepareUserData);
        $prepareUserData['asset_type'] = $assetValidate['asset_type'];
        $prepareUserData['asset_code'] = $assetValidate['asset_code'];

        AssetInventoryUploadJob::dispatch($prepareUserData, $userType, $userID, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
    }

    /**
     * Handle asset retailer mapping with optimizations
     */
    private function handleAssetRetailerMapping($prepareUserData)
    {
        DB::beginTransaction();
        try {
            if (empty($prepareUserData['request_number'])) {
                $request_number = PlacementRequestService::generateRequestNumber();
                $prepareUserData['request_number'] = $request_number;
            }

            // Validate asset upload data with caching
            $assetValidate = $this->getCachedAssetValidation($prepareUserData, 'placement');
            $prepareUserData['asset_type_code'] = $assetValidate['asset_type'];
            $prepareUserData['eligible_chiller_type'] = $assetValidate['asset_code'];

            if (AssetPlacementRequest::where('request_number', $prepareUserData['request_number'])->exists()) {
                throw new \Exception("Placement request with this request number already exists.");
            }

            // Validate pending from and request status
            $this->validatePlacementRequest($prepareUserData);

            // Validate hierarchy with caching
            $this->validateHierarchyCached($prepareUserData, $this->userRole);

            // Handle asset existence check
            $this->validateAssetExistence($prepareUserData);

            $user = User::where('user_id', $prepareUserData['outlet_code'])->with('outletHasOne')->first();

            $this->processPlacementRequest($prepareUserData, $user);
            AssetPlacementApprovalHistoryJob::dispatch($prepareUserData, $user);
            $this->processTaskCompletePlacementRequest($prepareUserData, $user);

            if (strtolower($prepareUserData['request_status']) == 'deployed') {
                AssignAssetToPlacementJob::dispatch($prepareUserData, $user, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
            }

            DB::commit();
        } catch (\Exception $e) {
            $this->failedRecords++;
            $this->failedData[] = $prepareUserData;
            DB::rollBack();
            $this->failedDataErrorLog($e->getMessage(), $prepareUserData);
            throw $e;
        }
    }

    /**
     * Handle standard user import with optimizations
     */
    private function handleStandardUserImport($prepareUserData, $row)
    {
        try {
            $userId = $this->getUserID($row);
            $userRole = $this->userRole == "CFA" ? "VENDOR" : $this->userRole;

            if (empty($userId)) {
                throw new \Exception($this->userRole . " Code is missing.");
            }

            $userData = [
                'user_id' => $userId,
                'user_type' => $userRole,
                'email' => $row['email_id'] ?? '',
                'mobile_number' => $row['mobile_number'] ?? '',
                'rsm_id' => $prepareUserData['rsm_id'] ?? '',
                'asm_id' => $prepareUserData['asm_id'] ?? '',
                'so_id' => $prepareUserData['so_id'] ?? '',
                'distributor_id' => $prepareUserData['distributor_id'] ?? '',
                'dsr_id' => $prepareUserData['dsr_id'] ?? '',
                'rtmm_code' => $prepareUserData['rtmm_code'] ?? '',
                'ae_id' => $prepareUserData['ae_id'] ?? 'AE_001',
                'cfa_code' => $prepareUserData['cfa_code'] ?? '',
                'batchID' => $this->batchID,
                'uploadBy' => $this->authUser->user_id ?? '',
                'uploadByName' => $this->authUser->name ?? '',
                'uploadTime' => now(),
            ];

            if ($this->userRole != 'RETAILER') {
                $userData['password'] = bcrypt($userId);
            }

            $this->validateHierarchyCached($userData, $userRole);
            $userData = array_merge($userData, $prepareUserData);

            ProcessUserImportRow::dispatch($userId, $userRole, $userData, $prepareUserData, $row, $this->authUser, request()->ip());
        } catch (\Exception $e) {
            $this->failedRecords++;
            $this->failedData[] = $row->toArray();
            $this->failedDataErrorLog($e->getMessage(), $row->toArray());
            Log::error('ProcessUserImportRow:', $row->toArray());
            throw $e;
        }
    }

    private function isEmptyRow($row)
    {
        foreach ($row as $value) {
            if (!empty($value)) {
                return false; // If any value is not empty, return false
            }
        }
        return true; // All values are empty
    }

    private function isValidRow($row, UserValidationService $userValidationService)
    {
        try {
            $requiredFields = $userValidationService->getUserTypeFields();

            if ($row->filter()->isEmpty()) {
                Log::error('isValidRow => Validation failed for row: All columns are empty.', $row->toArray());

                return false;
            }

            $missingFields = collect($requiredFields['validate'] ?? [])->filter(function ($field) use ($row) {
                return !isset($row[$field]) || empty($row[$field]);
            })->all();


            if (!empty($missingFields)) {
                throw new \Exception('Validation failed for row: Missing columns - ' . implode(', ', $missingFields));
                //                $this->failedRecords++;
                //                $this->failedDataErrorLog('Validation failed for row: Missing columns - ' . implode(', ', $missingFields), $row);
                //                Log::error('Validation failed for row: Missing columns - ' . implode(', ', $missingFields));
                //                return false;
            }
            $requiredFields = $userValidationService->getUserTypeFields()[$this->userRole]['validate'] ?? [];
            $requiredMissingFields = [];
            foreach ($requiredFields as $field) {
                if (empty($row[$field])) {
                    $requiredMissingFields[] = $field;
                }
            }
            if (!empty($requiredMissingFields)) {
                throw new \Exception('Validation failed: Missing or empty fields - ' . implode(', ', $requiredMissingFields));
            }

            return true;
        } catch (\Exception $e) {
            $this->failedRecords++;
            $this->failedDataErrorLog($e->getMessage(), $row);
            Log::error('Error validating row: ' . $e->getMessage());
            return false;
        }
    }

    private function prepareUserData($row)
    {
        $userData = [];

        $fieldMappings = app(UserValidationService::class)->getUserTypeFields()[$this->userRole]['form'] ?? [];

        foreach ($fieldMappings as $excelField) {
            $databaseField = app(UserValidationService::class)->mapExcelToDatabaseFields($this->userRole, $excelField);
            if ($databaseField && isset($row[$excelField])) {
                // $userData[$databaseField] = (string) trim($row[$excelField]);
                if (in_array($databaseField, ['effective_from', 'effective_to', 'retailer_created_on', 'modified_date'])) {
                    // $userData[$databaseField] = now();
                    // $userData[$databaseField] = Carbon::createFromFormat('Y-m-d', $row[$excelField])->format('Y-m-d');
                    $userData[$databaseField] = $this->convertDateFormatToDbFormat($row[$excelField]);
                } else {
                    // $userData[$databaseField] = $row[$excelField];
                    $userData[$databaseField] = (string) trim($row[$excelField]);
                }
            }
        }
        return $userData;
    }

    private function getUserID($row)
    {
        $userIDList = [
            "RSM" => "rsmrbdm_code",
            "ASM" => "asm_code",
            "DSR" => "dsr_code",
            "DISTRIBUTOR" => "distributor_code",
            "SO" => "so_user_code",
            "VENDOR" => "cfaplant_code",
            "RETAILER" => "retailercode",
            "VE" => "user_code",
        ];

        $userId = $row[$userIDList[$this->userRole]] ?? null;

        // if (!$userId) {
        //     $userId = UserIDGeneratorService::generateUserID($this->userRole);
        // }

        return $userId;
    }

    private function convertDateFormatToDbFormat($excelField, $timestamp = '')
    {
        $format = $timestamp == 'time' ? 'Y-m-d H:i:s' : '';
        return DateFormate::convertDbFormat($excelField, $format);
    }


    /**
     * Get cached asset validation
     */
    private function getCachedAssetValidation($prepareUserData, $type = 'inventory')
    {
        $key = $type === 'placement' ? $prepareUserData['eligible_chiller_type'] : $prepareUserData['asset_type'] ?? '';
        $cacheKey = $type . '_' . $key;

        if (isset($this->assetCache[$cacheKey])) {
            return $this->assetCache[$cacheKey];
        }

        if ($type === 'placement') {
            $result = AssetInventoryService::placementAssetTypeCode($key);
        } else {
            $result = AssetInventoryService::validateAsset($prepareUserData);
        }

        $this->assetCache[$cacheKey] = $result;
        return $result;
    }

    /**
     * Validate placement request with optimizations
     */
    private function validatePlacementRequest($prepareUserData)
    {
        $pendingFrom = ['AE', 'RSM', 'ASM', 'VENDOR', 'VE'];
        $request_pending_from = strtoupper($prepareUserData['request_pending_from']);
        $request_Status_list = ['deployed', 'rejected', 'cancelled', 'pending'];
        $request_Status = strtolower($prepareUserData['request_status']);

        if (!in_array($request_pending_from, $pendingFrom)) {
            throw new \Exception("Invalid Pending From");
        }

        if (!in_array($request_Status, $request_Status_list)) {
            throw new \Exception("Invalid Request Status");
        }

        if ($request_Status == 'deployed') {
            if (empty($prepareUserData['asset_serial_number']) || empty($prepareUserData['asset_barcode'])) {
                throw new \Exception("Asset Serial Number OR Asset barcode is Required");
            }
        }
    }

    /**
     * Validate asset existence with optimizations
     */
    private function validateAssetExistence($prepareUserData)
    {
        $request_Status = strtolower($prepareUserData['request_status']);

        if ($request_Status == 'deployed') {
            $serialBarcodeExist = AssetPlacementRequest::where('asset_serial_number', $prepareUserData['asset_serial_number'])
                ->when(!empty($prepareUserData['asset_barcode']), function ($query) use ($prepareUserData) {
                    return $query->orWhere('asset_barcode', $prepareUserData['asset_barcode']);
                })->exists();

            if ($serialBarcodeExist) {
                throw new \Exception("Asset Serial Number/BarCode Already Exist");
            }
        }
    }

    /**
     * Cached hierarchy validation
     */
    private function validateHierarchyCached($userData, $userRole)
    {
        $cacheKey = md5(serialize($userData) . $userRole);

        if (isset($this->hierarchyCache[$cacheKey])) {
            return $this->hierarchyCache[$cacheKey];
        }

        $result = HeirarchyService::validateHeirarchy($userData, $userRole);
        $this->hierarchyCache[$cacheKey] = $result;

        return $result;
    }

    /**
     * Get memory limit in bytes
     */
    private function getMemoryLimitInBytes()
    {
        $memoryLimit = ini_get('memory_limit');

        if ($memoryLimit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) $memoryLimit;

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }

    /**
     * Format bytes for logging
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Log final statistics
     */
    private function logFinalStatistics($startTime, $memoryStart)
    {
        $endTime = microtime(true);
        $memoryEnd = memory_get_usage();
        $peakMemory = memory_get_peak_usage();

        Log::info("Import completed", [
            'total_records' => $this->totalRecords,
            'success_records' => $this->successRecords,
            'failed_records' => $this->failedRecords,
            'success_rate' => $this->totalRecords > 0 ? round(($this->successRecords / $this->totalRecords) * 100, 2) . '%' : '0%',
            'execution_time' => round($endTime - $startTime, 2) . ' seconds',
            'memory_used' => $this->formatBytes($memoryEnd - $memoryStart),
            'peak_memory' => $this->formatBytes($peakMemory),
            'batch_id' => $this->batchID,
            'user_role' => $this->userRole
        ]);
    }

    public function chunkSize(): int
    {
        // Dynamic chunk size based on available memory and user role
        $memoryLimit = $this->getMemoryLimitInBytes();
        $availableMemory = $memoryLimit - memory_get_usage();

        // Base chunk size on memory and complexity of user role
        $complexRoles = ['asset_retailer_mapping', 'asset_inventory_upload'];
        $baseChunkSize = in_array($this->userRole, $complexRoles) ? 1000 : 3000;

        // Adjust based on available memory
        $memoryBasedChunk = max(500, intval($availableMemory / (2048 * 20))); // 2KB per row with 20x buffer

        return min($baseChunkSize, $memoryBasedChunk, 10000); // Cap at 10k for safety
    }

    public function batchSize(): int
    {
        // Smaller batch size for complex operations
        $complexRoles = ['asset_retailer_mapping', 'asset_inventory_upload'];
        return in_array($this->userRole, $complexRoles) ? 500 : 2000;
    }


    public function processPlacementRequest($amprocess, $user)
    {

        $amprocess['request_status'] = strtolower($amprocess['request_status']);
        $amprocess['request_pending_from'] = strtoupper($amprocess['request_pending_from']);
        $aprData = [
            'user_id' => $amprocess['so_code'],
            'asset_type_code' => $amprocess['asset_type_code'],
            'outlet_id' => $user->outletHasOne->id ?? '',
            'type' => 'SO',
            'expected_vpo' => $amprocess['expected_vpo'] ?? '',
            'request_number' => $amprocess['request_number'] ?? '',
            'eligible_chiller_type' => $amprocess['eligible_chiller_type'] ?? null,
            'request_type' => 'Normal',
            // 'additional_equipment' => json_encode([]),
            // 'competitor_chiller_size' => json_encode([]),
            // 'competitor_company' => json_encode([]),
            'chiller_location' => 'Others',
            // 'chiller_location_photo' => 'NULL',
            'customer_address' => $amprocess['customer_address'] ?? '',
            'customer_location' => $amprocess['customer_location'] ?? '',
            // 'current_location' => $amprocess['current_location'],
            // 'latitude' => $user->outletHasOne->lat ?? 0.0,
            // 'longitude' => $user->outletHasOne->long ?? 0.0,
            'correct_location' => $amprocess['correct_location'] ?? 'No',
            'remarks' => 'ivy asset mapping =>' . now(),
            'asset_mapping_type' => 'ivy',
            'consent_status' => 'Confirmed',
            'vpo_target' => $amprocess['vpo'],
            "outlet_code" => $amprocess['outlet_code'],
            "ae_code" => $amprocess['ae_code'],
            "rsm_code" => $amprocess['rsm_code'],
            "asm_code" => $amprocess['asm_code'],
            "so_code" => $amprocess['so_code'],
            "db_code" => $amprocess['db_code'],
            "dsr_code" => $amprocess['dsr_code'],
            "cfa_code" => $amprocess['cfa_code'],
            "asset_serial_number" => $amprocess['asset_serial_number'] ?? '',
            "asset_barcode" => $amprocess['asset_barcode'] ?? '',
            "asset_assigned_status" => 'Approved',
            "address_proof" => 'NULL',
            "retailer_photo" => 'NULL',
            "mobile_number" => 'NULL',
            "pincode" => $user->outletHasOne->pincode ?? '',
            "signature" => 'NULL',
            "approved_time" => now(),
            "placement_approved_time" => $amprocess['request_status'] != 'rejected' ? now() : null,
            "deployment_date" => isset($amprocess['deployment_date']) ? $this->convertDateFormatToDbFormat($amprocess['deployment_date'], 'time') : now(),
            // 'approved_by_user_id' => $amprocess['cfa_code'] ?? '',
            // 'approved_by_user_role' => 'VENDOR',
            // 'pending_from' => 'VE',
            // 'is_deploy' => 1,
            // 'is_quantity_allocated' => 'YES',
            'expected_deployment_date' => isset($amprocess['deployment_date']) ? $this->convertDateFormatToDbFormat($amprocess['deployment_date'], 'time') : now(),
            'assigned_organization' => $amprocess['cfa_code'] . "_001",
            // 'chiller_placed' => 'Yes',
            // 'task_status' => 'Completed',
            'pending_from' => $amprocess['request_pending_from'] ?? 'ASM',
            'batchID' => $this->batchID,
            'uploadBy' => $this->authUser->user_id ?? '',
            'uploadByName' => $this->authUser->name ?? '',
            'uploadTime' => now(),
        ];

        if ($amprocess['request_status'] == 'deployed') {
            $aprData['approved_by_user_id'] = !empty($amprocess['cfa_code']) ? $amprocess['cfa_code'] . '_001' : '';
            $aprData['approved_by_user_role'] = 'VENDOR';
            $aprData['pending_from'] = 'VE';
            $aprData['is_deploy'] = 1;
            $aprData['chiller_placed'] = 'Yes';
            $aprData['task_status'] = 'Completed';
            $aprData['is_quantity_allocated'] = 'YES';
        } elseif ($amprocess['request_status'] == 'rejected') {
            $aprData['chiller_placed'] = 'No';
            $aprData['task_status'] = 'Pending';
            $aprData['is_deploy'] = 0;
            $aprData['rejected_by_user_id'] = $this->getHeirarchyUserID($amprocess['request_pending_from'], $amprocess);
            $aprData['rejected_by_user_role'] = $amprocess['request_pending_from'];
            $aprData['placement_rejected_time'] = now();
            $aprData['approved_by_user_id'] = '';
            $aprData['approved_by_user_role'] = '';
            $aprData['is_quantity_allocated'] = 'NO';
            $aprData['rejection_reason'] = 'IVY cancelled this request and upload from admin user master, date: ' . now();
        } elseif ($amprocess['request_status'] == 'cancelled') {
            $aprData['approved_by_user_id'] = $amprocess['cfa_code'] ?? '';
            $aprData['approved_by_user_role'] = 'VENDOR';
            $aprData['pending_from'] = 'VE';
            $aprData['is_deploy'] = 0;
            $aprData['chiller_placed'] = 'No';
            $aprData['task_status'] = 'Completed';
            $aprData['is_quantity_allocated'] = 'NO';
        } else {
            $aprData['approved_by_user_id'] = $this->getHeirarchyUserID($amprocess['request_pending_from'], $amprocess);
            $aprData['approved_by_user_role'] = $amprocess['request_pending_from'];
            $aprData['pending_from'] = $amprocess['request_pending_from'] ?? 'ASM';
            $aprData['is_deploy'] = 0;
            $aprData['chiller_placed'] = 'No';
            $aprData['task_status'] = 'Pending';
            $aprData['is_quantity_allocated'] = 'NO';
        }
        ProcessAssetPlacementRequestJob::dispatch($aprData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
    }

    public function getHeirarchyUserID($pendingFrom, $heirarchy)
    {
        if (!isset($pendingFrom) || !is_array($heirarchy)) {
            return '';
        }
        $data = [
            'ASM' => $heirarchy['asm_code'],
            'RSM' => $heirarchy['rsm_code'],
            'AE' => $heirarchy['ae_code'],
            'VENDOR' => $heirarchy['cfa_code'],
            'VE' => $heirarchy['cfa_code']
        ];
        return $data[$pendingFrom] ?? '';
    }

    public function processTaskCompletePlacementRequest($amprocess, $user)
    {
        $outlet = $user->outletHasOne ?? null;

        $latitude = $outlet ? $outlet->lat : null;
        $longitude = $outlet ? $outlet->long : null;
        $chillerPlaced = $amprocess['request_status'] == 'deployed' ? 'Yes' : 'No';
        $aprData = [
            'asset_barcode' => $amprocess['asset_barcode'] ?? '',
            'asset_number' => $amprocess['asset_serial_number'] ?? '',
            'chiller_placed' => $chillerPlaced,
            'is_Deploy' => 0,
            'correct_location' => 'Yes',
            'pending_from' => 'VE',
            'current_location' => $amprocess['customer_address'],
            'latitude' => $latitude,
            'longitude' => $longitude,
            'outlet_id' => optional($user->outletHasOne)->id ?? '',
            'remarks' => 'ivy task completed',
            'request_number' => $amprocess['request_number'] ?? '',
            'user_id' => $amprocess['cfa_code'] . "_001",
            'approved_by_user_role' => 'VE',
            'task_status' => 'Pending',
            'approved_by_user_id' => $amprocess['cfa_code'] . "_001",
            'outlet_code' => $amprocess['outlet_code'],
            "ae_code" => $amprocess['ae_code'],
            "rsm_code" => $amprocess['rsm_code'],
            "asm_code" => $amprocess['asm_code'],
            "so_code" => $amprocess['so_code'],
            "db_code" => $amprocess['db_code'],
            "dsr_code" => $amprocess['dsr_code'],
            "cfa_code" => $amprocess['cfa_code'],
            "completion_date" => now(),
            'batchID' => $this->batchID,
            'uploadBy' => $this->authUser->user_id ?? '',
            'uploadByName' => $this->authUser->name ?? '',
            'uploadTime' => now(),
        ];
        Log::info('Handling processTaskCompletePlacementRequest', $aprData);
        AssetTaskCompletePlacementJob::dispatch($aprData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
    }

    public function failedDataErrorLog($message, $prepareUserData)
    {
        $authUser = $this->authUser;
        ErrorHandlingService::handleErrorLog($this->batchID, $message, request()->ip(), $authUser->user_type, $this->userRole, $prepareUserData, $authUser->user_id, $authUser->name);
    }

    private function logUploadHistory($batchID)
    {
        $authUser = $this->authUser;
        ErrorHandlingService::logUploadHistory($batchID, $this->totalRecords, $this->successRecords, $this->failedRecords, $this->failedData, $authUser->user_id, $authUser->name, request()->ip());
    }
}
