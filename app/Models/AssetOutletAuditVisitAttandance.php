<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetOutletAuditVisitAttandance extends Model
{
    use HasFactory;
    protected $table = 'asset_outlet_audit_visit_attandance';
    protected $fillable = [
       'user_id', 'user_type', 'visit_type', 'checkin_time', 'checkout_time', 'outlet_id', 'checkin_latitude', 'checkin_longitude', 'checkin_distance',
        'checkout_latitude', 'checkout_longitude', 'checkout_distance'
    ];
}
