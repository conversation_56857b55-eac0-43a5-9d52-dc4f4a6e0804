<?php

namespace App\Models;

use App\Models\AssetTaskPlacement\AssetPlacementTaskDeploymentModel;
use App\Models\AssetTaskPullout\AssetPulloutTaskDeploymentModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetPulloutRequest extends Model
{
    use HasFactory;
    protected $table = "asset_pullout_requets";

    protected $fillable = [
        'user_id',
        'outlet_id',
        'type',
        'expected_vpo',
        'current_vpo',
        'chiller_type',
        'asset_type_code',
        'request_type',
        'additional_equipment',
        'competitor_chiller_size',
        'competitor_company',
        'competitor_chiller_photo',
        'chiller_location',
        'chiller_location_photo',
        'address_proof',
        'retailer_photo',
        'signature',
        'mobile_number',
        'customer_address',
        'pincode',
        'customer_location',
        'current_location',
        'latitude',
        'longitude',
        'correct_location',
        'distance',
        'remarks',
        'consent_status',
        'placement_request_number',
        'asset_assigned_status',
        'pullout_date',
        'is_pullout',
        'pullout_request_number',
        'pullout_complete_date',
        'pullout_request_complete',
        'pullout_request_by',
        'pullout_request_by_name',
        'approved_time',
        'deployment_date',
        'rejection_reason',
        'is_deploy',
        'vpo_target',
        'approved_by_user_role',
        'approved_by_user_id',
        'pending_from',
        'rejected_by_user_id',
        'rejected_by_user_role',
        'pullout_approved_time',
        'pullout_rejected_time',
        'is_quantity_allocated',
        'expected_pullout_date',
        'assigned_organization',
        'pullout_placed',
        'task_status',
        'outlet_code',
        'ae_code',
        'rsm_code',
        'asm_code',
        'so_code',
        'db_code',
        'dsr_code',
        'cfa_code',
        'challan_number',
        'asset_barcode',
        'asset_serial_number',
        'ScannedBarcode',
        'asset_id',
        'customer_name',
        'customer_code',
        'contact_person',
        'contact_number',
        'pullout_asset_type',
        'pullout_reason',
        'email',
        'created_at',
        'updated_at',
        'chiller_pullout',
    ];

    public function outletUser()
    {
        return $this->hasOne(User::class, 'user_id', 'outlet_code');
    }
    public function retaileroutlets()
    {
        return $this->hasOne(RetailerOutlet::class, "id", "outlet_id");
    }

    public function outlet()
    {
        return $this->belongsTo(RetailerOutlet::class, 'outlet_id')->select('id','user_id','salesman_code','salesman_name','region','channel_code','class_name','category_name','route_name','invoiceaddress','contact_name');
    }
    public function retailer()
    {
        return $this->belongsTo(User::class, 'outlet_code', 'user_id')->select('address_1','pin_code','region','user_id','name','city','mobile_number','state');
    }
    public function rsm()
    {
        return $this->belongsTo(User::class, 'rsm_code', 'user_id')->select('user_id', 'name','region');
    }
    public function asm()
    {
        return $this->belongsTo(User::class, 'asm_code', 'user_id')->select('user_id', 'name','channel_type','area_code');
    }
    public function so()
    {
        return $this->belongsTo(User::class, 'so_code', 'user_id')->select('user_id', 'name','teritory_code');
    }
    public function distributor()
    {
        return $this->belongsTo(User::class, 'db_code', 'user_id')->select('user_id', 'name','cfa_code');
    }
    public function dsr()
    {
        return $this->belongsTo(User::class, 'dsr_code', 'user_id')->select('user_id', 'name');
    }
    public function cfa()
    {
        return $this->belongsTo(User::class, 'cfa_code', 'user_id')->select('user_id', 'name','gst_no','address_1');
    }

    public function approvalHistory()
    {
        return $this->hasMany(AssetApprovalPlacementRequestHistory::class, 'asset_placement_request_id', 'pullout_request_number');
    }
    public function placementAssets()
    {
        return $this->hasOne(OutletAssetDetailsModel::class, 'placement_request_number', 'pullout_request_number');
    }
    public function taskDeploy()
    {
        return $this->hasOne(AssetPulloutTaskDeploymentModel::class, 'pullout_request_number', 'pullout_request_number');
    }
    public function inventory()
    {
        return $this->belongsTo(AsssetInventory::class, 'cfa_code', 'warehouse_code');
    }

    public function inventoryOne()
    {
        return $this->hasOne(AsssetInventory::class, 'serial_number', 'asset_serial_number');
    }
    public function userOne()
    {
        return $this->hasOne(User::class,'user_id','outlet_code');
    }
}
