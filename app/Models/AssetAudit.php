<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetAudit extends Model
{
    use HasFactory;
    protected $table = 'assets_audit';
    protected $fillable = [
        'user_id',
        'type',
        'request_number',
        'outlet_id',
        'asset_id',
        'asset_number',
        'is_chiller_available',
        'is_barcode_available',
        'barcode_no_scan_reason',
        'scanned_barcode',
        'barcode_match_status',
        'customer_address',
        'contact_number',
        'retailer_name',
        'pincode',
        'current_location',
        'latitude',
        'longitude',
        'distance',
        'audit_status',
        'remarks',
        'outlet_code','ae_code','rsm_code','asm_code','so_code','db_code','dsr_code','cfa_code'
    ];

    public function outlet()
    {
        return $this->belongsTo(RetailerOutlet::class, 'outlet_id');
    }
    public function asset()
    {
        return $this->belongsTo(OutletAssetDetailsModel::class, 'asset_id');
    }
}
