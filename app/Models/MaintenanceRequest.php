<?php

namespace App\Models;

use App\Models\AssetTaskPlacement\AssetPlacementTaskDeploymentModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintenanceRequest extends Model
{
    use HasFactory;
    protected  $table='asset_maintenance_requests';
    protected $fillable = [
        'user_id',
        'maintenance_request_number',
        'asset_type_code',
        'type',
        'outlet_id',
        'asset_number',
        'asset_barcode',
        'current_vpo',
        'chiller_type',
        'customer_code',
        'customer_name',
        'contact_person',
        'contact_number',
        'consent_status',
        'signature',
        'email',
        'mobile_number',
        'customer_address',
        'pincode',
        'maintenance_reason',
        'customer_location',
        'current_location',
        'correct_location',
        'distance',
        'latitude',
        'longitude',
        'remarks',
        'status',
        'status_remarks',
        'task_status',
        'repair_status',
        'asset_assigned_status',
        'maintenance_date',
        'approved_by_user_role',
        'approved_by_user_id',
        'pending_from',
        'rejected_by_user_id',
        'rejected_by_user_role',
        'maintenance_approved_time',
        'maintenance_rejected_time',
        'expected_maintenance_date',
        'assigned_organization',
        'maintenance_complete_date',
        'request_by',
        'request_by_name',
        'approved_time',
        'rejection_reason',
        'outlet_code',
        'ae_code',
        'rsm_code',
        'asm_code',
        'so_code',
        'db_code',
        'dsr_code',
        'cfa_code',
        'batchID',
        'uploadBy',
        'uploadByName',
        'uploadTime',
        'chiller_image',
        'outlet_photo',
        'retailer_photo'
    ];

    public function outlet()
    {
        return $this->belongsTo(RetailerOutlet::class, 'outlet_id')->select('id','user_id','salesman_code','salesman_name','region','channel_code','class_name','category_name','route_name','invoiceaddress');
    }
    public function retailer()
    {
        return $this->belongsTo(User::class, 'outlet_code', 'user_id')->select('address_1','pin_code','region','user_id','name','city','mobile_number','state');
    }
    public function rsm()
    {
        return $this->belongsTo(User::class, 'rsm_code', 'user_id')->select('user_id', 'name','region','region_code');
    }
    public function asm()
    {
        return $this->belongsTo(User::class, 'asm_code', 'user_id')->select('user_id', 'name','channel_type','area_code');
    }
    public function so()
    {
        return $this->belongsTo(User::class, 'so_code', 'user_id')->select('user_id', 'name','teritory_code');
    }
    public function distributor()
    {
        return $this->belongsTo(User::class, 'db_code', 'user_id')->select('user_id', 'name','cfa_code');
    }
    public function dsr()
    {
        return $this->belongsTo(User::class, 'dsr_code', 'user_id')->select('user_id', 'name');
    }
    public function cfa()
    {
        return $this->belongsTo(User::class, 'cfa_code', 'user_id')->select('user_id', 'name','gst_no','address_1');
    }

    public function approvalHistory()
    {
        return $this->hasMany(AssetApprovalPlacementRequestHistory::class, 'asset_placement_request_id', 'maintenance_request_number');
    }
    public function placementAssets()
    {
        return $this->hasOne(OutletAssetDetailsModel::class, 'placement_request_number', 'maintenance_request_number');
    }
    public function taskDeploy()
    {
        return $this->hasOne(AssetPlacementTaskDeploymentModel::class, 'request_number', 'maintenance_request_number');
    }
    public function maintenancetaskDeploy()
    {
        return $this->hasOne(AssetMaintenanceTaskRepairModel::class, 'request_number', 'maintenance_request_number');
    }
    public function inventory()
    {
        return $this->belongsTo(AsssetInventory::class, 'cfa_code', 'warehouse_code');
    }

    public function inventoryOne()
    {
        return $this->hasOne(AsssetInventory::class, 'serial_number', 'asset_serial_number');
    }    public function userOne()
{
    return $this->hasOne(User::class,'user_id','outlet_code');
}
    public function retaileroutlets(){
        return $this->hasOne(RetailerOutlet::class,"id","outlet_id");
    }
}
