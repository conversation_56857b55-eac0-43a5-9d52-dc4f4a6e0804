<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;
use Illuminate\Support\Facades\Hash;
class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $table = "users";
    protected $primaryKey = 'id';
    protected $fillable = [
        'name',
        'email',
        'device_token'.'device_type',
        'password',
        'user_id',
        'cfa_code',
        'gst_no',
        'country',
        'dataUploadByOrganisation',
        'profile','mobile_number','user_type','first_name','last_name','personal_email','area_code','channel_type',
        'mobile_number','phone','address_1','address_2','address_3','city','state','pin_code','region','status','teritory_code',
        'rbdm_name','rbdm_email','rbdm_mobile_number','rtmm_code',
        'retailer_id','merchandiser_id','dsr_id','isr_id','distributor_id','so_id','asm_id','rsm_id','ae_id',
        'vendor_id','vendor_executive_id','region_manager_id','batchID','uploadBy','uploadByName','uploadTime'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];


    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    #handle for custome field to validate check
    public function attempt(array $credentials = [])
    {
        $user = $this->where('user_id', $credentials['UserName'])->first();

        if ($user && Hash::check($credentials['Password'], $user->password)) {
            return $user;
        }

        return null;
    }


    public function asm()
    {
//        return $this->belongsTo(User::class, 'asm_id');
        return $this->belongsTo(User::class, 'asm_id', 'user_id');
    }

    public function rsm()
    {
        return $this->belongsTo(User::class, 'rsm_id','user_id');
    }

    public function distributor()
    {
        return $this->belongsTo(User::class, 'distributor_id','user_id');
    }
    public function cfa()
    {
        return $this->belongsTo(User::class, 'cfa_code', 'user_id');
    }

    public function so()
    {
        return $this->belongsTo(User::class, 'so_id','user_id');
    }

    public function dsr()
    {
        return $this->belongsTo(User::class, 'dsr_id','user_id');
    }

    public function outlets()
    {
        return $this->hasMany(RetailerOutlet::class, 'user_id', 'id');
    }
    public function outletHasOne()
    {
        return $this->hasOne(RetailerOutlet::class, 'user_id', 'id');
    }
    public function mappedUsers()
    {
        return $this->hasMany(User::class, 'dsr_id'); // Assuming DSR is mapped with other users through the 'dsr_id' foreign key
    }

    public function retailers(){
        return $this->hasMany(User::class, 'distributor_id','user_id')->where('user_type', 'RETAILER');
    }
    public function retailerOutlet()
    {
        return $this->hasMany(RetailerOutlet::class, 'user_id');
    }

    public function userRetailer()
    {
        return $this->belongsTo(RetailerOutlet::class, 'id', 'user_id');
    }
    public function assets()
    {
        return $this->hasMany(OutletAssetDetailsModel::class, 'outlet_code', 'user_id');
    }


    public static function getUserTableColumnAttribute($userRole = 'SO')
    {
        $columnAttribute = [
            'DISTRIBUTOR' => ['SO Teritory Code','CHANNEL','CFA Code','Distributor Code','Distributor Name','City','Email Address','Mobile Number'],
            'DSR' => ['SO Teritory Code','DSR Code','DSR Name','Email Address','Mobile Number'],
            'SO' => ['ASM Area Code','ASM Name','SO Teritory Code','SO User Code','SO Name','Email Address','Mobile Number'],
            'ASM' => ['RSM Region Code','ASM Area Code','ASM Code','ASM Name','Email Address','Mobile Number'],
            'RSM' => ['Region Name','RSM/RBDM Code','RSM/RBDM Name','Email Address','Mobile Number'],
            'VENDOR' => ['CFA/Plant Code Code','Name','Address 1','Address 2','City','State','Country','Pin Code','Effective From','Effective To','GST No','Email Address','Mobile Number'],
            'VE' => ['Region','Name','user code','CFA Code','Address 1','Address 2','City','State','Country','Pin Code','Email Address','Mobile Number'],
            'RETAILER' => ['Region', 'Retailer Name', 'Retailer Code', 'AE Code', 'RSM Code', 'ASM Code', 'Area Code', 'SO Code', 'Teritory Code', 'DB Code', 'DSR Code', 'City', 'State', 'Pincode', 'Route Code', 'Route Name', 'Category Code', 'Category Name', 'SubChannelCode', 'SubChannelName', 'Channel Code', 'ClassName', 'Pan Number', 'GST Number', 'Address1', 'Address2', 'Address3', 'Invoiceaddress', 'Is TCS', 'Lat', 'Long', 'Contact Name', 'Aadhar Number', 'Credit Period', 'Credit Limit', 'Location Code', 'Return Qty', 'Return Value', 'License Number', 'License Exp. Date', 'Fssai Number', 'Fssai Exp. Date', 'Retailer Reference Code', 'Retailer Created_on', 'Modified Date', 'Retailer Modified On', 'Status', 'Email Address', 'Mobile Number']
         ];
        return $columnAttribute[$userRole] ?? [];
    }

    public static function getDBColumnsByUserType($userType)
    {
        $columnMappings = [
            'DISTRIBUTOR' => ['so.teritory_code','channel_type','cfa_code','user_id','name','city','email','mobile_number'],
            'DSR' => ['so.teritory_code','user_id','name','email','mobile_number'],
            'SO' => ['asm.area_code','asm.name','teritory_code','user_id','name','email','mobile_number'],
            'ASM' => ['rsm.region','area_code','user_id','name','email','mobile_number'],
            'RSM' => ['region','user_id','name','email','mobile_number'],
            'VENDOR' => ['user_id','name','address_1','address_2','city','state','country','pin_code','effective_from','effective_to','gst_no','email','mobile_number'],
            'VE' => ['region','name','user_id','cfa_code','address_1','address_2','city','state','country','pin_code','email','mobile_number'],
            'RETAILER' => [
                'region', 'name', 'user_id', 'ae_id', 'rsm_id', 'asm_id', 'asm.area_code', 'so_id', 'so.teritory_code', 'distributor_id',
                'userRetailer.salesman_code', 'city', 'state', 'pin_code', 'userRetailer.route_code', 'userRetailer.route_name',
                'userRetailer.category_code', 'userRetailer.category_name', 'userRetailer.sub_channel_code', 'userRetailer.sub_channel_name',
                'userRetailer.channel_code', 'userRetailer.class_name', 'userRetailer.pan_no', 'gst_no', 'userRetailer.address_1',
                'userRetailer.address_2', 'userRetailer.address_3', 'userRetailer.invoiceaddress', 'userRetailer.is_tcs', 'userRetailer.lat',
                'userRetailer.long', 'userRetailer.contact_name', 'userRetailer.aadhar', 'userRetailer.credit_period', 'userRetailer.credit_limit',
                'userRetailer.location_code', 'userRetailer.return_qty', 'userRetailer.return_value', 'userRetailer.drug_license_no',
                'userRetailer.drug_license_exp_date', 'userRetailer.fssai_no', 'userRetailer.fssai_exp_date', 'userRetailer.retailer_reference_code',
                'userRetailer.created_at', 'userRetailer.modified_date', 'userRetailer.updated_at', 'userRetailer.active', 'email', 'mobile_number'
            ],
            // Add mappings for other user types
        ];
        return $columnMappings[$userType] ?? [];
    }
}

