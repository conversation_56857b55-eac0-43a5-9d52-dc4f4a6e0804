<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OutletAssetDetailsModel extends Model
{
    use HasFactory;
    protected $table = 'assets_outlet_details';
    protected $fillable = [
        'asset_description',
        'asset_barcode',
        'outlet_code',
        'outlet_name',
        'placement_request_number',
        'date_of_placement',
        'asset_assigned_by',
        'asset_assigned_time',
        'last_audit_time',
        'asset_number'
    ];


    public function placement()
    {
        return $this->belongsTo(AssetPlacementRequest::class, 'placement_request_number', 'request_number');
    }
    public function inventory()
    {
        return $this->hasOne(AsssetInventory::class, 'serial_number', 'asset_number');
    }
}
