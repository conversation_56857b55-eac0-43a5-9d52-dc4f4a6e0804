<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetReplacementRequest extends Model
{
    use HasFactory;
    protected $table = 'asset_replacement_requests';
    protected $fillable = [
        'user_id',
        'is_bolt_town',
        'request_number',
        'asset_type_code',
        'request_chiller_code',
        'placement_request_number',
        'type',
        'outlet_id',
        'asset_barcode',
        'asset_number',
        'chiller_type',
        'current_vpo',
        'expected_vpo',
        'request_chiller_type',
        'request_type',
        'additional_equipment',
        'chiller_location',
        'chiller_location_photo',
        'consent_status',
        'retailer_photo',
        'signature',
        'address_proof',
        'mobile_number',
        'customer_address',
        'pincode',
        'replacement_reason',
        'customer_location',
        'current_location',
        'distance',
        'remarks',
        'latitude',
        'longitude',
        'correct_location',
        'task_status',
        'chiller_placed',
        'is_quantity_allocated',
        'asset_assigned_status',
        'replacement_date',
        'approved_by_user_role',
        'approved_by_user_id',
        'pending_from',
        'rejected_by_user_id',
        'rejected_by_user_role',
        'replacement_approved_time',
        'replacement_rejected_time',
        'deployment_date',
        'expected_deployment_date',
        'assigned_organization',
        'replacement_complete_date',
        'request_by',
        'request_by_name',
        'approved_time',
        'rejection_reason',
        'is_deploy',
        'outlet_code',
        'ae_code',
        'rsm_code',
        'rttm_code',
        'asm_code',
        'so_code',
        'db_code',
        'dsr_code',
        'cfa_code',
        'batchID',
        'uploadBy',
        'uploadByName',
        'uploadTime',
        'upload_by_org',
    ];
    public function outlet()
    {
        return $this->belongsTo(RetailerOutlet::class, 'outlet_id')->select('id', 'user_id', 'salesman_code', 'salesman_name', 'region', 'channel_code', 'class_name', 'category_name', 'route_name', 'invoiceaddress');
    }
    public function retailer()
    {
        return $this->belongsTo(User::class, 'outlet_code', 'user_id')->select('address_1', 'pin_code', 'region', 'user_id', 'name', 'city', 'mobile_number', 'state');
    }
    public function rsm()
    {
        return $this->belongsTo(User::class, 'rsm_code', 'user_id')->select('user_id', 'name', 'region', 'region_code');
    }
    public function asm()
    {
        return $this->belongsTo(User::class, 'asm_code', 'user_id')->select('user_id', 'name', 'channel_type', 'area_code');
    }
    public function so()
    {
        return $this->belongsTo(User::class, 'so_code', 'user_id')->select('user_id', 'name', 'teritory_code');
    }
    public function distributor()
    {
        return $this->belongsTo(User::class, 'db_code', 'user_id')->select('user_id', 'name', 'cfa_code');
    }
    public function dsr()
    {
        return $this->belongsTo(User::class, 'dsr_code', 'user_id')->select('user_id', 'name');
    }
    public function approvalHistory()
    {
        return $this->hasMany(AssetApprovalPlacementRequestHistory::class, 'asset_placement_request_id', 'request_number');
    }
    public function inventory()
    {
        return $this->belongsTo(AsssetInventory::class, 'cfa_code', 'warehouse_code');
    }

    public function inventoryOne()
    {
        return $this->hasOne(AsssetInventory::class, 'serial_number', 'asset_number');
    }
    public function userOne()
    {
        return $this->hasOne(User::class, 'user_id', 'outlet_code');
    }
    public function retaileroutlets()
    {
        return $this->hasOne(RetailerOutlet::class, "id", "outlet_id");
    }
    public function placement(){
        return $this->hasOne(AssetPlacementRequest::class,'asset_serial_number','asset_number');
    }
    public function replacementTaskDeploy()
    {
        return $this->hasOne(AssetTaskRePlacement::class, 'request_number', 'request_number');
    }
    public function placementAssets()
    {
        return $this->hasOne(OutletAssetDetailsModel::class, 'placement_request_number', 'request_number');
    }
    public function cfa()
    {
        return $this->belongsTo(User::class, 'cfa_code', 'user_id')->select('user_id', 'name','gst_no','address_1');
    }

}
