<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetErrorLog extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'asset_error_log';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'batch_id',
        'request_ip',
        'error_trace',
        'error_code',
        'file',
        'line',
        'context',
        'failed_type',
        'error_message',
        'uploadedBy',
        'uploadedTime',
        'error_data',
        'userType',
        'uploadByName'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'uploadedTime' => 'datetime',
        'error_data' => 'array',
    ];
}
