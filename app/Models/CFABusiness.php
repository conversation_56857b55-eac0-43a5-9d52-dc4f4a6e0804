<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CFABusiness extends Model
{
    use HasFactory;
    protected $table = 'cfa_businesses';
    protected $fillable = [
        'cfa_plant_code',
        'business_name',
        'registered_name',
        'register_number',
        'address_1',
        'address_2',
        'city',
        'state',
        'country',
        'pin_code',
        'effective_from',
        'effective_to',
        'gst_no',
        'user_type'
    ];

    // protected $dates = ['effective_from', 'effective_to'];
}
