<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetMaintenanceTaskRepairModel extends Model
{
    use HasFactory;

    protected $table = 'asset_maintenance_task_repair';

    protected $fillable = [
        'user_id',
        'request_number',
        'maintenance_asset',
        'reason_no_maintenance',
        'remarks',
        'outlet_photo',
        'retailer_photo',
        'asset_number',
        'asset_barcode',
        'scanned_barcode',
        'chiller_image',
        'distance',
        'latitude',
        'longitude',
        'current_location',
        'ae_code','rsm_code','asm_code','so_code','db_code','dsr_code','cfa_code',
        'batchID','uploadBy','uploadByName','uploadTime','completion_date','upload_by_org','correct_location','outlet_id'
    ];
    protected $attributes = [
        'correct_location' => 'No', // default is false, which means 'No'
    ];
}
