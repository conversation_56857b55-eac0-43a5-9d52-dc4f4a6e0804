<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApiLog extends Model
{
    use HasFactory;
    protected $fillable = [
        'action', 'url', 'method', 'request_data', 'response_data', 'status_code', 'client_ip','latitude','longitude','logged_at'
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
    ];

}
