<?php

namespace App\Models\AssetTaskPlacement;

use App\Models\AssetPlacementRequest;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetPlacementTaskDeploymentModel extends Model
{
    use HasFactory;
    protected $table = 'asset_placement_task_deployment';
    protected $fillable = [
        'user_id',
        'outlet_id',
        'outlet_code',
        'request_number',
        'chiller_placed',
        'reason_no_deployment',
        'remarks',
        'outlet_photo',
        'retailer_photo',
        'asset_number',
        'asset_barcode',
        'chiller_image',
        'installation_receipt',
        'correct_location',
        'distance',
        'latitude',
        'longitude',
        'current_location',
        'completion_date',
        'ae_code',
        'rsm_code','asm_code','so_code','db_code','dsr_code','cfa_code'
    ];

    public function apr(){
        return  $this->hasOne(AssetPlacementRequest::class,'request_number','request_number');
    }
}
