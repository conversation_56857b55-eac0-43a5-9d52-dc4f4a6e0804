<?php

namespace App\Models\AssetTaskPullout;

use App\Models\AssetPlacementRequest;
use App\Models\AssetPulloutRequest;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetPulloutTaskDeploymentModel extends Model
{
    use HasFactory;
    protected $table = 'asset_pullout_task_deployment';
    protected $fillable = [
        'user_id',
        'pullout_request_number',
        'pullout_asset',
        'reason_no_pullout',
        'remarks',
        'outlet_photo',
        'retailer_photo',
        'asset_number',
        'scanned_bar_code',
        'chiller_image',
        'correct_location',
        'distance',
        'latitude',
        'longitude',
        'current_location',
        'ae_code',
        'rsm_code', 'asm_code', 'so_code', 'db_code', 'dsr_code', 'cfa_code',
        'batchID', 'uploadBy', 'uploadByName', 'uploadTime'
    ];

    public function pullout()
    {
        return  $this->hasOne(AssetPulloutRequest::class, 'pullout_request_number', 'pullout_request_number');
    }
    public function outletUser()
    {
        return $this->hasOne(User::class, 'user_id', 'outlet_code');
    }
}
