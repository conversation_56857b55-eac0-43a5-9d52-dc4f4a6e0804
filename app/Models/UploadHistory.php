<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UploadHistory extends Model
{
    use HasFactory;
    protected $fillable = [
        'batchID',
        'uploadBy',
        'uploadByName',
        'requestIP',
        'userType',
        'uploadDateTime',
        'uploadType',
        'totalRecordscount',
        'successCount',
        'failedCount',
        'failedMessage',
        'failedData',
    ];

    protected $casts = [
        'uploadDateTime' => 'datetime',
        'failedData' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class,'user_id', 'uploadBy');
    }

    public function assetLog()
    {
        return $this->hasMany(AssetErrorLog::class, 'batch_id', 'batchID');
    }
}
