<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RetailerOutlet extends Model
{
    use HasFactory;
    protected $table = 'retaileroutlets';
    protected $fillable = [
        'user_id',
        'region',
        'salesman_code',
        'salesman_name',
        'route_code',
        'route_name',
        'category_code',
        'category_name',
        'sub_channel_code',
        'sub_channel_name',
        'channel_code',
        'channel_name',
        'class_name',
        'pan_no',
        'is_tcs',
        'invoiceaddress',
        'town',
        'lat',
        'long',
        'contact_name',
        'aadhar',
        'credit_limit',
        'credit_period',
        'location_code',
        'location_name',
        'return_qty',
        'return_value',
        'drug_license_no',
        'drug_license_exp_date',
        'fssai_no',
        'fssai_exp_date',
        'retailer_created_on',
        'modified_date',
        'retailer_reference_code',
        'active',
        'db_name',
        'outlet_image','asset_assigned_status'
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function dsr()
    {
        return $this->hasOne(User::class, 'user_id', 'salesman_code');
    }
    public function asm()
    {
        return $this->hasOne(User::class, 'user_id', 'asm_id');
    }
    public function rsm()
    {
        return $this->hasOne(User::class, 'user_id', 'rsm_id');
    }
    public function so()
    {
        return $this->hasOne(User::class, 'user_id', 'so_id');
    }
    public function distributor()
    {
        return $this->hasOne(User::class, 'user_id', 'distributor_id');
    }
    public function cfa()
    {
        return $this->hasOne(User::class, 'user_id', 'cfa_code');
    }
    public function retailer()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function assets()
    {
        return $this->hasMany(OutletAssetDetailsModel::class, 'outlet_code', 'id');
    }
}
