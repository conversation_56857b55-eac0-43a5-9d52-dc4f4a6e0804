<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceChallan extends Model
{
    use HasFactory;
    protected $table = 'invoice_challans';
    protected $fillable = [
        'invoice_challan_number',
        'request_number',
        'invoice_prefix',
        'challan_created_datetime',
    ];
    // Prefix constants for different asset activities
    const PREFIX_ASSET_REPLACEMENT_REQUEST = 'ARR';
    const PREFIX_ASSET_MAINTENANCE_REQUEST = 'AMR';
    const PREFIX_ASSET_ATOB_TRANSFER_REQUEST = 'ATR';
    const PREFIX_ASSET_PLACEMENT_REQUEST = 'APR';
    const PREFIX_ASSET_PULLOUT_REQUEST = 'PR';

    // Method to get a list of all prefixes
    public static function getPrefixes()
    {
        return [
            self::PREFIX_ASSET_REPLACEMENT_REQUEST,
            self::PREFIX_ASSET_MAINTENANCE_REQUEST,
            self::PREFIX_ASSET_ATOB_TRANSFER_REQUEST,
            self::PREFIX_ASSET_PLACEMENT_REQUEST,
            self::PREFIX_ASSET_PULLOUT_REQUEST,
        ];
    }
}
