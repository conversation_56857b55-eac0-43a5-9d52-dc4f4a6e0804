<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AsssetInventory extends Model
{
    use HasFactory;

    protected $table = 'assets_inventory';

    protected $fillable = [
        'description',
        'barcode',
        'asset_type',
        'asset_type_code',
        'model_name',
        'vendor',
        'quantity',
        'warehouse_code',
        'manufactured_year',
        'serial_number',
        'assigned_status',
        'asset_price', 'remarks',
        'barcode', 'asset_approval_status', 'approved_by_user', 'approved_by_user_type', 'approval_time', 'vpo_target'
    ];
    public function commonCategory()
    {
        return $this->belongsTo(AssetCommonCategory::class, 'asset_type', 'name');
    }
    public function assetPlacementRequest()
    {
        return $this->hasOne(AssetPlacementRequest::class, 'asset_serial_number', 'serial_number');
    }
    public function cfa()
    {
        return $this->hasOne(User::class, 'user_id', 'warehouse_code');
    }
}
