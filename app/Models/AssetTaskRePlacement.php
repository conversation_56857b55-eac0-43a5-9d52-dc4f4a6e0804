<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetTaskRePlacement extends Model
{
    use HasFactory;
    protected $table = 'asset_replacement_task_deployment';
    protected $fillable = [
        'user_id',
        'request_number',
        'outlet_id',
        'chiller_placed',
        'placement_request_number',
        'reason_no_deployment',
        'prev_assigned_asset_number',
        'remarks',
        'outlet_photo',
        'retailer_photo',
        'replaced_asset_number',
        'replaced_asset_barcode',
        'chiller_image',
        'installation_receipt',
        'correct_location',
        'distance',
        'latitude',
        'longitude',
        'current_location',
        'completion_date',
        'outlet_code',
        'ae_code',
        'rsm_code',
        'rttm_code',
        'asm_code',
        'so_code',
        'db_code',
        'dsr_code',
        'cfa_code',
        'batchID',
        'uploadBy',
        'uploadByName',
        'uploadTime',
    ];
    public function apr()
    {
        return  $this->hasOne(AssetReplacementRequest::class, 'request_number', 'request_number');
    }
}
