<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\AssetTaskPlacement\AssetPlacementTaskDeploymentModel;

class AssetPlacementRequest extends Model
{
    use HasFactory;
    protected $table = 'asset_placement_requests';
    protected $fillable = [
        'user_id',
        'outlet_id',
        'type',
        'is_bolt_town',
        'expected_vpo',
        'eligible_chiller_type',
        'request_type',
        'additional_equipment',
        'competitor_chiller_size',
        'competitor_company',
        'competitor_chiller_photo',
        'chiller_location',
        'chiller_location_photo',
        'address_proof',
        'retailer_photo',
        'signature',
        'mobile_number',
        'customer_address',
        'pincode',
        'customer_location',
        'current_location',
        'latitude',
        'longitude',
        'correct_location',
        'distance',
        'remarks',
        'consent_status',
        'request_number',
        'asset_assigned_status',
        'approved_time',
        'is_deploy',
        'asset_type_code',
        'batchID',
        'assigned_organization','asset_mapping_type',
        'expected_deployment_date','chiller_placed','task_status','asset_barcode','asset_serial_number',
        'deployment_date', 'rejection_reason','challan_number',
        'vpo_target', 'approved_by_user_role', 'approved_by_user_id', 'pending_from', 'rejected_by_user_id', 'placement_approved_time', 'placement_rejected_time',
        'is_quantity_allocated', 'rejected_by_user_role','outlet_code','ae_code','rsm_code','asm_code','so_code','db_code','dsr_code','cfa_code','pullout_request_number',
        'pullout_complete_date_time','pullout_request_by','pullout_request_by_name'
    ];
    public function outlet()
    {
        return $this->belongsTo(RetailerOutlet::class, 'outlet_id')->select('id','user_id','salesman_code','salesman_name','region','channel_code','class_name','category_name','route_name','invoiceaddress');
    }
    public function retailer()
    {
        return $this->belongsTo(User::class, 'outlet_code', 'user_id')->select('address_1','pin_code','region','user_id','name','city','mobile_number','state');
    }
    public function rsm()
    {
        return $this->belongsTo(User::class, 'rsm_code', 'user_id')->select('user_id', 'name');
    }
    public function asm()
    {
        return $this->belongsTo(User::class, 'asm_code', 'user_id')->select('user_id', 'name','channel_type','area_code');
    }
    public function so()
    {
        return $this->belongsTo(User::class, 'so_code', 'user_id')->select('user_id', 'name','teritory_code');
    }
    public function distributor()
    {
        return $this->belongsTo(User::class, 'db_code', 'user_id')->select('user_id', 'name','cfa_code');
    }
    public function dsr()
    {
        return $this->belongsTo(User::class, 'dsr_code', 'user_id')->select('user_id', 'name');
    }
    public function cfa()
    {
        return $this->belongsTo(User::class, 'cfa_code', 'user_id')->select('user_id', 'name','gst_no','address_1');
    }

    public function approvalHistory()
    {
        return $this->hasMany(AssetApprovalPlacementRequestHistory::class, 'asset_placement_request_id', 'request_number');
    }
    public function placementAssets()
    {
        return $this->hasOne(OutletAssetDetailsModel::class, 'placement_request_number', 'request_number');
    }
    public function taskDeploy()
    {
        return $this->hasOne(AssetPlacementTaskDeploymentModel::class, 'request_number', 'request_number');
    }
    public function inventory()
    {
        return $this->belongsTo(AsssetInventory::class, 'cfa_code', 'warehouse_code');
    }

    public function inventoryOne()
    {
        return $this->hasOne(AsssetInventory::class, 'serial_number', 'asset_serial_number');
    }    public function userOne()
    {
        return $this->hasOne(User::class,'user_id','outlet_code');
    }
    public function retaileroutlets(){
        return $this->hasOne(RetailerOutlet::class,"id","outlet_id");
    }

    // public function replacementTaskDeploy()
    // {
    //     return $this->hasOne(AssetTaskRePlacement::class, 'request_number', 'request_number');
    // }
}
