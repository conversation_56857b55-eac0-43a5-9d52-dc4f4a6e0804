<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class MailSentNotificationCommand extends Command
{
    protected $signature = 'mailNotification_copu:send';
    protected $description = 'Send a test email';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        Mail::raw('This is a test email', function ($message) {
            $message->to('<EMAIL>');
            $message->subject('Test Email');
        });

        $this->info('Test email sent successfully!');
    }
}
