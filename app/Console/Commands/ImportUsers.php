<?php

namespace App\Console\Commands;

use App\Services\UserImportHierarchyService;
use Illuminate\Console\Command;

class ImportUsers extends Command
{
    protected $signature = 'import:users {file}';

    protected $description = 'Import users from the specified file';

    protected $userImportService;

    public function __construct(UserImportHierarchyService $userImportService)
    {
        parent::__construct();

        $this->userImportService = $userImportService;
    }

    public function handle()
    {
        $file = $this->argument('file');

        if (!file_exists($file)) {
            $this->error('File not found: ' . $file);
            return;
        }

        $this->userImportService->importUsers($file);

        $this->info('Users imported successfully!');
    }
}
