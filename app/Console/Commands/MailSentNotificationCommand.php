<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class MailSentNotificationCommand extends Command
{
    protected $signature = 'mailNotification:send';
    protected $description = 'Send a test email';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // Define custom email credentials
        $customConfig = [
            'driver' => 'smtp',
            'host' => 'email-smtp.ap-south-1.amazonaws.com',
            'port' => 587,
            'encryption' => 'tls',
            'username' => 'AKIAU7PBKP7NS3ELEBWD',
            'password' => 'BE7NH16PNqThr+14U6nHgYIOTuDMOBaFgFStKFdawk/G',
        ];

        // Temporarily set the email configuration to use custom credentials
        config([
            'mail' => $customConfig,
        ]);

        // Send an email using the custom credentials


        Mail::raw('This is a test email', function ($message) {
            $message->from('<EMAIL>', 'Sender Name'); // Specify the "From" address and name
            $message->to('<EMAIL>');
            $message->subject('Test Email');
        });

        $this->info('Test email sent successfully!');

        // Restore the original email configuration (optional)
        config([
            'mail' => [
                'driver' => env('MAIL_DRIVER', 'smtp'), // You can use your default mail configuration here.
                // ...
            ],
        ]);
    }
}
