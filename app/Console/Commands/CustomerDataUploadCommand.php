<?php

namespace App\Console\Commands;

use App\Imports\UserMasterImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Services\UserImportService;
use Maatwebsite\Excel\Facades\Excel;
class CustomerDataUploadCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customer:upload:data';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload files';

    /**
     * The UserImportService instance.
     *
     * @var \App\Services\UserImportService
     */
    protected $userImportService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\UserImportService  $userImportService
     * @return void
     */
    public function __construct(UserImportService $userImportService)
    {
        parent::__construct();

        $this->userImportService = $userImportService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filePaths = [
            // public_path('excelsheet/AssetMappingBlankReqNo.xlsx'),
            // public_path('excelsheet/AssetMappingWithReqNo.xlsx'),
            public_path('excelsheet/AssetMappingBlankReqNo.xlsx'),
            // public_path('excelsheet/asset_mapping.xlsx'),
        ];

        foreach ($filePaths as $filePath) {
            if (!file_exists($filePath)) {
                $this->error("File not found at the provided path: $filePath");
                continue;
            }

            try {
                chmod($filePath, 0666);
                $userRole = 'asset_retailer_mapping';
                Excel::import(new UserMasterImport($userRole), $filePath);
                // $this->userImportService->importUsers($filePath, $userRole);
                $this->info("File imported successfully: $filePath");
            } catch (\Throwable $th) {
                Log::error('Failed to import users: ' . $th->getMessage());
                $this->error("Failed to import users from file: $filePath. Error: {$th->getMessage()}");
            }
        }
    }
}
