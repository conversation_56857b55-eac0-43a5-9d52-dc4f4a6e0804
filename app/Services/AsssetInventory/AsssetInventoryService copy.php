<?php

namespace App\Services\AsssetInventory;

use App\Library\Utils\DateFormate;
use App\Models\AsssetInventory;
use Illuminate\Support\Facades\Log;

class AsssetInventoryService
{
    #this is common method for inventory , so before changes , check carefully everything working
    public static function getInventopryList($user, $limit, $assetType, $currentPage, $approval_status, $from = '', $to = '', $withPlacement = false)
    {
        try {
            return AsssetInventory::where(['assigned_status' => 'no'])
                ->when($user->user_type == 'VENDOR', function ($query) use ($user) {
                    return $query->where('warehouse_code', $user->user_id);
                })
                ->when(!empty($assetType) && $assetType != 'all', function ($query) use ($assetType) {
                    return $query->where('asset_type', $assetType);
                })
                ->when(!empty($approval_status) && $approval_status != 'all', function ($query) use ($approval_status) {
                    return $query->where('asset_approval_status', $approval_status);
                })
                ->when(!empty($from) && !empty($to), function ($query) use ($from, $to) {
                    return $query->whereDatebetween('created_at', [$from, $to]);
                })
                ->when($withPlacement, function ($query) {
                    return $query->with(['assetPlacementRequest' => function ($query) {
                        $query->select('id', 'asset_serial_number', 'request_number');
                    }]);
                })
                ->latest()
                ->paginate($limit, ['*'], 'page', $currentPage);
        } catch (\Exception $exception) {
            Log::error('Error : ChallanPdfService generateChallanPDF at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            // throw $exception;
            return null;
        }
    }


    public static function getWorkingInventopryList($user, $limit, $assetType, $currentPage, $approval_status, $from = '', $to = '', $withPlacement = false)
    {
        try {
            return AsssetInventory::where(['assigned_status' => 'yes'])
                ->when($user->user_type == 'VENDOR', function ($query) use ($user) {
                    return $query->where('warehouse_code', $user->user_id);
                })
                ->when(!empty($assetType) && $assetType != 'all', function ($query) use ($assetType) {
                    return $query->where('asset_type', $assetType);
                })
                ->when(!empty($approval_status) && $approval_status != 'all', function ($query) use ($approval_status) {
                    return $query->where('asset_approval_status', $approval_status);
                })
                ->when(!empty($from), function ($query) use ($from) {
                    return  $query->whereDate('created_at', '>=', $from);
                })
                ->when(!empty($to), function ($query) use ($to) {
                    return  $query->whereDate('created_at', '<=', $to);
                })

                ->when($withPlacement, function ($query) {
                    return $query->with(['assetPlacementRequest' => function ($query) {
                        $query->select('id', 'asset_serial_number', 'request_number');
                    }]);
                })
                ->with(['cfa' => function ($query) {
                    $query->select('user_id', 'name', 'region');
                }])
                ->latest()
                ->paginate($limit, ['*'], 'page', $currentPage);
        } catch (\Exception $exception) {
            Log::error('Error : ChallanPdfService generateChallanPDF at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            // throw $exception;
            return null;
        }
    }

    public static function convertDateFormatToDbFormat($excelField, $timestamp = '')
    {
        $format = $timestamp == 'time' ? 'Y-m-d H:i:s' : 'Y-m-d';
        return DateFormate::convertDbFormat($excelField, $format);
    }
}
