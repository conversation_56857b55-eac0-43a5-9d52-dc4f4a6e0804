<?php

namespace App\Services\AsssetInventory;

use App\Library\Utils\DateFormate;
use App\Models\AsssetInventory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class AsssetInventoryService
{
    #this is common method for inventory , so before changes , check carefully everything working
    private static function buildInventoryQuery($user, $assetType, $approval_status, $from, $to, $withPlacement, $assignedStatus)

    {
        try {
            $request = request()->all();
            $warehouse_code = $request['warehouse_code']??'';
            $keyword = $request['keyword']??'';
            $query = AsssetInventory::where(['assigned_status' => $assignedStatus])
                ->when($user->user_type == 'VENDOR', function ($query) use ($user) {
                    return $query->where('warehouse_code', $user->user_id);
                })
                ->when($user->user_type != 'VENDOR'  && !empty($warehouse_code) && $warehouse_code !='all', function ($query) use ($warehouse_code) {
                    return $query->where('warehouse_code', $warehouse_code);
                })
                ->when(!empty($assetType) && $assetType != 'all', function ($query) use ($assetType) {
                    return $query->where('asset_type', $assetType);
                })
                ->when(!empty($approval_status) && $approval_status != 'all', function ($query) use ($approval_status) {
                    return $query->where('asset_approval_status', $approval_status);
                })
                ->when(!empty($from), function ($query) use ($from) {
                    return  $query->whereDate('created_at', '>=', $from);
                })
                ->when(!empty($to), function ($query) use ($to) {
                    return  $query->whereDate('created_at', '<=', $to);
                })
                ->when($withPlacement, function ($query) {
                    return $query->with(['assetPlacementRequest' => function ($query) {
                        $query->select('id', 'asset_serial_number', 'request_number');
                    }]);
                })
                ->when(!empty($request['keyword']), function ($query) use ($request) {
                    $keyword = $request['keyword'];
                    return $query->where(function ($query) use ($keyword) {
                        $query->where('serial_number', 'like', "%{$keyword}%")
                              ->orWhere('barcode', 'like', "%{$keyword}%")
                              ->orWhere('manufactured_year', 'like', "%{$keyword}%");
                    });
                });
            return $query;
        } catch (\Exception $exception) {
            Log::error('Error : ChallanPdfService generateChallanPDF at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            // throw $exception;
            return null;
        }
    }


    public static function getInventoryList($user, $limit, $assetType, $currentPage, $approval_status, $from = '', $to = '', $withPlacement = false)
    {
        try {
            $query = self::buildInventoryQuery($user, $assetType, $approval_status, $from, $to, $withPlacement, 'no');
            return $query->paginate($limit, ['*'], 'page', $currentPage);
        } catch (\Exception $exception) {
            Log::error('Error : AsssetInventoryService getInventoryList at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return null;
        }
    }


    public static function getWorkingInventoryList($user, $limit, $assetType, $currentPage, $approval_status, $from = '', $to = '', $withPlacement = false)
    {
        try {
            $query = self::buildInventoryQuery($user, $assetType, $approval_status, $from, $to, $withPlacement, 'no');
            $query = $query->with(['cfa' => function ($query) {
                $query->select('user_id', 'name', 'region');
            }]);
            return $query->paginate($limit, ['*'], 'page', $currentPage);
        } catch (\Exception $exception) {
            Log::error('Error : AsssetInventoryService getWorkingInventoryList at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return null;
        }
    }

    #Get Inward / OutWard inventory details / summary
    public static  function getInwardOutwardInventory($request, $download = '')
    {
        $assetType = $request->input('asset_type');
        $keyword = $request->input('keyword');
        $inward_outward_details = $request->input('inward_outward_details');
        $inward_outward = $request->input('inward_outward') ? strtolower($request->input('inward_outward')) : '';
        $cfaCode = $request->input('cfa_code');
        $startDate = $request->input('from_date') ? DateFormate::convertDateFormatToDbFormat($request->input('from_date')) : '';
        $endDate = $request->input('to_date') ? DateFormate::convertDateFormatToDbFormat($request->input('to_date')) : '';
        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);
        $user = Auth::user();

        // Base query with filters
        $query = AsssetInventory::with('cfa')->when($user->user_type == 'VENDOR', function ($query) use ($user) {
            return $query->where('warehouse_code', $user->user_id);
        })
            ->when(!empty($startDate), function ($query) use ($startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            })
            ->when(!empty($endDate), function ($query) use ($endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            })
            ->when(!empty($inward_outward) && $inward_outward != 'all', function ($query) use ($inward_outward) {
                $query->where('assigned_status', strtolower($inward_outward));
            })
            ->when(!empty($keyword), function ($query) use ($keyword) {
                $query->where(function ($query) use ($keyword) {
                    $query->where('asset_type', 'like', '%' . $keyword . '%')
                        ->orWhere('warehouse_code', 'like', '%' . $keyword . '%')
                        ->orWhere('description', 'like', '%' . $keyword . '%')
                        ->orWhere('model_name', 'like', '%' . $keyword . '%')
                        ->orWhereHas('cfa', function ($query) use ($keyword) {
                            $query->where('name', 'like', '%' . $keyword . '%'); // Adjust 'name' to match the actual column name in the 'cfa' table
                        });
                });
            });
        if ($inward_outward_details == 'details') {
            $query->selectRaw('
                    warehouse_code,
                    description,
                    asset_type,
                    model_name,
                    vendor,
                    barcode,
                    serial_number,
                    quantity as opening_inventory,
                    CASE WHEN assigned_status = "no" THEN quantity ELSE 0 END as stock_in,
                    CASE WHEN assigned_status = "yes" THEN quantity ELSE 0 END as stock_out,
                    quantity + CASE WHEN assigned_status = "no" THEN quantity ELSE 0 END - CASE WHEN assigned_status = "yes" THEN quantity ELSE 0 END as closing_inventory
                ');
        } else {
            $query->selectRaw('
                    warehouse_code,
                    description,
                    asset_type,
                    model_name,
                    vendor,
                    SUM(quantity) as opening_inventory,
                    SUM(CASE WHEN assigned_status = "no" THEN quantity ELSE 0 END) as stock_in,
                    SUM(CASE WHEN assigned_status = "yes" THEN quantity ELSE 0 END) as stock_out,
                    SUM(quantity) + SUM(CASE WHEN assigned_status = "no" THEN quantity ELSE 0 END) - SUM(CASE WHEN assigned_status = "yes" THEN quantity ELSE 0 END) as closing_inventory
                ')
                ->groupBy('warehouse_code', 'asset_type', 'model_name', 'vendor', 'description');
        }

        $query->orderBy('warehouse_code', 'DESC');

        // Paginate the results
        if (!empty($download) && $download = 'download') {
            $inwardOutwardInventory = $query->get();
        } else {
            $inwardOutwardInventory = $query->paginate($limit, ['*'], 'page', $currentPage);
        }


        // Calculate grand totals
        $grandTotals = [
            'opening_inventory' => $inwardOutwardInventory->sum('opening_inventory'),
            'stock_in' => $inwardOutwardInventory->sum('stock_in'),
            'stock_out' => $inwardOutwardInventory->sum('stock_out'),
            'closing_inventory' => $inwardOutwardInventory->sum('closing_inventory')
        ];

        return [
            'inwardOutwardInventory' => $inwardOutwardInventory,
            'grandTotals' => $grandTotals
        ];
    }
    public static function convertDateFormatToDbFormat($excelField, $timestamp = '')
    {
        $format = $timestamp == 'time' ? 'Y-m-d H:i:s' : 'Y-m-d';
        return DateFormate::convertDbFormat($excelField, $format);
    }

    #get Asset Type List
    public static function getAssetType($user)
    {
        return AsssetInventory::select('asset_type', 'vpo_target')
            ->where('asset_approval_status', 'approved')
            // ->when($user->user_type == 'VENDOR', function ($query) use ($user) {
            //     return $query->where('warehouse_code', $user->user_id);
            // })
            ->groupBy('asset_type', 'vpo_target')
            ->get()
            ->toArray();
    }
}
