<?php

namespace App\Services;

use App\Models\RetailerOutlet;
use Spatie\Permission\Models\Role;
use App\Models\User;
use App\Models\CFABusiness;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class UserService
{
    public function getUsers(Request $request)
    {
        $auth = Auth::user();
        $roles = Role::whereIn('name', ['RSM', 'ASM', 'SO', 'DSR', 'DISTRIBUTOR', 'VENDOR', 'VE', 'RETAILER'])->get();

        //used for getting filter data  and pagination data
        $user_type = $request->query('user_type', 'SO');
        $limit = $request->query('limit', 10);
        $currentPage = $request->query('page', 1);
        $fromDate = $this->convertDateFormate($request->query('from_date'));
        $toDate = $this->convertDateFormate($request->query('to_date'));
        $keyword = $request->query('keyword');

        // get data basis of user type relationship

        $query = User::where('user_type', $user_type);
        if ($fromDate && $toDate) {
            $query->whereDate('created_at', '>=', $fromDate);
        }
        if ($fromDate && $toDate) {
            $query->whereDate('created_at', '<=', $toDate);
        }
        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->where('name', 'like', '%' . $keyword . '%')
                    ->orWhere('user_id', 'like', '%' . $keyword . '%');
            });
        }

        if ($user_type === 'SO') {
            $query->with(['asm' => function ($query) {
                $query->select('area_code', 'name', 'user_id');
            }]);
        } elseif ($user_type === 'ASM') {
            $query->with(['rsm' => function ($query) {
                $query->select('region', 'area_code', 'user_id', 'name');
            }]);
        } elseif ($user_type === 'DSR') {
            $query->with(['dsr' => function ($query) {
                $query->select('teritory_code', 'user_id', 'name');
            }]);
        } elseif ($user_type === 'DISTRIBUTOR') {
            $query->with(['so' => function ($query) {
                $query->select('teritory_code', 'user_id', 'name');
            }]);
        } elseif ($user_type === 'RETAILER') {
            $query->with('userRetailer');
        }


        $users = $query->latest()->paginate($limit, ['*'], 'page', $currentPage);
        $headerColumns = User::getUserTableColumnAttribute($user_type);
        $dbColumns = User::getDBColumnsByUserType($user_type);

        return [
            'users' => $users,
            'roles' => $roles,
            'user_type' => $user_type,
            'headerColumns' => $headerColumns,
            'dbColumns' => $dbColumns,
            'auth' => $auth,
        ];
    }

    private function convertDateFormate($date)
    {
        if ($date) {
            try {
                return Carbon::createFromFormat('F j, Y', $date)->format('Y-m-d');
            } catch (\Exception $e) {
                Log::error("Error converting date: " . $e->getMessage());
            }
        }
        return null;
    }
}
