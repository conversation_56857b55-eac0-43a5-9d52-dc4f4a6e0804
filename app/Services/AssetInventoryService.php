<?php

namespace App\Services;

use App\Models\User; // Import the User model if needed
use App\Services\PlacementRequestService\PlacementRequestService; // Import the PlacementRequestService if needed

class AssetInventoryService
{
    public static function placementAssetTypeCode($asset_type)
    {
        $validAsset = self::getAssetDetails($asset_type);

        if (empty($validAsset)) {
            throw new \Exception('Validation failed: Invalid asset code.');
        }
        return $validAsset;
    }

    public static function validateAsset($asset)
    {
        $validAsset = self::getAssetDetails($asset['asset_type']);

        if (empty($validAsset)) {
            throw new \Exception('Validation failed: Invalid asset code.');
        }

        if ($asset['asset_price'] != $validAsset['price'] || $asset['vpo_target'] != $validAsset['vpo']) {
            throw new \Exception('Price or VPO does not match the expected value.');
        }
        return $validAsset;
    }

    public static function getAssetDetails($assetCode)
    {
        $validAssets = self::getAssetList();
        // Return the asset details if the asset code exists, otherwise return an empty array
        return $validAssets[$assetCode] ?? [];
    }

    public static function getAssetList()
    {
        $validAssets = [
            'GEN2CB001' => [
                'asset_type' => 'Gen 2',
                'asset_code' => 'GEN2CB001',
                'price' => 8000,
                'vpo' => 8000,
            ],
            'NEW30CT30CB002' => [
                'asset_type' => 'New 30L - CT 30',
                'asset_code' => 'NEW30CT30CB002',
                'price' => 9300,
                'vpo' => 1800,
            ],
            'NEW60VT30CB003' => [
                'asset_type' => 'New 60L - VT30',
                'asset_code' => 'NEW60VT30CB003',
                'price' => 9500,
                'vpo' => 1800,
            ],
            'SRC280CB004' => [
                'asset_type' => 'SRC 280',
                'asset_code' => 'SRC280CB004',
                'price' => 21000,
                'vpo' => 5400,
            ],
            'SRC380CB005' => [
                'asset_type' => 'SRC 380',
                'asset_code' => 'SRC380CB005',
                'price' => 32000,
                'vpo' => 8000,
            ],
            'SRC60CB006' => [
                'asset_type' => 'SRC 60',
                'asset_code' => 'SRC60CB006',
                'price' => 11000,
                'vpo' => 2300,
            ],
            'SRC650CB007' => [
                'asset_type' => 'SRC 650',
                'asset_code' => 'SRC650CB007',
                'price' => 60000,
                'vpo' => 15000,
            ],
            'SRC30CB008' => [
                'asset_type' => 'SRC 30',
                'asset_code' => 'SRC30CB008',
                'price' => 9300,
                'vpo' => 1800,
            ],
            'SRC950CB009' => [
                'asset_type' => 'SRC 950',
                'asset_code' => 'SRC950CB009',
                'price' => 72000,
                'vpo' => 25000,
            ],
        ];
        return $validAssets;
    }
}
