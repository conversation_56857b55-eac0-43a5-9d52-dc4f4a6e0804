<?php

namespace App\Services;

use App\Models\User; // Import the User model
use App\Services\PlacementRequestService\PlacementRequestService;

class HeirarchyService
{
    const MISSING_HIERARCHY_CODES = [
        'user_id' => 'User Code',
        'ae_id' => 'AE Code',
        'rsm_id' => 'RSM Code',
        'asm_id' => 'ASM Code',
        'so_id' => 'SO Code',
        'distributor_id' => 'Distributor Code',
        'outlet_code' => 'Outlet Code',
        'db_code' => 'DB Code',
        'cfa_code' => 'CFA Code'
    ];
    public static function checkHeirarchyUserExist($codesToCheck)
    {
        $codesToCheck = array_filter($codesToCheck, function ($code) {
            return !is_null($code);
        });
        // Extract values to check
        $codesToCheckValues = array_values($codesToCheck);
        // Query the database for existing user IDs
        $existingUserIDs = User::whereIn('user_id', $codesToCheckValues)->pluck('user_id')->toArray();
        $missingCodes = [];
        foreach ($codesToCheck as $key => $value) {
            if (!in_array($value, $existingUserIDs)) {
                $missingCodes[$key] = $value;
            }
        }
        // return $missingCodes;
        return array_keys($missingCodes);
    }

    public static function validateHeirarchy($userData, $userRole)
    {
        $dbHierarchyList = self::getDbHierarchyList();
        $codesToCheck = [];

        if (array_key_exists($userRole, $dbHierarchyList)) {
            foreach ($dbHierarchyList[$userRole] as $roleKey) {
                // $codesToCheck[] = $userData[$roleKey] ?? '';
                $codesToCheck[$roleKey] = $userData[$roleKey] ?? '';
            }
        }

        $missingHeirarchyCodes = self::checkHeirarchyUserExist($codesToCheck);

        if (!empty($missingHeirarchyCodes)) {
            $missingDescriptions = array_intersect_key(self::MISSING_HIERARCHY_CODES, array_flip($missingHeirarchyCodes));
            $missingDescriptionsString = implode(', ', $missingDescriptions);

            throw new \Exception('Validation failed: Hierarchy User not Exist - ' . $missingDescriptionsString);
        }
    }
    private static function getHierarchyList()
    {
        return [
            'AE' => ['ae_code'],
            'RSM' => ['ae_code', 'rsm_code'],
            'ASM' => ['ae_code', 'rsm_code', 'asm_code'],
            'SO' => ['ae_code', 'rsm_code', 'asm_code', 'so_code'],
            'DISTRIBUTOR' => ['ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code'],
            'DSR' => ['ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'dsr_code'],
            'RETAILER' => ['ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'retailer_code', 'salesman_code'],
        ];
    }
    public  static function getDbHierarchyList()
    {
        return [
            'AE' => ['user_id'],
            'RSM' => ['ae_id'],
            'ASM' => ['ae_id', 'rsm_id'],
            'SO' => ['ae_id', 'rsm_id', 'asm_id'],
            'DISTRIBUTOR' => ['ae_id', 'rsm_id', 'asm_id', 'so_id'],
            'DSR' => ['ae_id', 'rsm_id', 'asm_id', 'so_id', 'distributor_id'],
            'RETAILER' => ['ae_id', 'rsm_id', 'asm_id', 'so_id', 'distributor_id'],
            'VE' => ['cfa_code'],
            'retailer_lat_long' => ['user_id'],
            'db_to_cfa_mapping' => ['distributor_id','cfa_code'],
            'retailer_gst_update' => ['user_id'],
            'asset_retailer_mapping' => ['outlet_code','ae_code', 'rsm_code', 'asm_code', 'so_code', 'db_code','cfa_code'],
        ];
    }


    public static function gelRetailterHeirarchyList($outletID)
    {
        $heirarchy = PlacementRequestService::outletHeirarchy($outletID);
        $heirarchyUser = $heirarchy->user ?? '';
        $distributor = $heirarchy->user->distributor ?? '';

        return [
            'outlet_code' => $heirarchyUser->user_id ?? '',
            'ae_code' => $heirarchyUser->ae_id ?? '',
            'rsm_code' => $heirarchyUser->rsm_id ?? '',
            'asm_code' => $heirarchyUser->asm_id ?? '',
            'so_code' => $heirarchyUser->so_id ?? '',
            'db_code' => $heirarchyUser->distributor_id ?? '',
            'dsr_code' => $heirarchy->salesman_code ?? '',
            'cfa_code' => $distributor->cfa_code ?? '',
            'scanned_bar_code' => $assetBarcode ?? '',
        ];
    }
}
