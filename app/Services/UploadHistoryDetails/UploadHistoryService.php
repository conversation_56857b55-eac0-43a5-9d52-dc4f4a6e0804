<?php

namespace App\Services\UploadHistoryDetails;

use App\Constants\PlacementApprovalMessage;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetErrorLog;
use App\Models\AssetPlacementRequest;
use App\Models\RetailerOutlet;
use App\Models\UploadHistory;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class UploadHistoryService
{

    public static function getLatestHistorybyBatchID($batchId, $userID = '')
    {
        // Optimize query to avoid memory issues with large tables
        $query = UploadHistory::query();

        if (!empty($batchId)) {
            // If we have a specific batchID, search for it directly
            $query->where('batchID', $batchId);
        } else {
            // If no batchID, get latest by user with limit to avoid memory issues
            $query->where('uploadBy', $userID)
                  ->orderBy('id', 'desc') // Use id instead of created_at for better performance
                  ->limit(10); // Limit to recent records to avoid memory issues
        }

        // Only load assetLog relationship if needed and limit the query scope
        return $query->with(['assetLog' => function ($q) {
                $q->limit(100); // Limit related records to avoid memory issues
            }])
            ->first();
    }
    public static function getIVYLatestHistorybyBatchID($batchId, $userID = '')
    {
        return UploadHistory::when(!empty($batchId), function ($query) use ($batchId) {
            $query->where('batchID', $batchId);
        })->with('assetLog', function ($query) {
            $query->select('batch_id', 'failed_type', 'error_message', 'uploadedBy', 'uploadedTime','userType as dataUploadType', 'error_data');
        })
            ->latest()
            ->first();
    }

    public static function getAssetLogDetailsByBatchID($batchId)
    {
        return AssetErrorLog::where('batch_id', $batchId)->get();
    }

    private static function getHierarchyList()
    {
        return [
            'AE' => ['ae_code'],
            'RSM' => ['ae_code', 'rsm_code'],
            'ASM' => ['ae_code', 'rsm_code', 'asm_code'],
            'SO' => ['ae_code', 'rsm_code', 'asm_code', 'so_code'],
            'DISTRIBUTOR' => ['ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code'],
            'DSR' => ['ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'dsr_code'],
            'RETAILER' => ['ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'retailer_code', 'salesman_code'],
        ];
    }

    public static function ManageUploadHistory($batchID, $totalRecords, $successRecords, $failedRecords, $failedData, $user_id, $name, $ip, $userType, $uploadType)
    {
        $uploadHistory = [
            'batchID' => $batchID,
            'totalRecordscount' => $totalRecords,
            'successCount' => ($totalRecords - $failedRecords),
            'failedCount' => $failedRecords,
            'failedData' => $failedData, //before add this data will be in json
            'updated_at' => now(),
            'uploadBy' => $user_id ?? '<EMAIL>',
            'uploadByName' => $name ?? '',
            'requestIP' => $ip ?? '',
            'uploadType' => $uploadType ?? '',
            'userType' => Auth::user()->user_type ?? $userType,
            'uploadDateTime' => now(),
        ];
        Log::info("StoredIvyAssetRetailerMaster uploadHistory", [$uploadHistory]);
        return  UploadHistory::updateOrCreate(['batchID' => $batchID], $uploadHistory);
    }
}
