<?php

namespace App\Services\PlacementRequestService;

use App\Constants\PlacementApprovalMessage;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\InvoiceChallan;
use App\Models\RetailerOutlet;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class PlacementRequestService
{

    public static function placementRequestList($request, $user, $model = 'placement')
    {
        $user_type = $user->user_type;
        $so_name = $request->input('so_teritory');
        $asm_name = $request->input('asm_teritory');
        $db_code = $request->input('db_code');
        $distributor_name = $request->input('distributor_name');
        $customer_name = $request->input('customer_name');
        $customer_city = $request->input('customer_city');
        $customer_code = $request->input('customer_code');
        $route_name = $request->input('route_name');
        $request_no = $request->input('request_no');
        $status = $request->input('status');
        $request_status = $request->input('request_status');
        $fromDate = $request->input('FromDate');
        $toDate = $request->input('ToDate');
        $SerialNumber = $request->input('SerialNumber');
        //        $modelClass = self::getModel($model);
        $modelClassData = self::getModelWithRequestNumber($model);
        $modelClass = $modelClassData['model'];
        $chiller_placed = $request->input('chiller_placed');
        $query = $modelClass::with(['outlet', 'retailer', 'approvalHistory', 'distributor.cfa', 'so', 'asm', 'rsm', 'dsr'])
            ->when($user->user_type != 'VENDOR' && in_array($user->user_type, ['ASM', 'RSM', 'SO', 'AE']), function ($query) use ($user) {
                $key = $user->user_type == 'ASM' ? 'asm_code' : ($user->user_type == 'RSM' ? 'rsm_code' : ($user->user_type == 'SO' ? 'so_code' : ($user->user_type == 'AE' ? 'ae_code' : '')));
                return $query->where($key, $user->user_id);
            })
            ->when($user->user_type == 'VENDOR', function ($query) use ($user) {
                return $query->where(function ($query) use ($user) {
                    $query->where('cfa_code', $user->user_id)
                        ->whereIn('pending_from', ['VENDOR', 'VE'])
                        ->orWhere(function ($query) use ($user) {
                            $query->where('rejected_by_user_role', $user->user_type);
                        });
                });
            })
            ->when($SerialNumber, function ($query) use ($SerialNumber) {
                return $query->where('asset_serial_number', $SerialNumber);
            })
            ->when($chiller_placed, function ($query) use ($chiller_placed) {
                return $query->where('chiller_placed', ucfirst($chiller_placed));
            })
            // ->when($chiller_placed, function ($query) use ($chiller_placed) {
            //     return $query->where('is_quantity_allocated', $chiller_placed);
            // })
            ->when($fromDate, function ($query) use ($fromDate) {
                return $query->whereDate('created_at', '>=', Carbon::parse($fromDate));
            })
            ->when($toDate, function ($query) use ($toDate) {
                return $query->whereDate('created_at', '<=', Carbon::parse($toDate));
            })
            ->when($request_status, function ($query) use ($request_status) {
                return $query->where('is_quantity_allocated', $request_status);
            })
            ->when($so_name, function ($query) use ($so_name) {
                return $query->whereHas('so', function ($query) use ($so_name) {
                    $query->where('teritory_code', $so_name);
                });
            })
            ->when($asm_name, function ($query) use ($asm_name) {
                return $query->whereHas('asm', function ($query) use ($asm_name) {
                    $query->where('area_code', $asm_name);
                });
            })
            ->when($db_code || $distributor_name, function ($query) use ($db_code, $distributor_name) {
                return $query->whereHas('distributor', function ($query) use ($db_code, $distributor_name) {
                    $query->where(function ($query) use ($db_code, $distributor_name) {
                        if ($db_code) {
                            $query->where('user_id', $db_code);
                        }
                        if ($distributor_name) {
                            $query->where('name', $distributor_name);
                        }
                    });
                });
            })
            ->when($customer_name || $customer_city || $customer_code, function ($query) use ($customer_name, $customer_city, $customer_code) {
                return $query->whereHas('outlet.retailer', function ($query) use ($customer_name, $customer_city, $customer_code) {
                    $query->where(function ($query) use ($customer_city, $customer_name, $customer_code) {
                        if ($customer_city) {
                            $query->where('city', $customer_city);
                        }
                        if ($customer_name) {
                            $query->where('name', $customer_name);
                        }
                        if ($customer_code) {
                            $query->where('user_id', $customer_code);
                        }
                    });
                });
            })
            ->when($request_no, function ($query) use ($request_no, $modelClassData) {
                //                $request_number = $model == 'placement' ? 'request_number' : 'pullout_request_number';
                $request_number = $modelClassData['request_number'];
                return $query->where($request_number, $request_no);
            })
            ->when($route_name, function ($query) use ($route_name) {
                return $query->whereHas('outlet', function ($query) use ($route_name) {
                    $query->where('route_name', $route_name);
                });
            });
        $validUserTypes = ['ASM', 'RSM', 'SO', 'AE', 'VENDOR'];

        if (in_array($user_type, $validUserTypes)) {
            $query->orderByRaw("CASE
                    WHEN pending_from = '$user_type' THEN 0
                    ELSE 1
                END");
        }

        if ($status && $status !== 'All') {
            if ($status == "Pending") {
                $query->where('pending_from', $user->user_type);
            } elseif ($status == "Rejected") {
                $query->where('rejected_by_user_id', $user->user_id);
            } elseif ($status == "taskallocation") {
                $query->where('cfa_code', $user->user_id)
                    ->where('assigned_organization', '!=', '');
            } else {
                // $query->where('asset_assigned_status', $status);
                $query->where('pending_from', '!=', $user->user_type);
            }
        }
        return $query;
    }

    public static function getModelWithRequestNumber($modelType)
    {
        $models = [
            'placement' => [
                'model' => 'App\Models\AssetPlacementRequest',
                'request_number' => 'request_number',
                'asset_type' => 'eligible_chiller_type',
                'prefix' => InvoiceChallan::PREFIX_ASSET_PLACEMENT_REQUEST
            ],
            'pullout' => [
                'model' => 'App\Models\AssetPulloutRequest',
                'request_number' => 'pullout_request_number',
                'asset_type' => 'chiller_type',
                'prefix' => InvoiceChallan::PREFIX_ASSET_PULLOUT_REQUEST
            ],
            'maintenance' => [
                'model' => 'App\Models\MaintenanceRequest',
                'request_number' => 'maintenance_request_number',
                'asset_type' => 'chiller_type',
                'prefix' => InvoiceChallan::PREFIX_ASSET_MAINTENANCE_REQUEST
            ],
            'replacement' => [
                'model' => 'App\Models\AssetReplacementRequest',
                'request_number' => 'request_number',
                'asset_type' => 'chiller_type',
                'prefix' => InvoiceChallan::PREFIX_ASSET_REPLACEMENT_REQUEST
            ]
        ];

        return $models[$modelType] ?? $models['placement'];
    }
    public static function getModel($modelType)
    {
        $models = [
            'placement' => 'App\Models\AssetPlacementRequest',
            'pullout' => 'App\Models\AssetPulloutRequest',
            'maintenance' => 'App\Models\MaintenanceRequest',
            'replacement' => 'App\Models\AssetReplacementRequest',
        ];

        return $models[$modelType] ?? null;
    }

    public static function placementRequestTableData($assetPlacementList, $model = 'placement')
    {
        $TaskStatusService = app(TaskStatusService::class);
        $assetPlacementListView = [];
        foreach ($assetPlacementList as $asset) {
            // Extracting relationships
            $outlet = $asset->outlet;
            $retailer = $asset->retailer;
            $approval_history = $asset->approvalHistory;

            // Filtering approval history
            $asm_approval = $approval_history->firstWhere('user_role', 'ASM');
            $rsm_approval = $approval_history->firstWhere('user_role', 'RSM');

            $distributor = $asset->distributor;
            $cfa = $asset->cfa ?? '';
            $so = $asset->so ?? '';
            $asm = $asset->asm ?? '';
            $rsm = $asset->rsm ?? '';
            $dsr = $asset->dsr ?? '';
            $modelClass = self::getModel($model);
            // Get task and deployment statuses
            $taskStatus = $TaskStatusService->getTaskStatus($asset);
            $deploymentStatus = $TaskStatusService->deploymentStatus($asset, $model);
            if ($model == 'pullout') {
                $data = self::PulloutRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus);
            } elseif ($model == 'maintenance') {
                $data = self::MaintenanceRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus);
            } elseif ($model == 'replacement') {
                $data = self::ReplacementRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus);
            } else {
                $data = self::PlacementRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus);
            }
            $objectData = (object)$data;
            $assetPlacementListView[] = $objectData;
            // Perform further operations as needed
        }
        return $assetPlacementListView;
    }


    public static function PlacementFilter($user, $model = 'placement')
    {
        //        $modelClass = self::getModel($model);
        $modelClassData = self::getModelWithRequestNumber($model);
        $modelClass = $modelClassData['model'];
        $loginUserColumn = $user->user_type == 'ASM' ? 'asm_code' : ($user->user_type == 'RSM' ? 'rsm_code' : ($user->user_type == 'SO' ? 'so_code' : ($user->user_type == 'AE' ? 'ae_code' : ($user->user_type == 'VENDOR' ? 'cfa_code' : ''))));
        $city_list = self::getUserCodeList('RETAILER', 'city');
        $so_territory_list = self::getUserCodeList('SO', 'teritory_code');
        $asm_territory_list = self::getUserCodeList('ASM', 'area_code');
        $db_code_list = in_array($user->user_type, ['SUPERADMIN', 'ADMIN']) ? [] : self::getUserCodeList('DISTRIBUTOR', 'user_id');
        $db_name_list = in_array($user->user_type, ['SUPERADMIN', 'ADMIN']) ? [] : self::getUserCodeList('DISTRIBUTOR', 'name');
        // $vendorExecutiveList =  User::where(["user_type" => "VE", "cfa_code" => $user->user_id])->pluck('user_id')->unique();
        $requestNumberKey = $modelClassData['request_number'];
        $placement_request_list = $modelClass::when(!in_array($user->user_type, ['SUPERADMIN', 'ADMIN']), function ($query) use ($loginUserColumn, $user) {
            return $query->where($loginUserColumn, $user->user_id);
        })
            ->when($user->user_type == 'VENDOR', function ($query) {
                return $query->whereIn('pending_from', ['VENDOR', 'VE']);
            })
            ->latest()->pluck($requestNumberKey)->unique();




        // $placement_task_list =  AssetPlacementRequest::where('task_status', 'Completed')
        //     ->when($user->user_type != 'SUPERADMIN', function ($query) use ($loginUserColumn, $user) {
        //         return $query->where($loginUserColumn, $user->user_id);
        //     })->pluck('is_quantity_allocated')->unique();

        #filter value
        // return compact('city_list', 'so_territory_list', 'asm_territory_list', 'db_code_list', 'city_list', 'db_name_list', 'loginUserColumn', 'placement_request_list', 'placement_task_list', 'vendorExecutiveList');
        return compact('city_list', 'so_territory_list', 'asm_territory_list', 'db_code_list', 'city_list', 'db_name_list', 'loginUserColumn', 'placement_request_list');
    }


    public static function getUserCodeList($type = null, $column)
    {
        $user = Auth::user();
        $userType = $user->user_type;

        if ($userType == 'VENDOR' && !empty($column)) {
            return self::getVendorUserCodeList($user, $type, $column);
        }

        $key = ($userType == 'ASM') ? 'asm_id' : (($userType == 'RSM') ? 'rsm_id' : (($userType == 'SO') ? 'so_id' : (($userType == 'AE') ? 'ae_id' : '')));

        if ($type && $userType != 'VENDOR' && !in_array($userType, ['SUPERADMIN', 'ADMIN'])) {
            return self::getUserTypeCodeList($type, $key, $user, $column);
        }
        return User::whereNotNull($column)
            ->where($column, '<>', '')
            ->distinct()
            ->pluck($column)
            ->values()
            ->toArray();
    }

    private static function getVendorUserCodeList($user, $type, $column)
    {
        if ($type == 'RETAILER') {
            return User::where('user_type', 'DISTRIBUTOR')
                ->where('cfa_code', $user->user_id)
                ->whereNotNull($column)
                ->where($column, '<>', '')
                ->pluck($column)
                ->unique()
                ->values()
                ->toArray();
        }

        $type = strtolower($type);

        $usersData = User::where('user_type', 'DISTRIBUTOR')
            ->where('cfa_code', $user->user_id)
            ->with([$type => function ($query) use ($column) {
                $query->select('user_id', $column);
            }])
            ->get();

        $relationship = $type;

        $teritoryData = $usersData->map(function ($user) use ($relationship, $column) {
            return $user->$relationship->$column ?? null;
        })->filter()->unique()->values()->toArray();
        return $teritoryData;
    }

    private static function getUserTypeCodeList($type, $key, $user, $column)
    {

        // $userData = User::where('user_type', $type)
        // ->where($key, $user->user_id)
        //     ->whereNotNull($column)
        //     ->where($column, '<>', '')
        //     ->distinct($column)
        //     ->pluck($column)
        //     ->toArray();
        // return $userData;

        return User::where('user_type', $type)
            ->where($key, $user->user_id)
            ->whereNotNull($column)
            ->where($column, '<>', '')
            ->distinct($column)
            ->pluck($column)
            ->toArray();
    }


    public static function outletHeirarchy($OutletId)
    {
        $heirarchy = RetailerOutlet::where('id', $OutletId)
            ->with(['user' => function ($query) {
                $query->select('id', 'user_id', 'ae_id', 'rsm_id', 'asm_id', 'so_id', 'distributor_id');
            }])
            ->first();
        return  $heirarchy;
    }

    public static function generateRequestNumber($prefix = '')
    {
        $timestampComponent = substr(date('YmdHis'), -8);
        $startingPrefix = !empty($prefix) ? $prefix : 'APR';
        $newRequestNumber = $startingPrefix . $timestampComponent;
        return $newRequestNumber;
    }
    public static function PulloutRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus)
    {
        $data = [
            'id' => $asset->id ?? '',
            'region' => $rsm->region ?? '',
            'request_number' => $asset->pullout_request_number ?? '',
            'serial_number' => $asset->asset_serial_number ?? '',
            'barcode' => $asset->asset_barcode ?? '',
            'reason' => $asset->pullout_reason ?? '',
            'created_at' => date('Y-m-d H:i:s', strtotime($asset->created_at)) ?? '',
            'updated_at' => date('Y-m-d H:i:s', strtotime($asset->updated_at)) ?? '',
            'taskStatus' => $taskStatus ?? '',
            'eligible_chiller_type' => $asset->pullout_asset_type ?? '',
            'rsm_name' => $rsm->name ?? '',
            'asm_name' => $asm->name ?? '',
            'asm_area_code' => $asm->area_code ?? '',
            'so_name' => $so->name ?? '',
            'teritory_code' => $so->teritory_code ?? '',
            'db_name' => $distributor->name ?? '',
            'db_code' => $asset->db_code ?? '',
            'dsr_name' => $outlet->salesman_name ?? '',
            'dsr_code' => $asset->dsr_code ?? '',
            'cfa_code' => $asset->cfa_code ?? '',
            'deploymentStatus' => $deploymentStatus ?? '',
            'deployment_date' => $asset->deployment_date ?? '',
            'assigned_organization' => $asset->assigned_organization ?? '',
            'retailer_user_id' => $retailer->user_id ?? '',
            'outlet_code' => $asset->outlet_code ?? '',
            'retailer_name' => $retailer->name ?? '',
            'retailer_mobile_number' => $retailer->mobile_number ?? '',
            'pin_code' => $retailer->pin_code ?? '',
            'city' => $retailer->city ?? '',
            'state' => $retailer->state ?? '',
            'route_name' => $outlet->route_name ?? '',
            'current_vpo' => $asset->current_vpo ?? '',
            'address_1' => $retailer->address_1 ?? '',
            'address_2' => $retailer->address_2 ?? '',
            'rsm_approval_time' => $rsm_approval->action_updated_time ?? '',
            'asm_approval_time' => $asm_approval->action_updated_time ?? '',
            'approved_by_user_id' => $asset->approved_by_user_id ?? '',
            'rejected_by_user_id' => $asset->rejected_by_user_id ?? '',
            'asset_assigned_status' => $asset->asset_assigned_status ?? '',
            'assigned_organization' => $asset->assigned_organization ?? '',
            'pending_from' => $asset->pending_from ?? '',
            'task_status' => $asset->task_status ?? '',
            'pullout_reason' => $asset->pullout_reason ?? '',
            'pullout_date_time' => !empty($asset->pullout_complete_date) ? date('Y-m-d H:i:s', strtotime($asset->pullout_complete_date)) : '',
        ];
        return $data;
    }
    public static function MaintenanceRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus)
    {
        $data = [
            'id' => $asset->id ?? '',
            'date' => date('Y-m-d H:i:s', strtotime($asset->created_at)) ?? '',
            'updated_at' => date('Y-m-d H:i:s', strtotime($asset->updated_at)) ?? '',
            'region' => $rsm->region ?? '',
            'asm_name' => $asm->name ?? '',
            'asm_code' => $asset->asm_code ?? '',
            'rsm_name' => $rsm->name ?? '',
            'so_name' => $so->name ?? '',
            'so_code' => $asset->so_code ?? '',
            'db_name' => $distributor->name ?? '',
            'db_code' => $asset->db_code ?? '',
            'dsr_name' => $outlet->salesman_name ?? '',
            'dsr_code' => $asset->dsr_code ?? '',
            'cfa_code' => $asset->cfa_code ?? '',
            'request_number' => $asset->maintenance_request_number ?? '',
            'serial_number' => $asset->asset_number ?? '',
            'barcode' => $asset->asset_barcode ?? '',
            'reason' => $asset->maintenance_reason ?? '',
            'created_at' => date('Y-m-d H:i:s', strtotime($asset->created_at)) ?? '',
            'taskStatus' => $taskStatus ?? '',
            'eligible_chiller_type' => $asset->chiller_type ?? '',
            'pending_from' => $asset->pending_from ?? '',
            'task_status' => $asset->task_status ?? '',
            'expected_maintenance_date' => $asset->expected_maintenance_date ?? '',
            'maintenance_complete_date' => $asset->maintenance_complete_date ?? '',
            'contact_person' => $asset->contact_person ?? '',
            'contact_number' => $asset->contact_number ?? '',
            'outlet_code' => $asset->outlet_code ?? '',
            'retailer_name' => $retailer->name ?? '',
            'retailer_mobile_number' => $retailer->mobile_number ?? '',
            'address_1' => $retailer->address_1 ?? '',
            'address_2' => $retailer->address_2 ?? '',
            'asm_area_code' => $asm->area_code ?? '',
            'teritory_code' => $so->teritory_code ?? '',
            'deploymentStatus' => $deploymentStatus ?? '',
            'deployment_date' => $asset->deployment_date ?? '',
            'assigned_organization' => $asset->assigned_organization ?? '',
            'retailer_user_id' => $retailer->user_id ?? '',
            'pin_code' => $asset->pincode ?? '',
            'city' => $retailer->city ?? '',
            'state' => $retailer->state ?? '',
            'route_name' => $outlet->route_name ?? '',
            'current_vpo' => $asset->current_vpo ?? '',
            'rsm_approval_time' => $rsm_approval->action_updated_time ?? '',
            'asm_approval_time' => $asm_approval->action_updated_time ?? '',
            'approved_by_user_id' => $asset->approved_by_user_id ?? '',
            'rejected_by_user_id' => $asset->rejected_by_user_id ?? '',
            'asset_assigned_status' => $asset->asset_assigned_status ?? '',
            'retailer_photo' => !empty($asset->retailer_photo) && $asset->retailer_photo != "NULL" ? asset($asset->retailer_photo) : '',
            'outlet_photo' => !empty($asset->outlet_photo) && $asset->outlet_photo != "NULL" ? asset($asset->outlet_photo) : '',
            'chiller_image' => !empty($asset->chiller_image) && $asset->chiller_image != "NULL" ? asset($asset->chiller_image) : '',
        ];
        return $data;
    }
    public static function PlacementRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus)
    {
        $data = [
            'id' => $asset->id ?? '',
            'region' => $retailer->region ?? '',
            'address_1' => $retailer->address_1 ?? '',
            'request_number' => $asset->request_number ?? '',
            'asset_serial_number' => $asset->asset_serial_number ?? '',
            'asset_barcode' => $asset->asset_barcode ?? '',
            'created_at' => date('Y-m-d H:i:s', strtotime($asset->created_at)) ?? '',
            'updated_at' => date('Y-m-d H:i:s', strtotime($asset->updated_at)) ?? '',
            'taskStatus' => $taskStatus ?? '',
            'request_type' => $asset->request_type ?? '',
            'eligible_chiller_type' => $asset->eligible_chiller_type ?? '',
            'asset_type_code' => $asset->asset_type_code ?? '',
            'expected_vpo' => $asset->expected_vpo ?? '',
            'retailer_user_id' => $retailer->user_id ?? '',
            'retailer_name' => $retailer->name ?? '',
            'retailer_mobile_number' => $retailer->mobile_number ?? '',
            'mobile_number' => $asset->mobile_number ?? '',
            'pin_code' => $retailer->pin_code ?? '',
            'rsm_name' => $rsm->name ?? '',
            'asm_name' => $asm->name ?? '',
            'asm_area_code' => $asm->area_code ?? '',
            'so_name' => $so->name ?? '',
            'teritory_code' => $so->teritory_code ?? '',
            'db_name' => $distributor->name ?? '',
            'db_code' => $asset->db_code ?? '',
            'dsr_name' => $dsr->name ?? '',
            'dsr_code' => $asset->dsr_code ?? '',
            'cfa_code' => $asset->cfa_code ?? '',
            'assigned_organization' => $asset->assigned_organization ?? '',
            'channel_code' => $outlet->channel_code ?? '',
            'class_name' => $outlet->class_name ?? '',
            'category_name' => $outlet->category_name ?? '',
            'route_name' => $outlet->route_name ?? '',
            'city' => $retailer->city ?? '',
            'invoiceaddress' => $outlet->invoiceaddress ?? '',
            'address_1' => $retailer->address_1 ?? '',
            'customer_address' => $asset->customer_address ?? '',
            'rsm_approval_time' => $rsm_approval->action_updated_time ?? '',
            'asm_approval_time' => $asm_approval->action_updated_time ?? '',
            'deployment_date' => $asset->is_deploy == true ? $asset->deployment_date : '',
            'chiller_placed' => $asset->chiller_placed ?? '',
            'is_deploy' => $asset->is_deploy ?? '',
            'deploymentStatus' => $deploymentStatus ?? '',
            'approved_by_user_id' => $asset->approved_by_user_id ?? '',
            'is_bolt_town' => $asset->is_bolt_town ?? '',
            'rejected_by_user_id' => $asset->rejected_by_user_id ?? '',
            'asset_assigned_status' => $asset->asset_assigned_status ?? '',
            'assigned_organization' => $asset->assigned_organization ?? '',
            'signature' => !empty($asset->signature) && $asset->signature != "NULL" ? $asset->signature : '',
            'competitor_chiller_photo' => !empty($asset->competitor_chiller_photo) && $asset->competitor_chiller_photo != "NULL" ? asset($asset->competitor_chiller_photo) : '',
            'chiller_location_photo' => !empty($asset->chiller_location_photo)  && $asset->chiller_location_photo != "NULL" ? asset($asset->chiller_location_photo) : '',
            'address_proof' =>  !empty($asset->address_proof) && $asset->address_proof != "NULL" ? asset($asset->address_proof) : '',
            'retailer_photo' => !empty($asset->retailer_photo) && $asset->retailer_photo != "NULL" ? asset($asset->retailer_photo) : '',
            'pending_from' => $asset->pending_from ?? '',
            'task_status' => $asset->task_status ?? '',
            'remarks' => $asset->remarks ?? '',
            'is_bolt_town' => $asset->is_bolt_town == 1 ? "Yes" : 'No',
        ];
        return $data;
    }

    public static function ReplacementRequestFieldData($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus)
    {
        $data = array_merge(self::getCommonFields($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus), [
            'request_number' => $asset->request_number ?? '',
            'serial_number' => $asset->asset_number ?? '',
            'barcode' => $asset->asset_barcode ?? '',
            'reason' => $asset->replacement_reason ?? '',
            'eligible_chiller_type' => $asset->chiller_type ?? '',
            'request_chiller_type' => $asset->request_chiller_type ?? '',
            'expected_deployment_date' => $asset->expected_deployment_date ?? '',
            'complete_date' => $asset->replacement_complete_date ?? '',
            'contact_person' => $outlet->contact_name ?? '',
            'contact_number' => $retailer->mobile_number ?? '',
            'expected_vpo' => $asset->expected_vpo ?? '',
            'is_deploy' => $asset->is_deploy ?? '',
            'chiller_placed' => $asset->chiller_placed ?? '',
            'replacement_date' => $asset->replacement_date ?? '',
            'is_bolt_town' => $asset->is_bolt_town == 1 ? "Yes" : "No",
        ]);
        return $data;
    }

    private static function getCommonFields($asset, $outlet, $retailer, $approval_history, $asm_approval, $rsm_approval, $distributor, $cfa, $so, $asm, $rsm, $dsr, $taskStatus, $deploymentStatus)
    {
        return [
            'id' => $asset->id ?? '',
            'remarks' => $asset->remarks ?? '',
            'request_type' => $asset->request_type ?? '',
            'region' => $rsm->region ?? '',
            'asm_name' => $asm->name ?? '',
            'asm_teritory_code' => $asm->area_code ?? '',
            'asm_code' => $asset->asm_code ?? '',
            'outlet_code' => $asset->outlet_code ?? '',
            'rsm_name' => $rsm->name ?? '',
            'so_name' => $so->name ?? '',
            'so_teritory_code' => $so->teritory_code ?? '',
            'so_code' => $asset->so_code ?? '',
            'db_name' => $distributor->name ?? '',
            'db_code' => $asset->db_code ?? '',
            'dsr_name' => $outlet->salesman_name ?? '',
            'dsr_code' => $asset->dsr_code ?? '',
            'cfa_code' => $asset->cfa_code ?? '',
            'created_at' => self::formatDate($asset->created_at),
            'taskStatus' => $taskStatus ?? '',
            'deploymentStatus' => $deploymentStatus ?? '',
            'deployment_date' => $asset->deployment_date ?? '',
            'assigned_organization' => $asset->assigned_organization ?? '',
            'retailer_user_id' => $retailer->user_id ?? '',
            'retailer_name' => $retailer->name ?? '',
            'retailer_mobile_number' => $retailer->mobile_number ?? '',
            'address_1' => $retailer->address_1 ?? '',
            'address_2' => $retailer->address_2 ?? '',
            'pin_code' => $asset->pincode ?? '',
            'city' => $retailer->city ?? '',
            'state' => $retailer->state ?? '',
            'route_name' => $outlet->route_name ?? '',
            'current_vpo' => $asset->current_vpo ?? '',
            'rsm_approval_time' => self::formatDate($rsm_approval->action_updated_time ?? ''),
            'asm_approval_time' => self::formatDate($asm_approval->action_updated_time ?? ''),
            'approved_by_user_id' => $asset->approved_by_user_id ?? '',
            'rejected_by_user_id' => $asset->rejected_by_user_id ?? '',
            'asset_assigned_status' => $asset->asset_assigned_status ?? '',
            'pending_from' => $asset->pending_from ?? '',
            'task_status' => $asset->task_status ?? '',
        ];
    }
    public static function getHierarchy($validatedData)
    {
        $hierarchyList = self::outletHeirarchy($validatedData['outlet_id']);
        $hierarchyUser = $hierarchyList->user ?? '';
        $distributor = $hierarchyList->user->distributor ?? '';

        #Hierarchy User
        $validatedData['outlet_code'] = $hierarchyUser->user_id ?? '';
        $validatedData['ae_code'] = $hierarchyUser->ae_id ?? '';
        $validatedData['rsm_code'] = $hierarchyUser->rsm_id ?? '';
        $validatedData['asm_code'] = $hierarchyUser->asm_id ?? '';
        $validatedData['so_code'] = $hierarchyUser->so_id ?? '';
        $validatedData['db_code'] = $hierarchyUser->distributor_id ?? '';
        $validatedData['dsr_code'] = $hierarchyUser->salesman_code ?? '';
        $validatedData['cfa_code'] = $distributor->cfa_code ?? '';
        return $validatedData;
    }
    private static function formatDate($date)
    {
        return !empty($date) ? date('Y-m-d H:i:s', strtotime($date)) : '';
    }

    public static function getRetailerChillerRequest($outlet_code)
    {
        return AssetPlacementRequest::where([
            'outlet_code' => $outlet_code,
            'task_status' => 'Pending'
        ])
            ->where('asset_assigned_status', '!=', 'Rejected')
            ->count();
    }
}
