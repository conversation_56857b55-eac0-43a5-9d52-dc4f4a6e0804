<?php

namespace App\Services\PlacementRequestService;

use App\Constants\PlacementApprovalMessage;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\AssetPulloutRequest;
use App\Models\AssetReplacementRequest;
use App\Models\MaintenanceRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

class TaskStatusService
{
    public function placementPendingStatus($role)
    {
        return PlacementApprovalMessage::ApprovalPendingStatus[$role] ?? $role;
    }

    public function getTaskStatus(Model $asset): string
    {
        if ($asset instanceof AssetPlacementRequest) {
            return $this->handlePlacementRequest($asset);
        } elseif ($asset instanceof AssetPulloutRequest) {
            return $this->handlePulloutRequest($asset);
        } elseif ($asset instanceof MaintenanceRequest) {
            return $this->handleMaintenanceRequest($asset);
        } elseif ($asset instanceof AssetReplacementRequest) {
            return $this->handleReplacementRequest($asset);
        } else {
            throw new \InvalidArgumentException("Invalid asset type provided.");
        }
    }
    private function handleReplacementRequest(AssetReplacementRequest $asset)
    {
        if (empty($asset->approved_by_user_id) && empty($asset->rejected_by_user_id)) {
            return $this->placementPendingStatus("ASM");
        } elseif ($asset->is_deploy == true && $asset->chiller_placed == "Yes") {
            return 'Task completed';
        } elseif ($asset->chiller_placed == 'No' && $asset->task_status == 'Completed') {
            return 'Task Cancelled';
        } elseif ($asset->asset_assigned_status != 'Rejected' && $asset->pending_from) {
            return $this->placementPendingStatus($asset->pending_from);
        } elseif ($asset->asset_assigned_status == 'Rejected') {
            return 'Rejected by ' . $asset->rejected_by_user_role;
        }
    }

    private function handlePlacementRequest(AssetPlacementRequest $asset)
    {
        if (empty($asset->approved_by_user_id) && empty($asset->rejected_by_user_id)) {
            return $this->placementPendingStatus("ASM");
        } elseif ($asset->is_deploy == true && $asset->chiller_placed == "Yes") {
            return 'Task completed';
        } elseif ($asset->chiller_placed == 'No' && $asset->task_status == 'Completed') {
            return 'Task Cancelled';
        } elseif ($asset->asset_assigned_status != 'Rejected' && $asset->pending_from) {
            return $this->placementPendingStatus($asset->pending_from);
        } elseif ($asset->asset_assigned_status == 'Rejected') {
            return 'Rejected by ' . $asset->rejected_by_user_role;
        }
    }
    private function handlePulloutRequest(AssetPulloutRequest $asset)
    {
        if (empty($asset->approved_by_user_id) && empty($asset->rejected_by_user_id)) {
            return $this->placementPendingStatus("ASM");
        } elseif ($asset->is_pullout == true && $asset->pullout_placed == "Yes") {
            return 'Task completed';
        } elseif ($asset->pullout_placed == 'No' && $asset->task_status == 'Completed') {
            return 'Task Cancelled';
        } elseif ($asset->asset_assigned_status != 'Rejected' && $asset->pending_from) {
            return $this->placementPendingStatus($asset->pending_from);
        } elseif ($asset->asset_assigned_status == 'Rejected') {
            return 'Rejected by ' . $asset->rejected_by_user_role;
        }
    }
    private function handleMaintenanceRequest(MaintenanceRequest $asset)
    {
        if (empty($asset->approved_by_user_id) && empty($asset->rejected_by_user_id)) {
            return $this->placementPendingStatus("ASM");
        } elseif ($asset->is_maintenance == true && $asset->repair_status == 'Yes') {
            return 'Task completed';
        } elseif ($asset->repair_status == 'No' && $asset->task_status == 'Completed') {
            return 'Task Cancelled';
        } elseif ($asset->asset_assigned_status != 'Rejected' && $asset->pending_from) {
            return $this->placementPendingStatus($asset->pending_from);
        } elseif ($asset->asset_assigned_status == 'Rejected') {
            return 'Rejected by ' . $asset->rejected_by_user_role;
        }
    }
    public function getNextHeirarchyRole($role): string
    {
        $nextHeirarchyRole = [
            'ASM' => 'RSM',
            'RSM' => 'AE',
            'AE' => 'VENDOR',
            'VENDOR' => 'VE'
        ];

        return isset($nextHeirarchyRole[$role]) ? $nextHeirarchyRole[$role] : '';
    }
    public function approvalHistoryUpdate($request_number, $status, $guard = '')
    {
        $user = !empty($guard) ? Auth::guard('api')->user() : Auth::user();
        $actionUpdatedTime = now();
        $updateHistory = AssetApprovalPlacementRequestHistory::updateOrCreate(
            [
                'user_id' => $user->user_id,
                'user_role' => $user->user_type,
                'asset_placement_request_id' => $request_number,
            ],
            [
                'action_updated_time' => $actionUpdatedTime,
                'action' => $status,
            ]
        );
        return  $updateHistory;
    }


    public function placemenStatus($item, $model = 'placement')
    {
        if (
            $model == 'pullout' && $item->pullout_placed == "Yes" && $item->task_status == 'Completed' ||
            $model == 'maintenance' && $item->repair_status == "Yes" && $item->task_status == 'Completed' ||
            $item->is_deploy == true && $item->chiller_placed == "Yes" && $item->task_status == 'Completed'
        ) {
            return [
                "status" => "Completed",
                "message" => "Completed"
            ];
        }
        if ($item->asset_assigned_status == 'Approved') {
            return $this->approvalStatus($item->pending_from);
        }

        if ($item->asset_assigned_status == 'Rejected') {
            return $this->rejectStatus($item->rejected_by_user_role);
        }

        return ["message" => 'Pending at ASM', 'status' => 'Pending'];
    }

    private function approvalStatus($status)
    {
        $approvalStatus = [
            "ASM" => [
                "status" => "Pending",
                "message" => "Pending at  ASM."
            ],
            "RSM" => [
                "status" => "Pending",
                "message" => "Pending at RSM."
            ],
            "AE" => [
                "status" => "Pending",
                "message" => "Pending at AE"
            ],
            "VENDOR" => [
                "status" => "Pending",
                "message" => "Pending at VENDOR"
            ],
            "VE" => [
                "status" => "Pending",
                "message" => "Pending For Deployment"
            ],
        ];

        return isset($approvalStatus[$status]) ? $approvalStatus[$status] : $status;
    }
    private function RejectStatus($status)
    {
        $rejectStatus = [
            "ASM" => [
                "status" => "Rejected",
                "message" => "Rejected by  ASM."
            ],
            "RSM" => [
                "status" => "Rejected",
                "message" => "Rejected by at RSM."
            ],
            "AE" => [
                "status" => "Rejected",
                "message" => "Rejected by at AE"
            ]
        ];

        return isset($rejectStatus[$status]) ? $rejectStatus[$status] : $status;
    }

    private  function  getRequestCompleteStatus($model)
    {
        $request = [
            'placement' => 'chiller_placed',
            'pullout' => 'pullout_placed',
            'maintenance' => 'repair_status',
            'replacement' => 'chiller_placed',
        ];
        return $request[$model] ?? '';
    }
    public function deploymentStatus($asset, $type = 'placement')
    {
        //        $chiller_placed = $type == 'pullout' ? $asset->pullout_placed : $asset->chiller_placed;
        $requestDone = $this->getRequestCompleteStatus($type);

        $chiller_placed = property_exists($asset, $requestDone) ? ucfirst($asset->$requestDone) : null;

        if ($type == 'pullout') {
            $deploymentStatus = $asset->asset_assigned_status == 'Rejected'
                ? 'Pullout Rejected'
                : ($asset->is_pullout == true && $chiller_placed == 'Yes'
                    ? 'Pullout Completed'
                    : ($chiller_placed == 'No' && $asset->task_status == 'Completed'
                        ? 'Pullout Cancelled'
                        : 'Waiting for Pullout'));
        } elseif ($type == 'maintenance') {
            $deploymentStatus = $asset->asset_assigned_status == 'Rejected'
                ? 'Maintenance Rejected'
                : ($asset->is_maintenance == true && $chiller_placed == 'Yes'
                    ? 'Maintenance Completed'
                    : ($chiller_placed == 'No' && $asset->task_status == 'Completed'
                        ? 'Maintenance Cancelled'
                        : 'Waiting for Repair'));
        } elseif ($type == 'replacement') {
            $deploymentStatus = $asset->asset_assigned_status == 'Rejected'
                ? 'Replacememnt Rejected'
                : ($asset->is_deploy == true && $chiller_placed == 'Yes'
                    ? 'Replacement Completed'
                    : ($chiller_placed == 'No' && $asset->task_status == 'Completed'
                        ? 'Replacement Cancelled'
                        : 'Waiting for Replacement'));
        } else {
            $deploymentStatus = $asset->asset_assigned_status == 'Rejected'
                ? 'Task Rejected'
                : ($asset->is_deploy == true && $asset->chiller_placed == "Yes"
                    ? 'Chiller Deployed'
                    : ($chiller_placed == 'No' && $asset->task_status == 'Completed'
                        ? 'Chiller Not Deployed'
                        : 'Waiting for deployment'));
        }


        return $deploymentStatus;
    }
}
