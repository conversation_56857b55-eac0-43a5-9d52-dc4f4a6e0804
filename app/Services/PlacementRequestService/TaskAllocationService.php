<?php

namespace App\Services\PlacementRequestService;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\AssetPlacementRequest;
use Illuminate\Support\Facades\Validator;
use App\Services\PlacementRequestService\TaskStatusService;

class TaskAllocationService
{
    public function allocateTaskToVE($requestData)
    {
        DB::beginTransaction(); // Start a database transaction
        try {
            // Validation rules
            $validator = Validator::make($requestData, [
                'ExpectedDeploymentDate' => 'required|date',
                'assigned_organization' => 'required',
            ]);

            if ($validator->fails()) {
                return ['success' => false, 'errors' => $validator->errors()->all()];
            }

            $user = Auth::user();
            $requestNumber = $requestData['request_number'];
            $ExpectedDeploymentDate = $requestData['ExpectedDeploymentDate'];
            $assigned_organization = $requestData['assigned_organization'];
            $request_module = $requestData['request_type'] ?? 'placement';
            // Instantiate TaskStatusService
            $taskStatusService = new TaskStatusService();

            //update next  HeirarchyRole for assigning task
            $pending_from = $taskStatusService->getNextHeirarchyRole($user->user_type);
            $asset_assigned_status = "Approved";
            $updateData = [
                'asset_assigned_status' => $asset_assigned_status,
                'approved_by_user_role' => $user->user_type,
                'pending_from' => $pending_from,
                'assigned_organization' => $assigned_organization,
            ];
            $timestamps = [
                'placement' => [
                    'approved' => 'placement_approved_time',
                    'rejected' => 'placement_rejected_time',
                    'expected_date' => 'expected_deployment_date',
                ],
                'pullout' => [
                    'approved' => 'pullout_approved_time',
                    'rejected' => 'pullout_rejected_time',
                    'expected_date' => 'expected_pullout_date',
                ],
                'maintenance' => [
                    'approved' => 'maintenance_approved_time',
                    'rejected' => 'maintenance_rejected_time',
                    'expected_date' => 'expected_maintenance_date',
                ],
                'replacement' => [
                    'approved' => 'replacement_approved_time',
                    'rejected' => 'replacement_rejected_time',
                    'expected_date' => 'expected_deployment_date',
                ]
            ];

            $approvedKey = $timestamps[$request_module]['approved'] ?? null;
            $expectedDateKey = $timestamps[$request_module]['expected_date'] ?? null;

            if ($approvedKey) {
                $updateData[$approvedKey] = now();
            }

            if ($expectedDateKey) {
                $updateData[$expectedDateKey] = date('Y-m-d', strtotime($ExpectedDeploymentDate));
            }

            $modelClassData = PlacementRequestService::getModelWithRequestNumber($request_module);
            $modelClass = $modelClassData['model'];
            $placementRequestNumber = $modelClassData['request_number'];

            $updateStatus = $modelClass::where($placementRequestNumber, $requestNumber)->update($updateData);

            if (!$updateStatus) {
                Log::error('Error : Task not allocated to VE');
                DB::rollback();
                return ['success' => false, 'error' => 'Failed to allocate Task To VE'];
            }

            DB::commit(); // Commit the transaction

            // Update approval history
            $taskStatusService->approvalHistoryUpdate($requestData['request_number'], $asset_assigned_status);

            return ['success' => true, 'message' => 'Task Allocated successfully'];
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Error : allocateTaskToVE at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return ['success' => false, 'error' => 'Failed to allocate Task To VE'];
        }
    }
}
