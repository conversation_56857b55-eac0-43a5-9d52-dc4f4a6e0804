<?php

namespace App\Services\ErrorService;
use App\Jobs\ProcessFailedUpload;
use App\Models\AssetErrorLog;
use App\Models\UploadHistory;
use AWS\CRT\HTTP\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ErrorHandlingService
{
    public static function handleErrorLog($batchID, $message,$ip,$userType,$failed_type,$prepareUserData,$user_id,$name)
    {
//        $loginUser = $userData??Auth::user();
        $failedUploader = [
            'batch_id'      => $batchID,
            'error_message' => $message,
            'request_ip' => $ip,
            'userType' => $userType,
            'failed_type' => $failed_type,
            'error_data' => $prepareUserData,
            'uploadedTime' => now(),
            'uploadedBy' => $user_id ?? '<EMAIL>',
            'uploadByName' => $name ?? '',
        ];
         AssetErrorLog::create($failedUploader);
        Log::info('ErrorHandlingService handleErrorLog', $failedUploader);
        // ProcessFailedUpload::dispatch($failedUploader);
    }

    #Handle Excel file upload log
    public static function logUploadHistory($batchID, $totalRecords, $successRecords, $failedRecords, $failedData, $user_id, $name, $ip)
    {
        $updateData = [
            'batchID' => $batchID,
            'totalRecordscount' => $totalRecords,
            'successCount' => ($totalRecords-$failedRecords),
            'failedCount' => $failedRecords,
            'failedData' => json_encode($failedData),
            'updated_at' => now(),
            'uploadBy' => $user_id ?? '<EMAIL>',
            'uploadByName' => $name ?? '',
            'requestIP' => $ip ?? '',
        ];
        Log::error('ErrorHandlingService logUploadHistory', $updateData);
        return UploadHistory::where('batchID', $batchID)->update($updateData);
    }

    public static function downloadUploadHistory($request)
    {

    }
}
