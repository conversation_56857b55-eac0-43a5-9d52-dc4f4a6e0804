<?php

namespace App\Services\Approval;

class ApprovalService
{
    public static function getApprovalTime($type){
        $timestamps = [
            'placement' => [
                'approved' => 'placement_approved_time',
                'rejected' => 'placement_rejected_time',
                'expected_date' => 'expected_deployment_date',
                'chiller_type' => 'eligible_chiller_type',
                'asset_serial_number' => 'asset_serial_number',
                'asset_barcode' => 'asset_barcode',
            ],
            'pullout' => [
                'approved' => 'pullout_approved_time',
                'rejected' => 'pullout_rejected_time',
                'expected_date' => 'expected_pullout_date',
                'chiller_type' => 'pullout_asset_type',
                'asset_serial_number' => 'asset_serial_number',
                'asset_barcode' => 'asset_barcode',
            ],
            'maintenance' => [
                'approved' => 'maintenance_approved_time',
                'rejected' => 'maintenance_rejected_time',
                'expected_date' => 'expected_maintenance_date',
                'chiller_type' => 'chiller_type',
                'asset_serial_number' => 'asset_number',
                'asset_barcode' => 'asset_barcode',
            ],
            'replacement' => [
                'approved' => 'replacement_approved_time',
                'rejected' => 'replacement_rejected_time',
                'expected_date' => 'expected_deployment_date',
                'chiller_type' => 'chiller_type',
                'asset_serial_number' => 'asset_number',
                'asset_barcode' => 'asset_barcode',
            ]
        ];
        return $timestamps[$type]??$timestamps['placement'];
    }
    public static function redirectUrl($type){
        $redirectURL=[
            'pullout'=>'approval_center.pullout',
            'placement'=>'approval_center.placement',
            'maintenance'=>'approval_center.maintenance',
            'replacement'=>'approval_center.replacement'
        ];


        return $redirectURL[$type]??$redirectURL['placement'];
    }

}
