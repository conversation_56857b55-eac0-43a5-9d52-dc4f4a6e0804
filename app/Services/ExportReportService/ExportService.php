<?php
namespace App\Services\ExportReportService;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ExportService
{
    public static function getUsers(Request $request)
    {
        $auth = Auth::user();
        $user_type = $request->query('user_type', 'SO');
        $fromDate = self::convertDateFormate($request->query('from_date'));
        $toDate = self::convertDateFormate($request->query('to_date'));
        $keyword = $request->query('keyword');

        // Prepare the base query based on user_type
        $query = User::where('user_type', $user_type);
        if ($fromDate && $toDate) {
            $query->whereBetween('created_at', [$fromDate, $toDate]);
        }
        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->where('name', 'like', '%' . $keyword . '%')
                    ->orWhere('user_id', 'like', '%' . $keyword . '%');
            });
        }

        // Add relationships based on user_type
        if ($user_type === 'SO') {
            $query->with('asm');
        } elseif ($user_type === 'ASM') {
            $query->with('rsm');
        } elseif ($user_type === 'DSR') {
            $query->with('dsr');
        } elseif ($user_type === 'DISTRIBUTOR') {
            $query->with('so');
        } elseif ($user_type === 'RETAILER') {
            $query->with('userRetailer');
        }

        // Fetch users data and necessary column mappings
        $users = $query->latest();
        $headerColumns = User::getUserTableColumnAttribute($user_type);
        $dbColumns = User::getDBColumnsByUserType($user_type);

        return [
            'headerColumns' => $headerColumns,
            'dbQuery' => $users,
            'dbColumns' => $dbColumns,
        ];
    }

    private static function convertDateFormate($date)
    {
        if ($date) {
            try {
                return Carbon::createFromFormat('F j, Y', $date)->format('Y-m-d');
            } catch (\Exception $e) {
                Log::error("Error converting date: " . $e->getMessage());
            }
        }
        return null;
    }
}
