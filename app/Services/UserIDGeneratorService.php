<?php

namespace App\Services;

use App\Constants\RolePermissionConstants;
use App\Models\User; // Import the User model

class UserIDGeneratorService
{
    public static function generateUserID($role)
    {
        $format = RolePermissionConstants::UserIDFormat[$role] ?? null;
        if ($format) {
            $prefix = $format['prefix'] ?? '';
            $length = $format['length'] ?? null;

            if ($length !== null) {
                // For roles with a specified length, generate a random number and pad it with zeros
                $randomNumber = self::generateRandomNumber($length);
                return $prefix . $randomNumber;
            } else {
                // For roles with null length, generate the user ID based on the maximum ID from the database
                $latestUserId = User::max('id');
                $nextUserId = $prefix . str_pad($latestUserId + 1, 5, '0', STR_PAD_LEFT); // Assuming length is 5
                return $nextUserId;
            }
        }

        return '';
    }

    // private static function generateRandomNumber($length)
    // {
    //     // Generate a random number and pad it with zeros to match the specified length
    //     return str_pad(rand(1, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    // }

    private static function generateRandomNumber($length)
    {
        // Generate a random number within the specified range, excluding leading zeros
        $min = pow(10, $length - 1);
        $max = pow(10, $length) - 1;
        return rand($min, $max);
    }
}
