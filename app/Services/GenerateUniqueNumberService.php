<?php

namespace App\Services;

class GenerateUniqueNumberService
{
    public function generateNumber($prefix = '')
    {
        // Generate random bytes
        $randomNumberCount = $prefix ? 8 : 9;
        $randomBytes = random_bytes($randomNumberCount); // 8 bytes should be sufficient for most purposes

        // Convert random bytes to a hexadecimal string
        $hexString = bin2hex($randomBytes);

        // Remove non-numeric characters
        $numericPart = preg_replace('/[^0-9]/', '', $hexString);

        // Ensure the numeric part doesn't start with zero
        $numericPart = ltrim($numericPart, '0');

        // If the numeric part is empty, set it to '0'
        if (empty($numericPart)) {
            $numericPart = '0';
        }

        // Pad the numeric part to ensure it has at least 7 digits
        $numericPart = str_pad($numericPart, 7, '0', STR_PAD_LEFT);

        // Combine the prefix with the padded numeric portion
        $uniqueNumber = !empty($prefix) ? $prefix . $numericPart : $numericPart;

        return $uniqueNumber;
    }
}
