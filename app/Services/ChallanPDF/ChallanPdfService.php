<?php

namespace App\Services\ChallanPDF;

use App\Models\AssetPlacementRequest;
use App\Models\InvoiceChallan;
use App\Services\Approval\ApprovalService;
use App\Services\NumberToWordsService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Support\Facades\Log;
use PDF;
use Illuminate\Support\Str;

class ChallanPdfService
{
    #Generate challan pdf view and load data
    public function generateChillerInstallationChallanPDF($requestNumber,$request_type='placement')
    {

        try {
            $requestNumberList = is_array($requestNumber) ? $requestNumber : [$requestNumber];
            $pdfDataList = $this->generatePDF($requestNumberList,$request_type);
            if (!$pdfDataList) {
                return null;
            }
            $pdf = PDF::loadView('vendorAllocationChallan.placementAllocationChallan.chillerInstallation_challan', compact('pdfDataList'));
            return $pdf;
        } catch (\Exception $exception) {
            Log::error('Error : ChallanPdfService generateChallanPDF at line ' . $exception->getLine() . ': ' . $exception->getMessage());
            return null;
        }
    }

    #generate pdf for multiple request number
    private function generatePDF($requestNumber,$request_type)
    {
        $modelClassList = PlacementRequestService::getModelWithRequestNumber($request_type);
        $modeClass = $modelClassList['model'];
        $request_number = $modelClassList['request_number'];
        $placementRequestList = $modeClass::whereIn($request_number, $requestNumber)->with(['outlet.user', 'placementAssets', 'cfa', 'inventory'])->get();
        if (!$placementRequestList) {
            return null;
        }
        $placementData = [];
        $chillerType= ApprovalService::getApprovalTime($request_type)['chiller_type'];
        foreach ($placementRequestList as $placementRequestDetails) {

            $eligible_chiller_type = $placementRequestDetails->{$chillerType}??$placementRequestDetails->eligible_chiller_type;
            $placementRequestDetails['request_type']=$request_type;
            $placementRequestDetails['request_number'] = $request_number?$placementRequestDetails->{$request_number}:$placementRequestDetails->request_number;
            $inventory = $placementRequestDetails->inventory()->where(['warehouse_code' => $placementRequestDetails->cfa_code, 'asset_type' => $eligible_chiller_type])->first();
            $placementData[] = $this->Viewfield($placementRequestDetails,$inventory,$request_type);
        }
        return !empty($placementData) ? $placementData : null;
    }

    private function Viewfield($placementRequestDetails,$inventory,$request_type='placement')
    {
        $outletUser = $placementRequestDetails->outlet->user ?? null;
        $placementAssets = $placementRequestDetails->placementAssets ?? null;
        $gst_number = $placementRequestDetails->cfa->gst_no ?? null;
        $vendorAddress = $placementRequestDetails->cfa->address_1 ?? null;
        $challan_number = $this->getChallanNumber($placementRequestDetails->request_number,$request_type);
        $customerGSTnumber = optional($outletUser)->gst_no;
        $customerGSTstateCode = $customerGSTnumber ? $this->getGSTstateCode($customerGSTnumber) : '';
        $vendorGSTstateCode = $gst_number ? $this->getGSTstateCode($gst_number) : '';
        $asset_price = optional($inventory)->asset_price;
        $common_column= ApprovalService::getApprovalTime($request_type);
        $serial_number = $placementRequestDetails->{$common_column['asset_serial_number']}??optional($placementAssets)->asset_serial_number;
        $asset_barcode = $placementRequestDetails->{$common_column['asset_barcode']}??optional($placementAssets)->asset_barcode;
        $eligible_chiller_type = $placementRequestDetails->{$common_column['chiller_type']}??optional($placementRequestDetails)->eligible_chiller_type;
        $pdfData = [
            'customer_code' => optional($outletUser)->user_id,
            'request_number' => $placementRequestDetails->request_number,
            'challan_number' => $challan_number ?? '',
            'hsn_code' => '84185000',
            'date' => date("d-m-Y"),
            'outlet_name' => optional($outletUser)->name,
            'state' => optional($outletUser)->state,
            'city' => optional($outletUser)->city,
            'owner_name' => optional($outletUser)->name,
            'customer_gst_number' => $customerGSTnumber,
            'vendor_gst_state_code' => $vendorGSTstateCode,
            'customer_gst_state_code' => $customerGSTstateCode,
            'mobile_number' => !empty(trim($placementRequestDetails->mobile_number)) && $placementRequestDetails->mobile_number !='NULL' ? $placementRequestDetails->mobile_number : '',

            'chiller_type' =>  $eligible_chiller_type ?? '',
            'customer_address' => $placementRequestDetails->customer_address ?? '',
            'vendor_address' => $vendorAddress ?? '',
            'barcode' => $asset_barcode??'',
            'serial_number' => $serial_number??'',
            'gst_number' => $gst_number ?? '',
            'asset_price' => round($asset_price) ?? '',
            'asset_price_in_word' => !empty($asset_price)?NumberToWordsService::convert(round($asset_price)):'',
            'tax_price' => $asset_price ?? '',
        ];
        return $pdfData;
    }

    private function getChallanNumber($requestNumber,$request_type)
    {
        $modelClassList = PlacementRequestService::getModelWithRequestNumber($request_type);
        $modeClass = $modelClassList['model'];
        $request_number = $modelClassList['request_number'];
        $prefix = $modelClassList['prefix'];
        $challan_number = $modeClass::where($request_number, $requestNumber)->value('challan_number');
        if (!$challan_number) {
            $max_challan_number = InvoiceChallan::max('invoice_challan_number');
            $max_challan_number = is_numeric($max_challan_number) ? (int)$max_challan_number : 0;
            $challan_number = max(80001, $max_challan_number + 1);
            $modeClass::where($request_number, $requestNumber)->update(['challan_number' => $challan_number]);
            InvoiceChallan::updateOrCreate(
                ['request_number' => $requestNumber],
                [
                    'invoice_challan_number' => $challan_number,
                    'invoice_prefix' => $prefix,
                    'challan_created_datetime' => now()
                ]
            );

        }
//        return $prefix.$challan_number;
        return $challan_number;
    }

    public function getGSTstateCode($gst)
    {
        $gstNumber = $gst;
        $firstTwoCharacters = Str::substr($gstNumber, 0, 2);
        return $firstTwoCharacters;
    }
}
