<?php

namespace App\Services;

class UserValidationService
{
    // public function getUserTypeFields($userRole)
    public function getUserTypeFields()
    {
        $userTypeFields =  [
            "RSM" => [
                'validate' => ['ae_code', 'region_name', 'rsmrbdm_code', 'rsmrbdm_region_code', 'rsm_name'],
                'form' => ['ae_code', 'region_name', 'rsmrbdm_code', 'rsmrbdm_region_code', 'rsm_name','email_id', 'mobile_number', 'rbdm_name', 'rbdm_email', 'rbdm_mobile_number']
            ],
            "ASM" => [
                'validate' => ['ae_code','rsm_code', 'asm_area_code', 'asm_code', 'asm_name'],
                'form' => ['ae_code', 'rsm_code','rtmm_code', 'asm_area_code','asm_name', 'asm_code','email_id', 'mobile_number']
            ],
            "SO" => [
                'validate' => ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_user_code', 'so_teritory_code', 'so_name'],
                'form' => ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_teritory_code', 'so_user_code', 'so_name', 'mobile_number']
            ],
            "DISTRIBUTOR" => [
                'validate' => ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_code', 'channel', 'distributor_code', 'distributor_name', 'city'],
                'form' => ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_code', 'channel', 'cfa_code', 'distributor_code', 'distributor_name','city']
            ],
            "DSR" => [
                'validate' => ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_code', 'db_code', 'dsr_code', 'dsr_name'],
                'form' => ['ae_code', 'rsm_code', 'rtmm_code', 'asm_code', 'so_code', 'db_code', 'dsr_code', 'dsr_name', 'mobile_number']
            ],

            "VENDOR" => [
                'validate' => ['cfaplant_code', 'cfa_name', 'address_1', 'city', 'state', 'country', 'pin_code', 'effective_from', 'gst_no'],
                'form' => ['region', 'cfaplant_code', 'cfa_name', 'address_1', 'address_2', 'city', 'state', 'country', 'pin_code', 'effective_from', 'effective_to', 'gst_no'],

            ],
            "VE" => [
                'validate' => ['region', 'name', 'user_code', 'cfa_code', 'address_1', 'city', 'state', 'country', 'pin_code'],
                'form' => ['region', 'name', 'user_code', 'cfa_code', 'address_1', 'city', 'state', 'country', 'pin_code'],

            ],
            "RETAILER" => [
                'validate' => ['region', 'ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'retailercode', 'retailername','salesman_code','salesman_name'],
                'form' => ['region', 'ae_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code', 'retailercode', 'retailername', 'salesman_code', 'salesman_name', 'route_code', 'route_name', 'category_code', 'category_name', 'sub_channel_code', 'sub_channel_name', 'channel_code', 'channel_name', 'class_name', 'is_tcs', 'address1', 'address2', 'address3', 'invoiceaddress', 'pin_code', 'town', 'city', 'state', 'lat', 'long', 'contact_name', 'mobile', 'email', 'aadhar', 'credit_limit', 'credit_period', 'location_code', 'location_name', 'return_qty', 'return_value', 'drug_license_no', 'drug_license_exp_date', 'fssai_no', 'fssai_exp_date', 'retailer_created_on', 'modified_date', 'retailer_modified_on', 'retailer_reference_code','gst_no', 'active']

            ],
            'retailer_lat_long' => [
                'validate' => ['retailercode', 'lat', 'long', 'address1'],
                'form' => ['retailercode', 'lat', 'long', 'address1', 'address2', 'address3', 'pin_code'],
            ],
            "db_to_cfa_mapping" => [
                'validate' => ['distributor_code', 'city', 'state', 'cfa_code'],
                // 'validate' => ['region_code', 'region_name', 'channel', 'distributor_code', 'city', 'state', 'cfa_code'],
                // 'form' => ['region_code', 'region_name', 'channel', 'distributor_code', 'city', 'state', 'cfa_code'],
                'form' => ['distributor_code', 'city', 'state', 'cfa_code'],

            ],
            "asset_inventory_upload" => [
                'validate' => [ 'assetdescription', 'assettype', 'modelname', 'vendor', 'quantity', 'warehousecode', 'manufacturedyear', 'price', 'asset_serial_number', 'asset_barcode','vpo'],
                'form' => [ 'assetdescription', 'assettype', 'modelname', 'vendor', 'quantity', 'warehousecode', 'manufacturedyear', 'price', 'asset_serial_number', 'asset_barcode','vpo'],
            ],
            'retailer_gst_update' => [
                'validate' => ['retailercode', 'gst'],
                'form' => ['retailercode', 'gst'],
            ],
            'asset_retailer_mapping' => [
                'validate' => ['ae_code', 'rsm_code','asm_code','so_code','cfa_code','distributor_code','salesman_code','retailer_code','contact_person','customer_address','chiller_type_name','vpo','request_pending_from','request_status'],
                'form' => ['ae_code', 'rsm_code','asm_code','so_code','cfa_code','distributor_code','salesman_code','retailer_code','contact_person','customer_address','customer_location','current_location','asset_number','asset_barcode','chiller_type_name','vpo','request_number','date_of_placement','created_by','created_time','updated_by','updated_time','expected_vpo','request_pending_from','request_status'],
            ],

        ];
        // return $userTypeFields[$userRole] ?? null;
        return  $userTypeFields;
        // return $userTypeFields[$userRole] ?? null;
    }

    public function mapExcelToDatabaseFields($userRole, $excelField)
    {
        $fieldMappings = [
            "RSM" => [
                'rsm_name' => 'name',
                'rsmrbdm_code' => 'user_id',
                'rsmrbdm_region_code' => 'region_code',
                'region_name' => 'region',
                'mobile_number' => 'mobile_number',
                'rbdm_name' => 'rbdm_name',
                // 'rbdm_email' => 'rbdm_email',
                'rbdm_mobile_number' => 'rbdm_mobile_number',
            ],
            "ASM" => [
                // 'rsm_region_code' => 'region_code',
                'asm_area_code' => 'area_code',
                'asm_code' => 'user_id',
                'ae_code' => 'ae_code',
                'rsm_code' => 'rsm_id',
                'asm_name' => 'name',
                'mobile_number' => 'mobile_number',
                // 'email_id' => 'email',
                'rtmm_code' => 'rtmm_code',
            ],
            "SO" => [
                'so_teritory_code' => 'teritory_code',
                'so_user_code' => 'user_id',
                'so_name' => 'name',
                'mobile_number' => 'mobile_number',
                'ae_code' => 'ae_id',
                'rsm_code' => 'rsm_id',
                'rtmm_code' => 'rtmm_code',
                'asm_code' => 'asm_id',
            ],
            "DISTRIBUTOR" => [
                'so_teritory_code' => 'teritory_code',
                'channel' => 'channel_type',
                'cfa_code' => 'cfa_code',
                'distributor_code' => 'user_id',
                'distributor_name' => 'name',
                'ae_code' => 'ae_id',
                'rsm_code' => 'rsm_id',
                'rtmm_code' => 'rtmm_code',
                'asm_code' => 'asm_id',
                'so_code' => 'so_id',
            ],
            "DSR" => [
                'dsr_code' => 'user_id',
                'dsr_name' => 'name',
                'mobile_number' => 'mobile_number',
                'ae_code' => 'ae_id',
                'rsm_code' => 'rsm_id',
                'rtmm_code' => 'rtmm_code',
                'asm_code' => 'asm_id',
                'so_code' => 'so_id',
                'db_code' => 'distributor_id',
            ],
            "VENDOR" => [
                "region" => "region",
                "cfaplant_code" => "user_id",
                'cfa_name' => 'name',
                'address_1' => 'address_1',
                'address_2' => 'address_2',
                'city' => 'city',
                'state' => 'state',
                'country' => 'country',
                'pin_code' => 'pin_code',
                'effective_from' => 'effective_from',
                'effective_to' => 'effective_to',
                'gst_no' => 'gst_no',
            ],
            "VE" => [
                "region" => "region",
                'name' => 'name',
                "user_code" => "user_id",
                'cfa_code' => 'cfa_code',
                'address_1' => 'address_1',
                'city' => 'city',
                'state' => 'state',
                'country' => 'country',
                'pin_code' => 'pin_code',
            ],
            "retailer_lat_long" => [
                "retailercode" => "user_id",
                'lat' => 'lat',
                "long" => "long",
                'address1' => 'address_1',
                'address2' => 'address_2',
                'address3' => 'address_3',
                'pin_code' => 'pin_code',
            ],

            "RETAILER" => [
                'region' => 'region',
                'ae_code' => 'ae_id',
                'rsm_code' => 'rsm_id',
                'asm_code' => 'asm_id',
                'so_code' => 'so_id',
                'distributor_code' => 'distributor_id',
                'salesman_code' => 'dsr_id',
                'retailercode' => 'user_id',
                'retailername' => 'name',
                'salesman_code' => 'salesman_code',
                'salesman_name' => 'salesman_name',
                'route_code' => 'route_code',
                'route_name' => 'route_name',
                'category_code' => 'category_code',
                'category_name' => 'category_name',
                'sub_channel_code' => 'sub_channel_code',
                'sub_channel_name' => 'sub_channel_name',
                'channel_code' => 'channel_code',
                'channel_name' => 'channel_name',
                'class_name' => 'class_name',
                'pan_no' => 'pan_no',
                'gst_no' => 'gst_no',
                'is_tcs' => 'is_tcs',
                'address1' => 'address_1',
                'address2' => 'address_2',
                'address3' => 'address_3',
                'invoiceaddress' => 'invoiceaddress',
                'pin_code' => 'pin_code',
                'town' => 'town',
                'city' => 'city',
                'state' => 'state',
                'lat' => 'lat',
                'long' => 'long',
                'contact_name' => 'contact_name',
                'mobile' => 'mobile',
                'email' => 'email',
                'aadhar' => 'aadhar',
                'credit_limit' => 'credit_limit',
                'credit_period' => 'credit_period',
                'location_code' => 'location_code',
                'location_name' => 'location_name',
                'return_qty' => 'return_qty',
                'return_value' => 'return_value',
                'drug_license_no' => 'drug_license_no',
                'drug_license_exp_date' => 'drug_license_exp_date',
                'fssai_no' => 'fssai_no',
                'fssai_exp_date' => 'fssai_exp_date',
                'retailer_created_on' => 'retailer_created_on',
                'modified_date' => 'modified_date',
                'retailer_modified_on' => 'retailer_modified_on',
                'retailer_reference_code' => 'retailer_reference_code',
                'active' => 'active',
            ],
            "db_to_cfa_mapping" => [
                'region_code' => 'region_code',
                'region_name' => 'region',
                'channel' => 'channel_type',
                'distributor_code' => 'distributor_id',
                'city' => 'city',
                'state' => "state",
                'cfa_code' => "cfa_code",
            ],
            "asset_inventory_upload" => [
                'assetdescription' => 'description',
                'assettype' => 'asset_type',
                'modelname' => 'model_name',
                'vendor' => 'vendor',
                'quantity' => 'quantity',
                'warehousecode' => 'warehouse_code',
                'manufacturedyear' => 'manufactured_year',
                'price' => 'asset_price',
                'asset_serial_number' => 'serial_number',
                'asset_barcode' => 'barcode',
                'vpo' => 'vpo_target'
            ],
            "retailer_gst_update" => [
                "retailercode" => "user_id",
                "gst" => "gst_no",
            ],
            "asset_retailer_mapping" => [
                "ae_code" => "ae_code",
                "rsm_code" => "rsm_code",
                "asm_code" => "asm_code",
                "so_code" => "so_code",
                "cfa_code" => "cfa_code",
                "distributor_code" => "db_code",
                "salesman_code" => "dsr_code",
                "retailer_code" => "outlet_code",
                "customer_address" => "customer_address",
                "customer_location" => "customer_location",
                "current_location" => "current_location",
                "asset_number" => "asset_serial_number",
                "asset_barcode" => "asset_barcode",
                "chiller_type_name" => "eligible_chiller_type",
                "vpo" => "vpo",
                "request_number" => "request_number",
                "date_of_placement" => "deployment_date",
                "created_by" => "user_id",
                "created_time" => "created_at",
                "updated_by" => "user_id",
                "updated_time" => "updated_at",
                "expected_vpo" => "expected_vpo",
                "request_pending_from"=>"request_pending_from",
                "request_status"=>"request_status"

            ],

        ];

        // return isset($fieldMappings[$userRole]) && isset($fieldMappings[$userRole][$excelField])
        // ? $fieldMappings[$userRole][$excelField]
        // : null;
        return $fieldMappings[$userRole][$excelField] ?? null;
    }
}
