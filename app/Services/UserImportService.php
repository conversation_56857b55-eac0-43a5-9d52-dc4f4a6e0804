<?php
namespace App\Services;

use App\Imports\UserMasterImport;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Validation\ValidationException;

class UserImportService
{
    public function importUsers($file, $userRole)
    {
        try {
            // Excel::import(new UserMasterImport($userRole), $file, null, \Maatwebsite\Excel\Excel::XLSX);
            Excel::import(new UserMasterImport($userRole), $file);
        } catch (ValidationException $e) {
            // Re-throw the exception so it can be handled outside of this method
            throw $e;
        } catch (\Throwable $th) {
            // Log error for failed import
            Log::error('Failed to import users: ' . $th->getMessage());
            // Handle the error as required (e.g., notify admin, show error to user)
            throw $th; // Rethrow the exception to propagate it
        }
    }
}
