<?php

namespace App\Services;

class NumberToWordsService
{
    protected static $ones = [
        0 => '',
        1 => 'One',
        2 => 'Two',
        3 => 'Three',
        4 => 'Four',
        5 => 'Five',
        6 => 'Six',
        7 => 'Seven',
        8 => 'Eight',
        9 => 'Nine',
        10 => 'Ten',
        11 => 'Eleven',
        12 => 'Twelve',
        13 => 'Thirteen',
        14 => 'Fourteen',
        15 => 'Fifteen',
        16 => 'Sixteen',
        17 => 'Seventeen',
        18 => 'Eighteen',
        19 => 'Nineteen'
    ];

    protected static $tens = [
        0 => '',
        2 => 'Twenty',
        3 => 'Thirty',
        4 => 'Forty',
        5 => 'Fifty',
        6 => 'Sixty',
        7 => 'Seventy',
        8 => 'Eighty',
        9 => 'Ninety'
    ];

    protected static $hundreds = [
        'Hundred',
        'Thousand',
        'Million',
        'Billion',
        'Trillion',
        'Quadrillion',
        'Quintillion'
    ];

    public static function convert($number)
    {
        if ($number == 0) {
            return 'Zero';
        }

        $numStr = number_format($number, 2, '.', '');
        list($num, $dec) = explode('.', $numStr);

        $numInWords = self::convertNumberToWords((int)$num);
        // $decInWords = self::convertNumberToWords((int)$dec);

        // return trim($numInWords . '.' . $decInWords);
        return trim($numInWords.' Rupees');
    }

    protected static function convertNumberToWords($number)
    {
        if ($number < 20) {
            return self::$ones[$number];
        } elseif ($number < 100) {
            return self::$tens[(int)($number / 10)] . ' ' . self::$ones[$number % 10];
        } elseif ($number < 1000) {
            return self::$ones[(int)($number / 100)] . ' ' . self::$hundreds[0] . ' ' . self::convertNumberToWords($number % 100);
        } else {
            for ($i = 1, $unit = 1000; $unit <= $number; $i++, $unit *= 1000) {
                $remainder = $number % $unit;
                $mainPart = (int)($number / $unit);

                if ($remainder > 0) {
                    return self::convertNumberToWords($mainPart) . ' ' . self::$hundreds[$i] . ' ' . self::convertNumberToWords($remainder);
                } else {
                    return self::convertNumberToWords($mainPart) . ' ' . self::$hundreds[$i];
                }
            }
        }
    }
}
