<?php

namespace App\Services;

class AdminUserCreateValidationService
{
    public function getAdminUserTypeFields()
    {
        $commonField = ['user_type', 'channel_code', 'teritory_code', 'user_code', 'name', 'email', 'mobile_number', 'region_name', 'region_code', 'area_code', 'cfa_code', 'city', 'state', 'pincode', 'distributor_code', 'so_code', 'asm_code', 'rsm_code', 'rbdm_code', 'dsr_code'];
        $userTypeFields =  [
            "RBDM_GTMS" => [
                'validate' => ['user_type', 'user_code', 'name', 'email', 'city', 'state', 'pincode'],
                'form' =>  $commonField,
            ],
            "RSM" => [
                'validate' => ['user_type', 'user_code', 'name', 'region_name', 'region_code', 'rbdm_code'],
                'form' =>  $commonField,
            ],
            "ASM" => [
                'validate' => ['user_type', 'user_code', 'name', 'teritory_code', 'rsm_code', 'rbdm_code', 'region_code', 'area_code'],
                'form' => $commonField,
            ],
            "SO" => [
                'validate' => ['user_type', 'user_code', 'name', 'teritory_code', 'asm_code', 'rsm_code', 'rbdm_code'],
                'form' =>  $commonField,
            ],
            "DISTRIBUTOR" => [
                'validate' => ['user_type', 'user_code', 'name', 'channel_code', 'rbdm_code', 'rsm_code', 'asm_code', 'so_code'],
                'form' =>  $commonField,
            ],
            "DSR" => [
                'validate' => ['user_type', 'user_code', 'name', 'rbdm_code', 'rsm_code', 'asm_code', 'so_code', 'distributor_code'],
                'form' =>  $commonField,
            ],

            "CFA" => [
                'validate' => ['region_code', 'cfa_plant_code', 'cfa_name', 'address_1', 'address_2', 'city', 'state', 'country', 'pin_code', 'effective_from', 'effective_to', 'gst_number'],
                'form' => ['region_code', 'cfa_plant_code', 'cfa_name', 'address_1', 'address_2', 'country', 'pin_code', 'effective_from', 'effective_to', 'gst_number'],

            ],
            "RETAILER" => [
                'validate' => [
                    'user_type',
                    'user_code',
                    'cfa_code',
                    'dsr_code',
                    'dsr_name',
                    'rbdm_code',
                    'rsm_code',
                    'asm_code',
                    'so_code',
                    'distributor_code',
                    'retailercode',
                    'retailername',
                    'salesman_code',
                    // 'mobile_number',
                    'channel_code',
                    'channel_name',
                    'region_name',
                    'region_code',
                    'town',
                    'city',
                    'state',
                    'pincode',
                    'route_code',
                    'route_name',
                    'category_code',
                    'category_name',
                    'sub_channel_code',
                    'sub_channel_name',
                    'class_name',
                    'pan_no',
                    'gst_no',
                    'address1',
                    'address2',
                    'address3',
                    'invoiceaddress',
                    'is_tcs',
                    'lat',
                    'long',
                    'contact_name',
                    'aadhar',
                    'credit_period',
                    'credit_limit',
                    'location_code',
                    'return_qty',
                    'return_value',
                    'drug_license_no',
                    'drug_license_exp_date',
                    'fssai_no',
                    'fssai_exp_date',
                    'retailer_reference_code',
                    'status',
                ],
                'form' => [
                    'region',
                    'rbdm_code',
                    'rsm_code',
                    'asm_code',
                    'so_code',
                    'distributor_code',
                    'retailercode',
                    'retailername',
                    'salesman_code',
                    'salesman_name', // Assuming this field corresponds to 'salesman_name' in your 'validate' array
                    'route_code',
                    'route_name',
                    'category_code',
                    'category_name',
                    'sub_channel_code',
                    'sub_channel_name',
                    'channel_code',
                    'channel_name',
                    'class_name',
                    'is_tcs',
                    'address1',
                    'invoiceaddress',
                    'pin_code', // Corrected from 'pincode' to 'pin_code' based on your 'validate' array
                    'town',
                    'city',
                    'state',
                    'lat',
                    'long',
                    'contact_name',
                    'mobile', // Assuming this field corresponds to 'mobile_number' in your 'validate' array
                    'email',
                    'aadhar',
                    'credit_limit',
                    'credit_period',
                    'location_code',
                    'location_name',
                    'return_qty',
                    'return_value',
                    'drug_license_no',
                    'drug_license_exp_date',
                    'fssai_no',
                    'fssai_exp_date',
                    'retailer_created_on',
                    'modified_date',
                    'retailer_modified_on',
                    'retailer_reference_code',
                    'active'
                ],
            ],
        ];

        return $userTypeFields;
    }

    public function mapExcelToDatabaseFields($userRole, $excelField)
    {

        $createCommon = [
            'name' => 'name',
            'email' => 'email',
            'channel_type' => 'channel_type',
            'teritory_code' => 'teritory_code',
            'mobile_number' => 'mobile_number',
            'region' => 'region_name',
            'region_code' => 'region_code',
            'area_code' => 'area_code',
            'cfa_code' => 'cfa_code',
            'city' => 'city',
            'state' => 'state',
            'pincode' => 'pin_code',
            'distributor_id' => 'distributor_code',
            'so_id' => 'so_code',
            'asm_id' => 'asm_code',
            'rsm_id' => 'rsm_code',
            'rbdm_id' => 'rsm_code',
            'user_id' => 'user_id',
            'user_type' => 'user_type',
            'address_1' => 'address_1',
            'status' => 'status',
        ];
        $fieldMappings = [
            "RSM" => $createCommon,
            "ASM" => $createCommon,
            "DSR" => $createCommon,
            "DISTRIBUTOR" => $createCommon,
            "SO" => $createCommon,
            "CFA" => [
                "cfa_plant_code" => "cfa_plant_code",
                'cfa_name' => 'cfa_name',
                'address_1' => 'address_1',
                'address_2' => 'address_2',
                'city' => 'city',
                'state' => 'state',
                'country' => 'country',
                'pin_code' => 'pin_code',
                'effective_from' => 'effective_from',
                'effective_to' => 'effective_to',
                'gst_number' => 'gst_no',
            ],
            "RETAILER" => [
                'region' => 'region_code',
                'rbdm_code' => 'rbdm_id',
                'rsm_code' => 'rsm_id',
                'asm_code' => 'asm_id',
                'so_code' => 'so_id',
                'distributor_code' => 'distributor_id',
                'user_code' => 'user_id',
                'retailername' => 'name',
                'dsr_code' => 'dsr_id',
                'dsr_name' => 'dsr_name',
                'route_code' => 'route_code',
                'route_name' => 'route_name',
                'category_code' => 'category_code',
                'category_name' => 'category_name',
                'sub_channel_code' => 'sub_channel_code',
                'sub_channel_name' => 'sub_channel_name',
                'channel_code' => 'channel_code',
                'channel_name' => 'channel_name',
                'class_name' => 'class_name',
                'pan_no' => 'pan_no',
                'gst_no' => 'gst_no',
                'is_tcs' => 'is_tcs',
                'address1' => 'address_1',
                'address2' => 'address_2',
                'address3' => 'address_3',
                'invoiceaddress' => 'invoiceaddress',
                'pin_code' => 'pin_code',
                'town' => 'town',
                'city' => 'city',
                'state' => 'state',
                'lat' => 'lat',
                'long' => 'long',
                'contact_name' => 'contact_name',
                'mobile' => 'mobile_number',
                'email' => 'email',
                'aadhar' => 'aadhar',
                'credit_limit' => 'credit_limit',
                'credit_period' => 'credit_period',
                'location_code' => 'location_code',
                'return_qty' => 'return_qty',
                'return_value' => 'return_value',
                'drug_license_no' => 'drug_license_no',
                'drug_license_exp_date' => 'drug_license_exp_date',
                'fssai_no' => 'fssai_no',
                'fssai_exp_date' => 'fssai_exp_date',
                'retailer_created_on' => 'created_at',
                'modified_date' => 'updated_at',
                'retailer_modified_on' => 'updated_at',
                'retailer_reference_code' => 'retailer_reference_code',
                'active' => 'status',
            ],


        ];

        return $fieldMappings[$userRole][$excelField] ?? null;
    }
}
