<?php

namespace App\Services\IvyAssetMaster;

use App\Constants\IVYConstant;
use App\Jobs\ProcessUserImportRow;
use App\Services\ErrorService\ErrorHandlingService;
use App\Services\HeirarchyService;
use Illuminate\Support\Facades\Log;

class IvyAssetMasterService
{
    public $failedRecords = 0;

    public static function assetRetailerMasterStored($request, $requestIP, $batchID)
    {
        $service = new self();
        $userId = IVYConstant::IVYUSERID;
        $uploadByName = IVYConstant::IVYUSERNAME;
        $userRole = IVYConstant::IVYUSERROLE;
        $authUser = $service->authUser($userId, $uploadByName);
        try {
            foreach ($request as $row) {
                $row['AECode']='AE_001';
                $requiredMissingFields = $service->validateRequiredField($row);
                // Collect codes and their corresponding roles to check
                $codesToCheck = [
                    'AECode' => $row['AECode'] ?? null,
                    'RSMCode' => $row['RSMCode'] ?? null,
                    'ASMCode' => $row['ASMCode'] ?? null,
                    'SOCode' => $row['SOCode'] ?? null,
                    'DistributorCode' => $row['DistributorCode'] ?? null,
                ];
                $missingCodes = HeirarchyService::checkHeirarchyUserExist($codesToCheck);

                if (!empty($requiredMissingFields) || !empty($missingCodes)) {
                    $service->failedRecords++;
                    $errorMessage = '';
                    if (!empty($requiredMissingFields)) {
                        $errorMessage .= 'Validation failed: Missing or empty fields - ' . implode(', ', $requiredMissingFields) . '. ';
                    }

                    if (!empty($missingCodes)) {
                        $errorMessage .= 'Hierarchy code does not exist - ' . implode(', ', array_keys($missingCodes));
                    }
                    $service->failedDataErrorLog($batchID, $authUser, $errorMessage, $row, $userRole);
                    Log::error('Error validating row:', ['message' => $errorMessage]);

                    continue; // Skip to the next row
                }

                $userData = [
                    'user_id' => $row['RetailerCode'] ?? '',
                    'user_type' => 'RETAILER',
                    'email' => $row['Email'] ?? '',
                    'mobile_number' => $row['Mobile'] ?? '',
                    'rsm_id' => $prepareUserData['RSMCode'] ?? '',
                    'asm_id' => $prepareUserData['ASMCode'] ?? '',
                    'so_id' => $prepareUserData['SOCode'] ?? '',
                    'distributor_id' => $prepareUserData['DistributorCode'] ?? '',
                    'dsr_id' => $prepareUserData['SalesManCode'] ?? '',
                    'emp_code' => $prepareUserData['SalesManEmpCode'] ?? '',
                    'rtmm_code' => $prepareUserData['rtmm_code'] ?? '',
                    'ae_id' => $prepareUserData['ae_id'] ?? 'AE_001',
                    'batchID' => $batchID,
                    'uploadBy' => $userId,
                    'uploadByName' => $uploadByName,
                    'dataUploadByOrganisation' => 'IVY',
                    'uploadTime' => now(),
                ];
                $retailerData = [
                    'region' => $row['Region'] ?? '',
                    // 'ae_id' => $row['AECode'] ?? '',
                    // 'rsm_id' => $row['RSMCode'] ?? '',
                    // 'asm_id' => $row['ASMCode'] ?? '',
                    // 'so_id' => $row['SOCode'] ?? '',
                    // 'distributor_id' => $row['DistributorCode'] ?? '',
                    // 'dsr_id' => $row['SalesManCode'] ?? '',
                    'user_id' => $row['RetailerCode'] ?? '',
                    'name' => $row['RetailerName'] ?? '',
                    'salesman_code' => $row['SalesManCode'] ?? '',
                    'emp_code' => $row['SalesManEmpCode'] ?? '',
                    'salesman_name' => $row['SalesManName'] ?? '',
                    'route_code' => $row['RouteCode'] ?? '',
                    'route_name' => $row['RouteName'] ?? '',
                    'category_code' => $row['CategoryCode'] ?? '',
                    'category_name' => $row['CategoryName'] ?? '',
                    'sub_channel_code' => $row['SubChannelCode'] ?? '',
                    'sub_channel_name' => $row['SubChannelName'] ?? '',
                    'channel_code' => $row['ChannelCode'] ?? '',
                    'channel_name' => $row['ChannelName'] ?? '',
                    'class_name' => $row['ClassName'] ?? '',
                    'pan_no' => $row['PANNumber'] ?? '',
                    'gst_no' => $row['GSTNumber'] ?? '',
                    'is_tcs' => $row['isTcs'] ?? '',
                    'address_1' => $row['Address1'] ?? '',
                    'address_2' => $row['Address2'] ?? '',
                    'address_3' => $row['Address3'] ?? '',
                    'invoiceaddress' => $row['InvoiceAddress'] ?? '',
                    'pin_code' => $row['PinCode'] ?? '',
                    'town' => $row['Town'] ?? '',
                    'city' => $row['City'] ?? '',
                    'state' => $row['State'] ?? '',
                    'lat' => $row['Lat'] ?? '',
                    'long' => $row['Long'] ?? '',
                    'contact_name' => $row['ContactName'] ?? '',
                    'mobile' => $row['Mobile'] ?? '',
                    'email' => $row['Email'] ?? '',
                    'aadhar' => $row['Aadhar'] ?? '',
                    'credit_limit' => $row['CreditLimit'] ?? '',
                    'credit_period' => $row['CreditPeriod'] ?? '',
                    'location_code' => $row['LocationCode'] ?? '',
                    'location_name' => $row['LocationName'] ?? '',
                    'return_qty' => $row['ReturnQty'] ?? '',
                    'return_value' => $row['ReturnValue'] ?? '',
                    'drug_license_no' => $row['DrugLicenseNo'] ?? '',
                    'drug_license_exp_date' => $row['DrugLicenseExpDate'] ?? '',
                    'fssai_no' => $row['FSSAINo'] ?? '',
                    'fssai_exp_date' => $row['FSSAIExpDate'] ?? '',
                    'retailer_created_on' => $row['RetailerCreatedOn'] ?? '',
                    'modified_date' => $row['ModifiedDate'] ?? '',
                    'retailer_modified_on' => $row['RetailerReferenceCode'] ?? '',
                    'retailer_reference_code' => $row['RetailerCode'] ?? '',
                    'uploadBy' => $row['CreatedBy'] ?? '',
                    'created_at' => $row['CreatedTime'] ?? '',
                    'updated_at' => $row['UpdatedTime'] ?? '',
                    // 'uploadByName' => $uploadByName,
                    'active' => $row['Active'] ?? 'yes',
                ];
                $userData = array_merge($userData, $retailerData);
                ProcessUserImportRow::dispatch($userId, 'RETAILER', $userData, $retailerData, $row, $authUser, request()->ip());
            }
            return $batchID;
        } catch (\Exception $e) {
            $service->failedRecords++;
            $service->failedDataErrorLog($batchID, $authUser, $e->getMessage(), $row, $userRole);
            Log::error('Error IvyAssetMasterService asset Retailer master validating row: ' . $e->getMessage());
            return false;
        }
    }

    public function failedDataErrorLog($batchID, $authUser, $message, $prepareUserData, $userRole)
    {
        ErrorHandlingService::handleErrorLog($batchID, $message, request()->ip(), $authUser->user_type, $userRole, $prepareUserData, $authUser->user_id, $authUser->name);
    }

    public static function authUser($ivyUserId, $uploadByName)
    {
        $authUser = new \stdClass();
        $authUser->user_type = 'RETAILER';
        $authUser->user_id = $ivyUserId;
        $authUser->name = $uploadByName;
        return $authUser;
    }

    public function validateRequiredField($row)
    {
        $validateData = [
            //Region',
            'AECode', 'RSMCode', 'ASMCode', 'SOCode', 'DistributorCode', 'RetailerCode', 'RetailerName', 'SalesManCode', 'SalesManName', 'SalesManEmpCode', 'CategoryCode', 'CategoryName',
            'ClassName', 'Address1', 'PinCode', 'City', 'State', 'Lat', 'Long', 'LocationCode', 'LocationName', 'RetailerCreatedOn', 'CreatedBy', 'CreatedTime'
        ];
        $requiredMissingFields = [];
        foreach ($validateData as $field) {
            if (empty($row[$field])) {
                $requiredMissingFields[] = $field;
            }
        }
        return $requiredMissingFields;
    }
}
