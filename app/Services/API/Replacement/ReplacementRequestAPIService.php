<?php

namespace App\Services\API\Replacement;

use App\Constants\PlacementApprovalMessage;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\AssetReplacementRequest;
use App\Models\InvoiceChallan;
use App\Models\MaintenanceRequest;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class ReplacementRequestAPIService
{
    public static function buildQuery($userId, $type, $status, $fromDate, $toDate)
    {
        return AssetReplacementRequest::where('user_id', $userId)
            ->with(['placement','retaileroutlets:id,route_name,salesman_name','retailer'])
            ->where('type', $type)
            ->when($status !== 'All' && $status !== '', function ($query) use ($status) {
                return $query->where('asset_assigned_status', $status);
            })
            ->when($fromDate || $toDate, function ($query) use ($fromDate, $toDate) {
                if ($fromDate) {
                    $query->whereDate('created_at', '>=', Carbon::parse($fromDate));
                }
                if ($toDate) {
                    $query->whereDate('created_at', '<=', Carbon::parse($toDate));
                }
            });
    }

    public static function formatReplacementRequests($maintenanceRequests)
    {
        return $maintenanceRequests->map(function ($data) {
            $taskStatusService = new TaskStatusService();
            $approvalData = $taskStatusService->placemenStatus($data, 'replacement');
            $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
            $message = !empty($approvalData['message']) ? $approvalData['message'] : null;
            $placement=AssetPlacementRequest::where('asset_serial_number',$data->asset_number)->first();
            return [
                "RequestNumber" => $data->request_number ?? '',
                "RequestNumber" => $data->request_number ?? '',
                "AssetNumber" => $data->asset_number ?? '',
                "PlacementRequestNumber" => $placement->request_number ?? '',
                "CustomerName" => $data->retailer->name ?? '',
                "ReplacementReason" => $data->replacement_reason ?? '',
                "RequestStatus" => $data->asset_assigned_status ?? '',
                "CustomerCode" => $data->outlet_code ?? '',
                "CustomerAddress" => $data->customer_address ?? '',
                "DSRName" => $data->retaileroutlets->salesman_name ?? '',
                "RouteName" => $data->retaileroutlets->route_name ?? '',
                "ChillerType" => $data->chiller_type ?? '',
                "NewChillerType" => $data->request_chiller_type ?? '',
                "DateTime" => $data->created_at->format("Y-m-d H:i:s"),
                "Status" => $approvalStatus ?? '',
                "RequestStatus" => $approvalStatus ?? '',
                'ApprovalMessage' => $message ?? null,
                'PendingAt' => $data->pending_from,
                "RequestType" => $data->request_type ?? 'Normal',
                "IsBoltTown" => $data->is_bolt_town ?? 0,
            ];
        })->toArray();
    }

    public static function GetAssetReplacementRequestDetails($request)
    {
        $replaceent = AssetReplacementRequest::where(['user_id' => $request['user_id'], 'type' => $request['type'], 'request_number' => $request['request_number']])
            ->with(['placement','retaileroutlets:id,route_name,salesman_name,contact_name','retailer'])->first();
        if (!$replaceent) {
            return [];
        }
        $taskStatusService = new TaskStatusService();
        $approvalData = $taskStatusService->placemenStatus($replaceent, 'replaceent');
        $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
        $message = !empty($approvalData['message']) ? $approvalData['message'] : null;
        $placement=AssetPlacementRequest::where('asset_serial_number',$replaceent->asset_number)->first();
        return [
            "RequestNumber" => $replaceent->request_number ?? '',
            "DateTime" => $replaceent->created_at->format("Y-m-d H:i:s"),
            "RequestStatus" => $replaceent->asset_assigned_status ?? '',
            "ChillerType" => $replaceent->chiller_type ?? '',
            "CurrentVPO" => $replaceent->current_vpo ?? '',
            "AssetNumber" => $replaceent->asset_number ?? '',
            "AssetBarcode" => $replaceent->asset_barcode ?? '',
            "PlacementRequestNumber" => $placement->request_number ?? '',
            "ExpectedVPO" => $replaceent->expected_vpo ?? '',
            "RequestChillerType" => $replaceent->request_chiller_type ?? '',
            'AdditionalEquipment' => json_decode($replaceent->additional_equipment),
            'ChillerLocation' => $replaceent->chiller_location,
            'ChillerLocationPhoto' => asset($replaceent->chiller_location_photo),
            'RetailerPhoto' => asset($replaceent->retailer_photo),
            'AddressProof' => asset($replaceent->address_proof),
            "CustomerName" => $replaceent->retailer->name ?? '' ?? '',
            "DSRName" => $replaceent->retaileroutlets->salesman_name ?? '',
            "RouteName" => $replaceent->retaileroutlets->route_name ?? '',
            "ContactPerson" => $replaceent->retaileroutlets->contact_name ?? '',
            "ContactNumber" => $replaceent->retailer->mobile_number ?? '',
            "Signature" => $replaceent->signature ? asset($replaceent->signature) : '',
            "MobileNumber" => $replaceent->mobile_number ?? '',
            "CustomerAddress" => $replaceent->customer_address ?? '',
            "Pincode" => $replaceent->pincode ?? '',
            "ReplacementReason" => $replaceent->replacement_reason ?? '',
            "CustomerLocation" => $replaceent->customer_location ?? '',
            "CurrentLocation" => $replaceent->current_location ?? '',
            "CorrectLocation" => $replaceent->correct_location ?? 'No',
            "Distance" => $replaceent->distance ?? '',
            "RequestType" => $replaceent->request_type ?? 'Normal',
            "Remarks" => $replaceent->remarks ?? '',
            "Status" => $approvalStatus ?? '',
            'ApprovalMessage' => $message ?? null,
            'IsBoltTown' => $replaceent->is_bolt_town ?? 0,
            'PendingAt' => $replaceent->pending_from
        ];
    }
    public static function parseDate($date)
    {
        return $date ? Carbon::createFromFormat('Y-m-d', $date) : null;
    }
    public static function replacementTaskRequestList($request, $user)
    {
        $from_Date = $request->input('FromDate') ? self::parseDate($request->input('FromDate')) : '';
        $to_date = $request->input('ToDate') ? self::parseDate($request->input('ToDate')) : '';
        $keyword = $request->input('Keyword');
        $taskStatus = $request->input('task_status') ??'Pending';
        $offset = $request->input('Offset', 0);
        $limit = $request->input('Limit', 10);
        $query = AssetReplacementRequest::where('assigned_organization', $user->user_id)->where('asset_assigned_status', '!=', 'Rejected')
            ->with(['outlet.user','retaileroutlets:id,route_name,salesman_name,contact_name','retailer', 'approvalHistory', 'replacementTaskDeploy']);

        if ($from_Date) {
            $query->whereDate('created_at', '>=', Carbon::parse($from_Date));
        }

        if ($to_date) {
            $query->whereDate('created_at', '<=', Carbon::parse($to_date));
        }
        if ($taskStatus && $taskStatus !== 'All') {
            $query->where('task_status', $taskStatus);
        }

        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->whereHas('outlet.user', function ($query) use ($keyword) {
                    $query->where('name', 'like', '%' . $keyword . '%')
                        ->orWhere('user_id', 'like', '%' . $keyword . '%');
                })
                    ->orWhere('request_number', 'like', '%' . $keyword . '%');
            });
        }
        if ($taskStatus === 'completed') {
            // $query->whereHas('replacementTaskDeploy')->orderBy('updated_at', 'desc');
        } else {
            $query->latest('created_at');
        }
        $assetPlacementList = $query->offset($offset)
            ->limit($limit)->get();
        return self::ReplacementTaskFormat($assetPlacementList);
    }
    public static function ReplacementTaskFormat($assetPlacementList)
    {
        $responseData = $assetPlacementList->map(function ($item) {
            $deploy = $item->replacementTaskDeploy;
            return [
                'outlet_id' => $item->outlet_id ?? '',
                'outlet_code' => $item->outlet_code ?? '',
                'request_no' => $item->request_number,
                'placement_request_number' => $item->placement_request_number, //placement request nuber
                'customer_name' => $item->outlet->user->name ?? '',
                'customer_code' => $item->outlet->user->user_id ?? '',
                'customer_address' => $item->outlet->user->address_1 ?? '',
                'dsr_name' => $item->outlet->salesman_name,
                'route_name' => $item->outlet->route_name,
                'asset_number' => $item->asset_number ?? null, //
                'asset_barcode' => $item->asset_barcode ?? null,
                'request_chiller_type' => $item->request_chiller_type,
                'chiller_type' => $item->chiller_type,
                "AssetTypeCode" => $item->asset_type_code ?? '',
                "RequestAssetTypeCode" => $item->request_chiller_code ?? '',
                'replacement_reason' => $item->replacement_reason,
                'completion_date' => $item->replacement_complete_date ? date("Y-m-d", strtotime($item->replacement_complete_date)) : null,
                'expected_date' => $item->expected_deployment_date ? date("Y-m-d", strtotime($item->expected_deployment_date)) : null,
                'status' => $item->task_status,
                'ReplacedAssetNumber' =>$deploy->replaced_asset_number??'',
                'ReplacedAssetBarcode' => $deploy->replaced_asset_barcode??'',
                'latitude' => $item->latitude,
                'longitude' => $item->longitude,
                'contact_number' => $item->retailer->mobile_number ?? '',
                'contact_person' => $item->retaileroutlets->contact_name ?? '',
                'customer_email' => $item->outlet->user->email ?? '',
                'contact_email' => $item->email ?? '',
                'created_at' => $item->created_at ?? '',
            ];
        });
        return $responseData ?? [];
    }

    public static function ReplacementTaskRequestDetails($request, $user)
    {
        $item = AssetReplacementRequest::where(['assigned_organization'=> $user->user_id,'request_number'=>$request['RequestNumber']])->where('asset_assigned_status', '!=', 'Rejected')
        ->with(['outlet.user', 'replacementTaskDeploy','retaileroutlets:id,route_name,salesman_name,contact_name'])->first();
        if (!$item) {
            return [];
        }
            $deploy = $item->replacementTaskDeploy;
            return [
                'outlet_id' => $item->outlet_id ?? '',
                'request_no' => $item->request_number,
                'outlet_code' => $item->outlet_code ?? '',
                'completion_date' => $item->replacement_complete_date ?? '',
                'asset_replaced'=>$item->chiller_placed ?? 'No',
                'status' => $item->task_status,
                'placement_request_number' => $item->placement_request_number, //placement request nuber
                'prev_assigned_asset_number' => $item->prev_assigned_asset_number ?? null, // already  assigned asset number
                'customer_name' => $item->outlet->user->name ?? '',
                'customer_code' => $item->outlet->user->user_id ?? '',
                'customer_address' => $item->outlet->user->address_1 ?? '',
                'asset_number' => $item->asset_number ?? null, //
                'asset_barcode' => $item->asset_barcode ?? null,
                'replacement_reason' => $item->replacement_reason,
                'contact_person' => $item->retaileroutlets->contact_name ?? '',
                'contact_number' => $item->retailer->mobile_number ?? '',
                'customer_email' => $item->outlet->user->email ?? '',
                'contact_email' => $item->email ?? '',
                'outlet_photo' => !empty($deploy->outlet_photo) ? asset($deploy->outlet_photo) : null,
                'retailer_photo' => !empty($deploy->retailer_photo) ? asset($deploy->retailer_photo) : null,
                'chiller_type' => $item->chiller_type,
                'request_chiller_type' => $item->request_chiller_type,
                'replacement_asset_number' => $deploy->replaced_asset_number ?? null,
                'replaced_asset_barcode' => $deploy->replaced_asset_barcode ?? null,
                'chiller_image' => !empty($deploy->outlet_photo) ? asset($deploy->outlet_photo) : null,
                'customer_location' => $item->outlet->user->address_1 ?? '',
                'customer_location' => $maintenance->outlet->user->address_1 ?? '',
                'current_location' => $deploy->current_location ?? '',
                'distance' => $deploy->distance ?? '',
                'remarks' => $deploy->remarks ?? '',
                'created_at' => $item->created_at ?? '',
                'is_bolt_town' => $item->is_bolt_town ?? 0,
            ];
    }
}

