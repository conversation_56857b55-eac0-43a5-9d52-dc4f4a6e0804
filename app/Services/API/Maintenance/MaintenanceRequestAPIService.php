<?php

namespace App\Services\API\Maintenance;

use App\Constants\PlacementApprovalMessage;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\InvoiceChallan;
use App\Models\MaintenanceRequest;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class MaintenanceRequestAPIService
{
    public static function buildQuery($userId, $type, $status, $fromDate, $toDate)
    {
        return MaintenanceRequest::where('user_id', $userId)
            ->with(['retaileroutlets:id,route_name,salesman_name'])
            ->where('type', $type)
            ->when($status !== 'All' && $status !== '', function ($query) use ($status) {
                return $query->where('asset_assigned_status', $status);
            })
            ->when($fromDate || $toDate, function ($query) use ($fromDate, $toDate) {
                if ($fromDate) {
                    $query->whereDate('created_at', '>=', Carbon::parse($fromDate));
                }
                if ($toDate) {
                    $query->whereDate('created_at', '<=', Carbon::parse($toDate));
                }
            });
    }

    public static function formatMaintenanceRequests($maintenanceRequests)
    {
        return $maintenanceRequests->map(function ($data) {
            $taskStatusService = new TaskStatusService();
            $approvalData = $taskStatusService->placemenStatus($data, 'maintenance');
            $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
            $message = !empty($approvalData['message']) ? $approvalData['message'] : null;

            return [
                "RequestNumber" => $data->maintenance_request_number ?? '',
                "AssetNumber" => $data->asset_number ?? '',
                "CustomerName" => $data->asset_barcode ?? '',
                "MaintenanceReason" => $data->maintenance_reason ?? '',
                "RequestStatus" => $data->asset_assigned_status ?? '',
                "RepairStatus" => $data->repair_status ?? '',
                "CustomerCode" => $data->outlet_code ?? '',
                "CustomerAddress" => $data->customer_address ?? '',
                "DSRName" => $data->retaileroutlets->salesman_name ?? '',
                "RouteName" => $data->retaileroutlets->route_name ?? '',
                "ChillerType" => $data->chiller_type ?? '',
                "DateTime" => $data->created_at->format("Y-m-d H:i:s"),
                "Status" =>$approvalStatus ?? '',
                'ApprovalMessage' => $message ?? null,
                'PendingAt' => $data->pending_from

            ];
        })->toArray();
    }

    public static function getMaintenanceRequestDetails($request)
    {
        $maintenance = MaintenanceRequest::where(['user_id' => $request['user_id'], 'type' => $request['type'], 'maintenance_request_number' => $request['request_number']])
            ->with(['retaileroutlets:id,route_name,salesman_name'])->first();
        if (!$maintenance) {
            return [];
        }
        $taskStatusService = new TaskStatusService();
        $approvalData = $taskStatusService->placemenStatus($maintenance, 'maintenance');
        $approvalStatus = !empty($approvalData['status']) ? $approvalData['status'] : null;
        $message = !empty($approvalData['message']) ? $approvalData['message'] : null;
        return [
            "RequestNumber" => $maintenance->maintenance_request_number ?? '',
            "DateTime" => $maintenance->created_at->format("Y-m-d H:i:s"),
            "ChillerType" => $maintenance->chiller_type ?? '',
            "RequestStatus" => $maintenance->asset_assigned_status ?? '',
            "RepairStatus" => $maintenance->repair_status ?? '',
            "CurrentVPO" => $maintenance->current_vpo ?? '',
            "AssetNumber" => $maintenance->asset_number ?? '',
            "AssetBarcode" => $maintenance->asset_barcode ?? '',
            "CustomerName" => $maintenance->asset_barcode ?? '',
            "DSRName" => $maintenance->retaileroutlets->salesman_name ?? '',
            "RouteName" => $maintenance->retaileroutlets->route_name ?? '',
            "ContactPerson" => $maintenance->contact_person ?? '',
            "ContactNumber" => $maintenance->contact_number ?? '',
            "ContactEmail" => $maintenance->email ?? '',
            "Signature" => $maintenance->signature ? asset($maintenance->signature) : '',
            "OutletPhoto" => $maintenance->outlet_photo ? asset($maintenance->outlet_photo) : '',
            "RetailerPhoto" => $maintenance->retailer_photo ? asset($maintenance->retailer_photo) : '',
            "ChillerImage" => $maintenance->chiller_image ? asset($maintenance->chiller_image) : '',
            "MobileNumber" => $maintenance->mobile_number ?? '',
            "CustomerAddress" => $maintenance->customer_address ?? '',
            "Pincode" => $maintenance->pincode ?? '',
            "MaintenanceReason" => $maintenance->maintenance_reason ?? '',
            "CustomerLocation" => $maintenance->customer_location ?? '',
            "CurrentLocation" => $maintenance->current_location ?? '',
            "Distance" => $maintenance->distance ?? '',
            "Remarks" => $maintenance->remarks ?? '',
            "Status" => $approvalStatus ?? '',
            'ApprovalMessage' => $message ?? null,
            'PendingAt' => $maintenance->pending_from
        ];
    }
}
