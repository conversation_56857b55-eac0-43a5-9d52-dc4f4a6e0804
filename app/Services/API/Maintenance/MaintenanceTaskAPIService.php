<?php

namespace App\Services\API\Maintenance;

use App\Constants\PlacementApprovalMessage;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\InvoiceChallan;
use App\Models\MaintenanceRequest;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\PlacementRequestService\TaskStatusService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class MaintenanceTaskAPIService
{
    public static function maintenanceTaskRequestList($request, $user)
    {
        $from_Date = $request->input('FromDate') ? self::parseDate($request->input('FromDate')) : '';
        $to_date = $request->input('ToDate') ? self::parseDate($request->input('ToDate')) : '';
        $keyword = $request->input('Keyword');
        $taskStatus = $request->input('task_status') ? strtolower($request->input('task_status')) : 'Pending';
        $offset = $request->input('Offset', 0);
        $limit = $request->input('Limit', 10);
        $query = MaintenanceRequest::where('assigned_organization', $user->user_id)->where('asset_assigned_status', '!=', 'Rejected')
            ->with(['outlet.user', 'approvalHistory', 'maintenancetaskDeploy']);
        if ($from_Date) {
            $query->whereDate('created_at', '>=', Carbon::parse($from_Date));
        }
        if ($to_date) {
            $query->whereDate('created_at', '<=', Carbon::parse($to_date));
        }
        if (!empty($taskStatus)) {
            $query->where('task_status', $taskStatus);
        }

        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->whereHas('outlet.user', function ($query) use ($keyword) {
                    $query->where('name', 'like', '%' . $keyword . '%')
                        ->orWhere('user_id', 'like', '%' . $keyword . '%');
                })
                    ->orWhere('maintenance_request_number', 'like', '%' . $keyword . '%');
            });
        }
        if ($taskStatus === 'completed') {
            // $query->whereHas('maintenancetaskDeploy')->orderBy('updated_at', 'desc');
        } else {
            $query->latest('created_at');
        }
        $assetPlacementList = $query->offset($offset)
            ->limit($limit)->get();
        return self::MaintenanceTaskFormat($assetPlacementList);
    }


    public static function maintenanceTaskRequestDetails($request, $user)
    {
        $maintenance = MaintenanceRequest::where(['assigned_organization' => $user->user_id, 'maintenance_request_number' => $request['RequestNumber']])
            ->with(['outlet.user', 'approvalHistory', 'maintenancetaskDeploy'])->first();
        if (!$maintenance) {
            return [];
        }
        $deploy = $maintenance->maintenancetaskDeploy;
        return [
            'outlet_id' => $maintenance->outlet->id ?? '',
            'outlet_code' => $maintenance->outlet_code ?? '',
            'request_no' => $maintenance->maintenance_request_number,
            'completion_date' => $maintenance->maintenance_complete_date ? date("Y-m-d", strtotime($maintenance->maintenance_complete_date)) : null,
            'status' => $maintenance->task_status,
            'customer_code' => $maintenance->outlet->user->user_id ?? '',
            'customer_name' => $maintenance->outlet->user->name ?? '',
            'asset_number' => $maintenance->asset_number ?? null, //
            'asset_barcode' => $maintenance->asset_barcode ?? null,
            'scanned_barcode' => $deploy->scanned_barcode ?? null,
            'maintenance_reason' => $maintenance->maintenance_reason,
            'contact_person' => $maintenance->contact_person ?? null, //
            'contact_number' => $maintenance->contact_number ?? null, //
            'contact_email' => $maintenance->email ?? null, //
            'outlet_photo' => !empty($deploy->outlet_photo) ? asset($deploy->outlet_photo) : null,
            'retailer_photo' => !empty($deploy->retailer_photo) ? asset($deploy->retailer_photo) : null,
            'chiller_type' => $maintenance->chiller_type,
            "AssetTypeCode" => $maintenance->asset_type_code ?? '',
            'maintained_asset_number' => $deploy->asset_number ?? null, //
            'maintained_asset_barcode' => $deploy->asset_barcode ?? null,
            'chiller_image' => !empty($deploy->outlet_photo) ? asset($deploy->outlet_photo) : null,
            'customer_location' => $maintenance->outlet->user->address_1 ?? '',
            'current_location' => $deploy->current_location ?? '',
            'distance' => $deploy->distance ?? '',
            'remarks' => $deploy->remarks ?? '',
            'latitude' => $maintenance->latitude,
            'longitude' => $maintenance->longitude,
            'asset_maintained' => $deploy->maintenance_asset??'No',
            'customer_email' => $maintenance->outlet->user->email ?? '',
        ];
    }
    private function parseDate($date)
    {
        return $date ? Carbon::createFromFormat('Y-m-d', $date) : null;
    }

    public static function MaintenanceTaskFormat($assetPlacementList)
    {
        $responseData = $assetPlacementList->map(function ($item) {
            $deploy = $item->maintenancetaskDeploy;
            return [
                'outlet_id' => $item->outlet->id ?? '',
                'outlet_code' => $item->outlet_code ?? '',
                'request_no' => $item->maintenance_request_number,
                'customer_name' => $item->outlet->user->name ?? '',
                'customer_code' => $item->outlet->user->user_id ?? '',
                'customer_address' => $item->outlet->user->address_1 ?? '',
                'dsr_name' => $item->outlet->salesman_name,
                'route_name' => $item->outlet->route_name,
                'asset_number' => $item->asset_number ?? null, //
                'asset_barcode' => $item->asset_barcode ?? null,
                'scanned_barcode' => $deploy->scanned_barcode ?? null,
                'chiller_type' => $item->chiller_type,
                "AssetTypeCode" => $item->asset_type_code ?? '',
                'maintenance_reason' => $item->maintenance_reason,
                'completion_date' => $item->maintenance_complete_date ? date("Y-m-d", strtotime($item->maintenance_complete_date)) : null,
                'status' => $item->task_status,
                'repair_status' => $item->repair_status,
                'created_date' => $item->created_at,
                'expected_date' => $item->expected_maintenance_date ?? '',
                'latitude' => $item->latitude,
                'longitude' => $item->longitude,
                'contact_number' => $item->contact_number ?? '',
                'contact_person' => $item->contact_person ?? '',
                'customer_email' => $item->outlet->user->email ?? '',
                'contact_email' => $item->email ?? '',
                'outlet_photo'=> asset($item->retailer_photo),
                'retailer_photo'=> asset($item->retailer_photo),

            ];
        });
        return $responseData ?? [];
    }
}
