<?php

namespace App\Services\API;

use App\Constants\PlacementApprovalMessage;
use App\Models\AssetApprovalPlacementRequestHistory;
use App\Models\AssetPlacementRequest;
use App\Models\InvoiceChallan;
use App\Models\RetailerOutlet;
use App\Models\User;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class AssetRequestService
{

    public static function assetRequestList($request, $user, $model = 'placement')
    {
        $fromDate = $request->input('FromDate');
        $toDate = $request->input('ToDate');
        $searchKeyword = $request->input('Keyword');
        $taskStatus = $request->input('task_status') ? strtolower($request->input('task_status')) : '';
        $offset = $request->input('Offset', 0);
        $limit = $request->input('Limit', 10);
        $modelData = PlacementRequestService::getModelWithRequestNumber($model);
        $modeClass = $modelData['model'];
        $placementStatus = $taskStatus == 'completed' ? "Completed" : "Pending";
        $PulloutFilterDate=  $taskStatus == 'completed' ?'pullout_complete_date':'created_at';
        $PlacementFilterDate=  $taskStatus == 'completed'? 'deployment_date':'created_at';
        $filterDate =  $model =='pullout'?$PulloutFilterDate:$PlacementFilterDate;
        $query = $modeClass::where('assigned_organization', $user->user_id)->where('asset_assigned_status', '!=', 'Rejected')
            ->with(['outlet.user', 'approvalHistory', 'taskDeploy']);
        if ($fromDate) {
            $query->whereDate($filterDate, '>=', Carbon::parse($fromDate));
        }
        if (!empty($taskStatus)) {
            $query->where('task_status', $placementStatus);
        }
        if ($toDate) {
            $query->whereDate($filterDate, '<=', Carbon::parse($toDate));
        }

        if ($searchKeyword) {
            $query->where(function ($query) use ($searchKeyword) {
                $query->whereHas('outlet.user', function ($query) use ($searchKeyword) {
                    $query->where('name', 'like', '%' . $searchKeyword . '%')
                        ->orWhere('user_id', 'like', '%' . $searchKeyword . '%');
                })
                    ->orWhere('pullout_request_number', 'like', '%' . $searchKeyword . '%');
            });
        }
        if ($taskStatus === 'completed') {
            // $query->whereHas('taskDeploy', function ($query) {
            //     $query->latest('completion_date');
            // });
            $query->whereHas('taskDeploy')->orderBy('updated_at', 'desc');
        } else {
            $query->latest('created_at');
        }
//        $assetPlacementList = $query->latest()->offset($offset)
//            ->limit($limit)->get();
        $assetPlacementList = $query->offset($offset)
            ->limit($limit)->get();
        return $assetPlacementList;
    }
}
