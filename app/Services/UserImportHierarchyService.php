<?php

namespace App\Services;

use App\Imports\UsersHierarchyImport;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Validation\ValidationException;
class UserImportHierarchyService
{
    public function importUsers($file)
    {
        try {
            // Excel::import(new UsersImport(), $file);
            Excel::import(new UsersHierarchyImport(), $file, null, \Maatwebsite\Excel\Excel::XLSX);

        } catch (ValidationException $e) {
            // Handle validation errors
            throw $e;
        }
        catch (\Throwable $th) {
            // Log error for failed import
            Log::error('Failed to import users: ' . $th->getMessage());
            // Handle the error as required (e.g., notify admin, show error to user)
            throw $th; // Rethrow the exception to propagate it
        }
    }
}
