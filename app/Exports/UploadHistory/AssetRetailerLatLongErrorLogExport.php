<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetRetailerLatLongErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'RetailerCode',
            'Lat',
            'Long',
            'Address1',
            'Address2',
            'Address3',
            'Pin Code',
            'Error Message'
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['retailercode'] ?? '',
            $errorData['error_data']['lat'] ?? '',
            $errorData['error_data']['long'] ?? '',
            $errorData['error_data']['address1'] ?? '',
            $errorData['error_data']['address2'] ?? '',
            $errorData['error_data']['address3'] ?? '',
            $errorData['error_data']['pin_code'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
