<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetRetailerGSTupdateErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'RetailerCodee',
            'gst',
            'Error Message'
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['retailercode'] ?? '',
            $errorData['error_data']['gst'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
