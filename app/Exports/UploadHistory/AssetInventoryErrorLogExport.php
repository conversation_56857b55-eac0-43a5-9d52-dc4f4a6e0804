<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetInventoryErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'AssetDescription',
            'AssetType',
            'ModelName',
            'Vendor',
            'Quantity',
            'WarehouseCode',
            'ManufacturedYear',
            'Price',
            'Asset Serial Number',
            'Asset Barcode',
            'VPO',
            'Error Message',
        ];
    }

    public function map($errorData): array
    {

        return [
            $errorData['error_data']['description'] ?? '',
            $errorData['error_data']['asset_type'] ?? '',
            $errorData['error_data']['model_name'] ?? '',
            $errorData['error_data']['vendor'] ?? '',
            $errorData['error_data']['quantity'] ?? '',
            $errorData['error_data']['warehouse_code'] ?? '',
            $errorData['error_data']['manufactured_year'] ?? '',
            $errorData['error_data']['asset_price'] ?? '',
            $errorData['error_data']['serial_number'] ?? '',
            $errorData['error_data']['barcode'] ?? '',
            $errorData['error_data']['vpo_target'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
