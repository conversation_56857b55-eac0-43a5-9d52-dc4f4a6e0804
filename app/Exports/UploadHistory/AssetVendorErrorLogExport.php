<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetVendorErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'Region',
            'CFA/Plant Code',
            'CFA Name',
            'Address 1',
            'Address 2',
            'Mobile Number',
            'Email',
            'City',
            'State',
            'Country',
            'Pin Code',
            'Effective From',
            'Effective To',
            'GST No',
            'Error Message',
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['region'] ?? '',
            $errorData['error_data']['cfaplant_code'] ?? '',
            $errorData['error_data']['cfa_name'] ?? '',
            $errorData['error_data']['address_1'] ?? '',
            $errorData['error_data']['address_2'] ?? '',
            $errorData['error_data']['mobile_number'] ?? '',
            $errorData['error_data']['email'] ?? '',
            $errorData['error_data']['city'] ?? '',
            $errorData['error_data']['state'] ?? '',
            $errorData['error_data']['country'] ?? '',
            $errorData['error_data']['pin_code'] ?? '',
            $errorData['error_data']['effective_from'] ?? '',
            $errorData['error_data']['effective_to'] ?? '',
            $errorData['error_data']['gst_no'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
