<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetASMErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'AE Code',
            'RSM Region Code',
            'RSM Code',
            'RTMM Code',
            'ASM Area Code',
            'ASM Name',
            'ASM Code',
            'Email Id',
            'Mobile Number',
            'Error Message'
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['ae_code'] ?? '',
            $errorData['error_data']['rsm_region_code'] ?? '',
            $errorData['error_data']['rsm_code'] ?? '',
            $errorData['error_data']['rtmm_code'] ?? '',
            $errorData['error_data']['asm_area_code'] ?? '',
            $errorData['error_data']['asm_name'] ?? '',
            $errorData['error_data']['asm_code'] ?? '',
            $errorData['error_data']['email_id'] ?? '',
            $errorData['error_data']['mobile_number'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
