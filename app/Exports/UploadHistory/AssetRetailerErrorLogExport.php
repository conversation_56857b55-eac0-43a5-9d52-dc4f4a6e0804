<?php


namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetRetailerErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'Region',
            'AE Code',
            'RSM Code',
            'ASM Code',
            'SO Code',
            'Distributor Code',
            'Distributor Name',
            'Retailer Code',
            'Retailer Name',
            'Salesman Code',
            'Salesman Name',
            'Salesman Emp Code',
            'Route Code',
            'Route Name',
            'Category Code',
            'Category Name',
            'Sub Channel Code',
            'Sub Channel Name',
            'Channel Code',
            'Channel Name',
            'Class Name',
            'PAN No',
            'GST No',
            'IS TCS',
            'Address1',
            'Address2',
            'Address3',
            'Invoice Address',
            'Pin Code',
            'Town',
            'City',
            'State',
            'Latitude',
            'Longitude',
            'Contact Name',
            'Mobile',
            'Email',
            'Aadhar',
            'Credit Limit',
            'Credit Period',
            'Location Code',
            'Location Name',
            'Return Qty',
            'Return Value',
            'Drug License No',
            'Drug License Exp Date',
            'FSSAI No',
            'FSSAI Exp Date',
            'Retailer Created On',
            'Modified Date',
            'Retailer Modified On',
            'Retailer Reference Code',
            'Active',
            'Error Message',
        ];
    }

    public function map($assetErrorLog): array
    {
        $errorData = $assetErrorLog->error_data;

        return [
            $errorData['region'] ?? '',
            $errorData['ae_code'] ?? '',
            $errorData['rsm_code'] ?? '',
            $errorData['asm_code'] ?? '',
            $errorData['so_code'] ?? '',
            $errorData['distributor_code'] ?? '',
            $errorData['distributor_name'] ?? '',
            $errorData['retailercode'] ?? '',
            $errorData['retailername'] ?? '',
            $errorData['salesman_code'] ?? '',
            $errorData['salesman_name'] ?? '',
            $errorData['salesman_emp_code'] ?? '',
            $errorData['route_code'] ?? '',
            $errorData['route_name'] ?? '',
            $errorData['category_code'] ?? '',
            $errorData['category_name'] ?? '',
            $errorData['sub_channel_code'] ?? '',
            $errorData['sub_channel_name'] ?? '',
            $errorData['channel_code'] ?? '',
            $errorData['channel_name'] ?? '',
            $errorData['class_name'] ?? '',
            $errorData['pan_no'] ?? '',
            $errorData['gst_no'] ?? '',
            $errorData['is_tcs'] ?? '',
            $errorData['address1'] ?? '',
            $errorData['address2'] ?? '',
            $errorData['address3'] ?? '',
            $errorData['invoiceaddress'] ?? '',
            $errorData['pin_code'] ?? '',
            $errorData['town'] ?? '',
            $errorData['city'] ?? '',
            $errorData['state'] ?? '',
            $errorData['lat'] ?? '',
            $errorData['long'] ?? '',
            $errorData['contact_name'] ?? '',
            $errorData['mobile'] ?? '',
            $errorData['email'] ?? '',
            $errorData['aadhar'] ?? '',
            $errorData['credit_limit'] ?? '',
            $errorData['credit_period'] ?? '',
            $errorData['location_code'] ?? '',
            $errorData['location_name'] ?? '',
            $errorData['return_qty'] ?? '',
            $errorData['return_value'] ?? '',
            $errorData['drug_license_no'] ?? '',
            $errorData['drug_license_exp_date'] ?? '',
            $errorData['fssai_no'] ?? '',
            $errorData['fssai_exp_date'] ?? '',
            $errorData['retailer_created_on'] ?? '',
            $errorData['modified_date'] ?? '',
            $errorData['retailer_modified_on'] ?? '',
            $errorData['retailer_reference_code'] ?? '',
            $errorData['active'] ?? '',
            $assetErrorLog['error_message'] ?? '',
        ];
    }
}
