<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetRetailerMappingErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'AE Code',
            'RSM Code',
            'ASM Code',
            'SO Code',
            'CFA Code',
            'Distributor Code',
            'Salesman Code',
            'Retailer Code',
            'Customer Address',
            'Current Location',
            'Asset Number',
            'Asset Barcode',
            'Chiller Type Name',
            'VPO',
            'Request Number',
            'Expected Deployment Date',
            'Date of Placement',
            'Assigned Organization',
            'Created By',
            'Created Time',
            'Updated By',
            'Updated Time',
            'Expected VPO',
            'Pincode',
            'Contact Person',
            'Correct Location',
            'Error message'
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['ae_code'] ?? '',                   // AE Code
            $errorData['error_data']['rsm_code'] ?? '',                  // RSM Code
            $errorData['error_data']['asm_code'] ?? '',                  // ASM Code
            $errorData['error_data']['so_code'] ?? '',                   // SO Code
            $errorData['error_data']['cfa_code'] ?? '',                  // CFA Code
            $errorData['error_data']['db_code'] ?? '',                   // Distributor Code
            $errorData['error_data']['dsr_code'] ?? '',                  // Salesman Code
            $errorData['error_data']['outlet_code'] ?? '',               // Retailer Code
            $errorData['error_data']['customer_address'] ?? '',          // Customer Address
            $errorData['error_data']['current_location'] ?? '',          // Current Location
            $errorData['error_data']['asset_serial_number'] ?? '',       // Asset Number
            $errorData['error_data']['asset_barcode'] ?? '',             // Asset Barcode
            $errorData['error_data']['eligible_chiller_type'] ?? '',     // Chiller Type Name
            $errorData['error_data']['vpo'] ?? '',                       // VPO
            $errorData['error_data']['request_number'] ?? '',            // Request Number
            $errorData['error_data']['deployment_date'] ?? '',           // Expected Deployment Date
            $errorData['error_data']['date_of_placement'] ?? '',         // Date of Placement
            isset($errorData['error_data']['cfa_code'])?$errorData['error_data']['cfa_code'] . "_001" : '',         // Assigned Organization
            $errorData['error_data']['created_by'] ?? '',                // Created By
            $errorData['error_data']['created_at'] ?? '',                // Created Time
            $errorData['error_data']['updated_by'] ?? '',                // Updated By
            $errorData['error_data']['updated_at'] ?? '',                // Updated Time
            $errorData['error_data']['expected_vpo'] ?? '',              // Expected VPO
            $errorData['error_data']['pincode'] ?? '',                   // Pincode
            $errorData['error_data']['contact_person'] ?? '',            // Contact Person
            $errorData['error_data']['correct_location'] ?? '',          // Correct Location
            $errorData['error_message'] ?? '',                           // Error message
        ];
    }
}
