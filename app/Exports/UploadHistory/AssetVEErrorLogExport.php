<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetVEErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'Region',
            'Name',
            'User Code',
            'CFA Code',
            'CFA Name',
            'Address 1',
            'Mobile Number',
            'Email Id',
            'City',
            'State',
            'Country',
            'Pin Code',
            'Error Message',
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['region'] ?? '',
            $errorData['error_data']['name'] ?? '',
            $errorData['error_data']['user_code'] ?? '',
            $errorData['error_data']['cfa_code'] ?? '',
            $errorData['error_data']['cfa_name'] ?? '',
            $errorData['error_data']['address_1'] ?? '',
            $errorData['error_data']['mobile_number'] ?? '',
            $errorData['error_data']['email_id'] ?? '',
            $errorData['error_data']['city'] ?? '',
            $errorData['error_data']['state'] ?? '',
            $errorData['error_data']['country'] ?? '',
            $errorData['error_data']['pin_code'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
