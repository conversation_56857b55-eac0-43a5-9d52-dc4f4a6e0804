<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetRSMErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'AE Code',
            'Region Name',
            'RSM/RBDM Code',
            'RSM/RBDM Region Code',
            'RSM Name',
            'RBDM Name',
            'Email Id',
            'Mobile Number',
            'RBDM Email',
            'RBDM Mobile Number',
            'Error Message'
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['ae_code'] ?? '',
            $errorData['error_data']['region_name'] ?? '',
            $errorData['error_data']['rsmrbdm_code'] ?? '',
            $errorData['error_data']['rsmrbdm_region_code'] ?? '',
            $errorData['error_data']['rsm_name'] ?? '',
            $errorData['error_data']['rbdm_name'] ?? '',
            $errorData['error_data']['email_id'] ?? '',
            $errorData['error_data']['mobile_number'] ?? '',
            $errorData['error_data']['rbdm_email'] ?? '',
            $errorData['error_data']['rbdm_mobile_number'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
