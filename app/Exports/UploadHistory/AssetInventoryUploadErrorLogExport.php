<?php

namespace App\Exports\UploadHistory;

use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetInventoryUploadErrorLogExport implements FromCollection, WithHeadings, WithMapping
{
    protected $batchID;

    public function __construct($batchID)
    {
        $this->batchID = $batchID;
    }

    public function collection()
    {
        return UploadHistoryService::getAssetLogDetailsByBatchID($this->batchID);
    }

    public function headings(): array
    {
        return [
            'Region Code',
            'Region Name',
            'RSM Name',
            'RTMM Name',
            'ASM Area Code',
            'ASM Name',
            'SO Teritory Code',
            'SO Name',
            'CHANNEL',
            'Distributor Code',
            'Distributor Name',
            'City',
            'State',
            'CFA Code',
            'CFA Name',
            'Error Message'
        ];
    }

    public function map($errorData): array
    {
        return [
            $errorData['error_data']['region_code'] ?? '',
            $errorData['error_data']['region_name'] ?? '',
            $errorData['error_data']['rsm_name'] ?? '',
            $errorData['error_data']['rtmm_name'] ?? '',
            $errorData['error_data']['asm_area_code'] ?? '',
            $errorData['error_data']['asm_name'] ?? '',
            $errorData['error_data']['so_teritory_code'] ?? '',
            $errorData['error_data']['so_name'] ?? '',
            $errorData['error_data']['channel'] ?? '',
            $errorData['error_data']['distributor_code'] ?? '',
            $errorData['error_data']['distributor_name'] ?? '',
            $errorData['error_data']['city'] ?? '',
            $errorData['error_data']['state'] ?? '',
            $errorData['error_data']['cfa_code'] ?? '',
            $errorData['error_data']['cfa_name'] ?? '',
            $errorData['error_message'] ?? '',
        ];
    }
}
