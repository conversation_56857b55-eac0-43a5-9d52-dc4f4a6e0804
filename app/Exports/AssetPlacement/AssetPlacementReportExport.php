<?php

namespace App\Exports\AssetPlacement;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class AssetPlacementReportExport implements FromCollection, WithHeadings, WithMapping,WithChunkReading
{
    protected $placement;

    public function __construct($placement)
    {
        $this->placement = $placement;
    }

    public function collection()
    {
        return collect($this->placement);
    }

    public function headings(): array
    {
        return [
            'Region',
            'Request Number',
            'Submit Date',
            'Request Status',
            'Request Type',
            'Chiller Type',
            'Expected VPO',
            'Customer Code',
            'Customer Name',
            'RTMM Name',
            'ASM Name',
            'ASM Territory',
            'SO Name',
            'SO Territory',
            'Distributor Name',
            'Distributor Code',
            'DSR Name',
            'DSR Code',
            'CFA',
            'Assigned VE',
            'Channel Code',
            'Outlet Classification',
            'Customer Category',
            'Route Name',
            'Customer City',
            'Address 1',
            'Address 2',
            'Pin Code',
            'RTMM Approval Time',
            'ASM Approval Time',
            'SO Placement Time',
            'Deployment Date',
            'Deployment Status'
        ];
    }

    public function map($asset): array
    {
        return [
            $asset->region ?? '',
            $asset->request_number ?? '',
            $asset->created_at ?? '',
            $asset->taskStatus ?? '',
            $asset->request_type ?? '',
            $asset->eligible_chiller_type ?? '',
            $asset->expected_vpo ?? '',
            $asset->retailer_user_id ?? '',
            $asset->retailer_name ?? '',
            $asset->rsm_name ?? '',
            $asset->asm_name ?? '',
            $asset->asm_area_code ?? '',
            $asset->so_name ?? '',
            $asset->teritory_code ?? '',
            $asset->db_name ?? '',
            $asset->db_code ?? '',
            $asset->dsr_name ?? '',
            $asset->dsr_code ?? '',
            $asset->cfa_code ?? '',
            $asset->assigned_organization ?? '',
            $asset->channel_code ?? '',
            $asset->class_name ?? '',
            $asset->category_name ?? '',
            $asset->route_name ?? '',
            $asset->city ?? '',
            $asset->address_1 ?? '',
            $asset->customer_address ?? '',
            $asset->pin_code ?? '',
            $asset->rsm_approval_time ?? '',
            $asset->asm_approval_time ?? '',
            $asset->created_at ?? '',
            $asset->deployment_date ?? '',
            $asset->deploymentStatus ?? ''
        ];
    }

    public function chunkSize(): int
    {
        return 1000; // Adjust chunk size as needed
    }
}
