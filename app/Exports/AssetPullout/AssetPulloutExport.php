<?php

namespace App\Exports\AssetPullout;

use App\Services\AsssetInventory\AsssetInventoryService;
use App\Services\PlacementRequestService\PlacementRequestService;
use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AssetPulloutExport implements FromCollection, WithHeadings, WithMapping
{
    protected $pullout;

    public function __construct($pullout)
    {
        $this->pullout = $pullout;
    }

    public function collection()
    {
         return collect($this->pullout);
    }

    public function headings(): array
    {
        return [
            'Region',
            'ASM Territory',
            'SO Territory',
            'CFA',
            'Submit Date',
            'ASM Name',
            'SO Name',
            'Request No',
            'Request Status',
            'Asset Serial Number',
            'Asset Barcode',
            'Chiller Type',
            'Deployment Date',
            'Customer Code',
            'Customer Name',
            'Customer City Name',
            'State',
            'Distributor Code',
            'Distributor Name',
            'Route Name',
            'Current VPO',
            'Customer Address',
            'Pin Code',
            'Pullout Date & Time',
            'Pullout Reason'
        ];
    }

    public function map($asset): array
    {
        return [
            $asset->region ?? '',
            $asset->asm_area_code ?? '',
            $asset->teritory_code ?? '',
            $asset->cfa_code ?? '',
            $asset->created_at ?? '',
            $asset->asm_name ?? '',
            $asset->so_name ?? '',
            $asset->request_number ?? '',
            $asset->taskStatus ?? '',
            $asset->serial_number ?? '',
            $asset->barcode ?? '',
            $asset->eligible_chiller_type ?? '',
            $asset->deployment_date ?? '',
            $asset->outlet_code ?? '',
            $asset->retailer_name ?? '',
            $asset->city ?? '',
            $asset->state ?? '',
            $asset->db_code ?? '',
            $asset->db_name ?? '',
            $asset->route_name ?? '',
            $asset->current_vpo ?? '',
            $asset->address_1 ?? '',
            $asset->pin_code ?? '',
            $asset->pullout_date_time ?? '',
            $asset->pullout_reason ?? ''
        ];
    }
}
