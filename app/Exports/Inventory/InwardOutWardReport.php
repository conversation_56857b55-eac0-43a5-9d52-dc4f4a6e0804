<?php

namespace App\Exports\Inventory;

use App\Services\AsssetInventory\AsssetInventoryService;
use App\Services\UploadHistoryDetails\UploadHistoryService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class InwardOutWardReport implements FromCollection, WithHeadings, WithMapping
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $data = AsssetInventoryService::getInwardOutwardInventory($this->request, 'download');
        return collect($data['inwardOutwardInventory']);
    }

    public function headings(): array
    {
        $heading = [
            'AssetDescription',
            'AssetType',
            'CFA Code',
            'CFA Name',
            'Opening Inventory',
            'Stock In',
            'Stock Out',
            'Closing Inventory',
        ];
        if ($this->request['inward_outward_details'] == 'details') {
            $heading[] = 'Asset Serial Number';
        }
        return $heading;
    }

    public function map($inventory): array
    {
        $cfaName = $inventory->cfa->name ?? '';
        $mappedData = [
            $inventory->description ?? '',
            $inventory->asset_type ?? '',
            $inventory->warehouse_code ?? '',
            $cfaName,
            $inventory->opening_inventory ?? 0,
            $inventory->stock_in ?? 0,
            $inventory->stock_out ?? 0,
            $inventory->closing_inventory ?? 0,
        ];
        if ($this->request['inward_outward_details'] == 'details') {
            $mappedData[] = $inventory->serial_number ?? '';
        }
        return $mappedData;
    }
}
