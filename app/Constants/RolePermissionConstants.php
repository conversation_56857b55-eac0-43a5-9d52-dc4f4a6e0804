<?php

namespace App\Constants;

class RolePermissionConstants
{
    // Define constants for roles
    const ROLES = [
        'SUPERADMIN', 'ADMI<PERSON>',  'RETA<PERSON><PERSON>', 'ME<PERSON><PERSON><PERSON><PERSON><PERSON>', 'DIS<PERSON>IBUT<PERSON>', 'DSR', 'ISR', 'Merchaniser', 'SO', 'ASM', 'RBDM_GTMS', 'RSM', 'AE', 'VENDOR', 'VE', 'CFA', 'writer'
    ];


    const HIERARCHY = [
        'RETAILER', 'MERC<PERSON><PERSON>ISER', 'DISTRIBUTOR', 'DSR', 'ISR', 'Merchaniser', 'SO', 'ASM', 'RBDM_GTMS', 'RSM', 'AE', 'VENDOR', 'VE', 'CFA', 'SuperAdmin', 'Admin'
    ];

    const UserIDFormat = [
        "SUPERADMIN" => ["prefix" => "MARS_", "length" => null],
        "ADMIN" => ["prefix" => "ADMIN_", "length" => null],
        "RETAILER" => ["prefix" => "RETN_", "length" => 6],
        "MERCHANDISER" => ["prefix" => "M_", "length" => 10],
        "DISTRIBUTOR" => ["prefix" => "", "length" => 10],
        "DSR" => ["prefix" => "DSR_", "length" => null],
        "ISR" => ["prefix" => "DSR_", "length" => null],
        "Merchaniser" => ["prefix" => "", "length" => null],
        "SO" => ["prefix" => "", "length" => 8],
        "ASM" => ["prefix" => "ASM_", "length" => null],
        "RBDM_GTMS" => ["prefix" => "RBDM_", "length" => null],
        "RSM" => ["prefix" => "RSM_", "length" => null],
        "AE" => ["prefix" => "AE_", "length" => null],
        "VENDOR" => ["prefix" => "", "length" => null],
        "VE" => ["prefix" => "", "length" => null],
        "CFA" => ["prefix" => "", "length" => 6],
    ];


    // Define constants for permissions
    const PERMISSIONS = [
        'Post access', 'Post edit', 'Post create', 'Post delete',
        'Role access', 'Role edit', 'Role create', 'Role delete',
        'User access', 'User edit', 'User create', 'User delete',
        'Permission access', 'Permission edit', 'Permission create', 'Permission delete',
        'Mail access', 'Mail edit',
        'AssetTransferAccess', 'AssetTransferEditAccess', 'AssetTransferCreateAccess', 'AssetTransferDeleteAccess',
        'AssetMaintenanceAccess', 'AssetMaintenanceEditAccess', 'AssetMaintenanceCreateAccess', 'AssetMaintenanceDeleteAccess',
        'AssetPulloutAccess', 'AssetPulloutEditAccess', 'AssetPulloutCreateAccess', 'AssetPulloutDeleteAccess',
        'AssetReplacementAccess', 'AssetReplacementEditAccess', 'AssetReplacementCreateAccess', 'AssetReplacementDeleteAccess',
        'AssetPlacementAccess', 'AssetPlacementEditAccess', 'AssetPlacementCreateAccess', 'AssetPlacementDeleteAccess',
        'AssetAuditAccess', 'AssetAuditEditAccess', 'AssetAuditCreateAccess', 'AssetAuditDeleteAccess'
    ];

    const UPLOADFORMAT = [
        'RETAILER' => 'UploadDataFormat/CustomerRetailer.xlsx',
        'DISTRIBUTOR' => 'UploadDataFormat/DBCreation.xlsx',
        'DSR' => 'UploadDataFormat/DSR.xlsx',
        'SO' => 'UploadDataFormat/SOCreation.xlsx',
        'ASM' => 'UploadDataFormat/ASMCreation.xlsx',
        'RSM' => 'UploadDataFormat/RTMM.xlsx',
        // 'AE' => 'UploadDataFormat/SOCreation.xlsx',
        'VENDOR' => 'UploadDataFormat/VendoreCreation.xlsx',
        'VE' => 'UploadDataFormat/VEUserCreation.xlsx',
        'CFA' => 'UploadDataFormat/VendoreCreation.xlsx',
        'db_to_cfa_mapping' => 'UploadDataFormat/CFAtoDistributorMapping.xlsx',
        'retailer_lat_long' => 'UploadDataFormat/RetailerLatLongUpdate.xlsx',//
        'retailer_gst_update' => 'UploadDataFormat/RetailerGSTUpdate.xlsx',//
        'asset_retailer_mapping' => 'UploadDataFormat/AssetMappingSheet.xlsx',
        'asset_inventory_upload' => 'UploadDataFormat/AssetInventoryUploader.xlsx',
    ];



}
