

## Laravel + Tailwind Css  Project with <PERSON><PERSON><PERSON><PERSON> (Admin and Front-end)
- create users 
- create roles and permissions
- assign roles and permissions to user



## How to run the code
- cp .env.example `.env`
- open .env and update DB_DATABASE (database details)
- run : `composer install`
- run : `php artisan key:generate`
- run : `php artisan migrate:fresh --seed`
- run : `php artisan serve`
- run : `php artisan db:seed --class=Database\\Seeders\\AdditionalDataSeeder`
