# User Import Guide - Large Data Files

## 📋 Overview

This guide explains how to import large data files (up to 500,000+ records) efficiently using the optimized import system.

## 🚀 Getting Started

### Supported File Types
- **Excel Files**: `.xlsx`, `.xls`
- **CSV Files**: `.csv`
- **Maximum Size**: 500MB per file
- **Maximum Records**: 500,000+ records

### File Preparation Tips
1. **Clean Data**: Remove empty rows and columns
2. **Consistent Headers**: Use exact column names as specified
3. **Valid Data**: Ensure all required fields are filled
4. **File Format**: Save Excel files in `.xlsx` format for best performance

## 📊 Import Process Types

### Small Files (< 10,000 records)
- **Processing**: Immediate (real-time)
- **Time**: 2-5 seconds
- **Experience**: Instant results with progress bar

### Medium Files (10,000 - 50,000 records)  
- **Processing**: Optimized chunks
- **Time**: 30 seconds - 2 minutes
- **Experience**: Progress tracking with live updates

### Large Files (50,000 - 200,000 records)
- **Processing**: Background processing
- **Time**: 5-15 minutes  
- **Experience**: Upload confirmation, check status in upload history

### Very Large Files (200,000+ records)
- **Processing**: Advanced background processing
- **Time**: 30-60 minutes
- **Experience**: Batch tracking with detailed progress monitoring

## 🔄 Step-by-Step Import Process

### 1. Access Import Page
- Navigate to **Admin → Users → Create**
- Select your **User Role** from dropdown
- Click **Upload File** button

### 2. File Upload
- Click **Choose File** and select your data file
- System automatically detects file size and record count
- Click **Upload** to start the import process

### 3. Processing Feedback

#### **Small/Medium Files**
- Real-time progress bar
- Immediate success/error feedback
- Results displayed instantly

#### **Large Files**
- Upload confirmation message
- Batch ID provided for tracking
- Redirected to upload history page

### 4. Monitor Progress
- Check **Latest Upload History** section
- View real-time status updates
- Track success/failed record counts

### 5. Handle Results

#### **Successful Import**
- All records processed successfully
- Status shows "Completed"
- No further action needed

#### **Partial Success**
- Some records failed validation
- Download failed records for correction
- Fix issues and re-upload failed records

## 📈 Understanding Upload History

### Status Indicators
- **Processing**: Import is currently running
- **Completed**: All records processed successfully  
- **Partial**: Some records failed (download available)
- **Failed**: Import encountered critical errors

### Data Columns
- **Batch ID**: Unique identifier for your import
- **Upload Date & Time**: When the import was started
- **Total Records**: Number of records in your file
- **Incorrect Records**: Number of records that failed validation
- **Success Records**: Number of successfully processed records
- **Upload Type**: User role being imported

## 📥 Downloading Failed Records

### When to Download
- When "Incorrect Records" count > 0
- Status shows "Partial" completion
- You need to fix validation errors

### Download Process
1. Click **Download** button in the Action column
2. Excel file will be generated with failed records
3. File includes original data + error messages
4. Fix the issues in the downloaded file
5. Re-upload the corrected file

### Understanding Error Messages
- **Validation Errors**: Missing required fields, invalid formats
- **Hierarchy Errors**: User hierarchy relationships not found
- **Duplicate Errors**: Records already exist in system
- **Format Errors**: Incorrect data types or formats

## ⚡ Performance Tips

### File Optimization
1. **Remove Empty Rows**: Delete any blank rows in your file
2. **Consistent Data**: Ensure all data follows the same format
3. **Split Large Files**: Consider splitting 500k+ record files
4. **Use Excel Format**: `.xlsx` files process faster than `.csv`

### Best Upload Times
- **Off-Peak Hours**: Upload during low system usage
- **Stable Connection**: Ensure stable internet connection
- **Avoid Interruptions**: Don't close browser during large uploads

### Preparation Checklist
- [ ] Data cleaned and validated
- [ ] Required fields completed
- [ ] File size under 500MB
- [ ] Stable internet connection
- [ ] Sufficient time for processing

## 🚨 Troubleshooting

### Common Issues

#### **Upload Fails Immediately**
- **Cause**: File too large or invalid format
- **Solution**: Check file size and format, try splitting the file

#### **Processing Stuck**
- **Cause**: System overload or network issues
- **Solution**: Wait 10-15 minutes, refresh page, contact support if needed

#### **High Failure Rate**
- **Cause**: Data validation issues
- **Solution**: Download failed records, fix errors, re-upload

#### **Timeout Errors**
- **Cause**: File too large for direct processing
- **Solution**: System automatically switches to background processing

### Getting Help
1. **Check Upload History**: Review error messages in downloaded files
2. **Contact Support**: Provide Batch ID for specific assistance
3. **Documentation**: Refer to field requirements and formats
4. **Sample Files**: Use provided templates for correct format

## 📋 Data Requirements by User Type

### RETAILER
- **Required Fields**: Retailer code, name, distributor mapping
- **Validation**: Hierarchy relationships, GST format, contact details
- **Common Errors**: Missing distributor codes, invalid mobile numbers

### DISTRIBUTOR  
- **Required Fields**: Distributor code, name, territory mapping
- **Validation**: Territory assignments, contact information
- **Common Errors**: Duplicate codes, missing territory data

### RSM/ASM/SO/DSR
- **Required Fields**: Employee codes, hierarchy relationships
- **Validation**: Reporting structure, territory assignments
- **Common Errors**: Broken hierarchy chains, duplicate employee codes

### VENDOR/VE
- **Required Fields**: Vendor codes, business details
- **Validation**: Business registration, contact information
- **Common Errors**: Invalid GST numbers, missing addresses

## 📞 Support Information

### For Technical Issues
- **Email**: <EMAIL>
- **Phone**: +91-XXXXXXXXXX
- **Include**: Batch ID, error messages, file details

### For Data Questions
- **Documentation**: Refer to field requirement guides
- **Templates**: Download sample files for reference
- **Training**: Contact admin for data format training

---

**Remember**: The system is optimized to handle large files efficiently. For files over 50,000 records, be patient as background processing ensures system stability and data integrity.
