# Large Data Import - Quick Reference Guide

## 🚀 Quick Start

### File Size Thresholds
```php
< 50MB or < 100k records  → Direct Processing
50-100MB or 100k-200k     → Background Queue (ProcessUploaderImport)
> 100MB or > 200k records → Large File Queue (ProcessLargeFileImport)
```

### Processing Times
- **10k records**: 30 seconds - 1 minute
- **50k records**: 2-5 minutes  
- **100k records**: 5-10 minutes
- **300k records**: 30-45 minutes
- **500k+ records**: 1-2 hours

## 🔧 Key Components

### 1. Main Import Class
**File**: `app/Imports/UserMasterImport.php`
```php
// Dynamic chunk sizing
public function chunkSize(): int
{
    return $this->getOptimalChunkSize($this->totalRows);
}

// Cached validation
private function isValidRowCached($row, $userValidationService)
{
    // Caches validation results to avoid repeated processing
}
```

### 2. Controller Logic
**File**: `app/Http/Controllers/Admin/UserImportController.php`
```php
// File size detection and strategy selection
if ($fileSize > 100 * 1024 * 1024 || $estimatedRows > 200000) {
    ProcessLargeFileImport::dispatch($filePath, $userRole, $batchID);
} elseif ($fileSize > 50 * 1024 * 1024 || $estimatedRows > 100000) {
    ProcessUploaderImport::dispatch($filePath, $userRole, $batchID);
} else {
    Excel::import(new UserMasterImport($userRole, $batchID), $filePath);
}
```

### 3. Background Jobs
**Files**: 
- `app/Jobs/ProcessUploaderImport.php`
- `app/Jobs/ProcessLargeFileImport.php`

## 📊 Chunk Size Strategy

### Role-Based Chunk Sizes
```php
// Simple roles (fewer validations)
'VENDOR', 'VE' → 3,000 records per chunk

// Complex roles (more validations)  
'RETAILER', 'DISTRIBUTOR', 'RSM', 'ASM', 'SO', 'DSR' → 1,000 records per chunk
```

### Memory-Based Adjustment
```php
private function getOptimalChunkSize($totalRows)
{
    $memoryLimit = $this->getMemoryLimitInBytes();
    $availableMemory = $memoryLimit - memory_get_usage();
    
    // Calculate optimal chunk size based on available memory
    $optimalSize = min($baseChunkSize, floor($availableMemory / $estimatedMemoryPerRow));
    
    return max(100, $optimalSize); // Minimum 100 records per chunk
}
```

## 🛠️ Configuration

### PHP Settings
```ini
memory_limit = 2G
max_execution_time = 3600
upload_max_filesize = 500M
post_max_size = 500M
```

### Laravel Queue
```env
QUEUE_CONNECTION=database
QUEUE_FAILED_DRIVER=database
```

### MySQL Optimization
```sql
-- Increase sort buffer for large queries
SET SESSION sort_buffer_size = 2097152; -- 2MB
```

## 🔍 Monitoring & Debugging

### Log Locations
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Import-specific logs
grep "Starting import\|Chunk.*completed\|Import completed" storage/logs/laravel.log
```

### Key Metrics to Monitor
```php
// Performance metrics logged:
- Processing time per chunk
- Memory usage patterns  
- Success/failure rates
- Queue processing times
- Validation cache hit rates
```

### Debug Commands
```bash
# Check queue status
php artisan queue:work --verbose

# Monitor failed jobs
php artisan queue:failed

# Clear caches
php artisan cache:clear
php artisan config:clear
```

## ⚠️ Common Issues & Solutions

### 1. Memory Errors
**Error**: `Fatal error: Allowed memory size exhausted`
**Solution**: 
- Increase PHP memory limit
- Reduce chunk size
- Check for memory leaks in validation logic

### 2. Timeout Issues  
**Error**: `Maximum execution time exceeded`
**Solution**:
- Use background queue processing
- Increase `max_execution_time`
- Optimize validation queries

### 3. Database Errors
**Error**: `SQLSTATE[HY001]: Memory allocation error`
**Solution**:
- Optimize queries with proper indexes
- Increase MySQL `sort_buffer_size`
- Limit relationship loading

### 4. Queue Processing Issues
**Error**: Jobs stuck in queue
**Solution**:
```bash
# Restart queue workers
php artisan queue:restart

# Process failed jobs
php artisan queue:retry all
```

## 📈 Performance Optimization Tips

### 1. Database Optimization
- Add indexes on frequently queried fields (`batch_id`, `batchID`)
- Use `exists()` instead of `count()` for existence checks
- Limit relationship loading with `with()` constraints

### 2. Memory Management
- Use `unset()` to free memory after processing chunks
- Avoid loading large relationships unnecessarily
- Implement garbage collection between chunks

### 3. Validation Optimization
- Cache validation results for similar records
- Use database queries instead of in-memory filtering
- Optimize regex patterns and validation rules

## 🔄 Deployment Checklist

### Before Deployment
- [ ] Test with sample large files (100k+ records)
- [ ] Verify queue workers are running
- [ ] Check PHP memory and execution time limits
- [ ] Ensure database has sufficient resources
- [ ] Test error handling and recovery

### After Deployment
- [ ] Monitor initial large imports
- [ ] Check log files for errors
- [ ] Verify queue processing efficiency
- [ ] Monitor system resource usage
- [ ] Test download functionality

## 📞 Support & Maintenance

### Regular Maintenance Tasks
- Clean up old upload history records
- Monitor and rotate log files
- Check queue worker health
- Review performance metrics
- Update chunk sizes based on usage patterns

### Emergency Procedures
1. **High Memory Usage**: Reduce chunk sizes temporarily
2. **Queue Backup**: Add more queue workers
3. **Database Issues**: Check for locks and optimize queries
4. **File Processing Failures**: Check file format and permissions

---

**For detailed technical documentation, see: `docs/LARGE_DATA_IMPORT_PROCESS.md`**
