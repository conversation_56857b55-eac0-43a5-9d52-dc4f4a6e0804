<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Asset Management') }}</title>

    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <meta name="robots" content="noindex, nofollow">
    <title> admin </title>

    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('img/favicon-mars.png') }}">
    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/daterangepicker.css') }}">
    <link rel="stylesheet" href="{{ asset('css/animate.css') }}">
    <link rel="stylesheet" href="{{ asset('plugins/select2/css/select2.min.css') }}">

    <link rel="stylesheet" href="{{ asset('css/dataTables.bootstrap4.min.css') }}">
    <link rel="stylesheet" href="{{ asset('plugins/fontawesome/css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ asset('plugins/fontawesome/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/semantic.min.css') }}">


    <link rel="stylesheet" href="{{ asset('css/style.css') }}">
    <script src="{{ asset('js/jquery-3.6.0.min.js') }}"></script>

    <style>
        .validatin-error {
            color: red;
            font-weight: 400
        }

        .loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            /* Semi-transparent black */
            z-index: 9999;
            /* Ensure it appears above other elements */
            display: none;
            /* Initially hidden */
        }

        .loader {
            border: 6px solid #f3f3f3;
            /* Light grey */
            border-top: 6px solid #3498db;
            /* Blue */
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            /* Ensure it appears on top of overlay */
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>

    @include('layouts.sidebar')

    @include('layouts.header')

    {{--  @if (\Session::has('success'))
        <div class="text-green-600 pt-5 pl-5">
            <ul>
                <li>{!! \Session::get('success') !!}</li>
            </ul>
        </div>
    @endif

    @if (\Session::has('error'))
        <div class="text-green-600 pt-5 pl-5">
            <ul>
                <li>{!! \Session::get('error') !!}</li>
            </ul>
        </div>
    @endif  --}}

    {{--  @if ($errors->any())
        <div class="text-red-600  pt-5 pl-5">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif  --}}
    <div id="loader-overlay" class="loader-overlay">
        <div id="loader" class="loader"></div>
    </div>

    {{ $slot }}
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function success_message(message) {
            return `<div class="alert alert-success" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline">${message}!</span>
            </div>`;
        }

        function error_message(message) {
            return `<div class="alert alert-danger" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">${message}.</span>
            </div>`;
        }
        // Show loader and overlay
        function showLoader() {
            document.getElementById('loader-overlay').style.display = 'block';
        }

        // Hide loader and overlay
        function hideLoader() {
            document.getElementById('loader-overlay').style.display = 'none';
        }

        // Show loader and overlay
        // function showLoader() {
        //     document.getElementById('loader-overlay').style.display = 'block';
        // }

        // // Hide loader and overlay
        // function hideLoader() {
        //     document.getElementById('loader-overlay').style.display = 'none';
        // }
    </script>
    <script>
        function handleFormSubmission(formId, successHandler, errorHandler) {
            $(formId).on('submit', function(event) {
                event.preventDefault();
                showLoader();
                $('#ajax_message').html('');
                $('.validatin-error').remove();

                var formData = new FormData(this);
                var inputFile = $(this).find('#user_import_file')[0].files[0];

                if (inputFile) {
                    formData.append('user_import_file', inputFile);
                }

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        hideLoader();
                        if (successHandler) {
                            successHandler(response);

                        }
                    },
                    error: function(xhr, status, error) {
                        hideLoader();
                        if (errorHandler) {
                            errorHandler(xhr);
                        }
                    }
                });
            });
        }

        function defaultSuccessHandler(response) {
            if (response.success) {
                $('#ajax_message').html('Success: ' + success_message(response.success));
                $('#user_import_form')[0].reset();
                setTimeout(function() {
                    if(response.redirectRoute){
                        window.location.href = response.redirectRoute;
                    }else{
                        window.location.href = "{{ route('admin.users.index') }}";
                    }

                }, 2000); // 2000 milliseconds = 2 seconds
            } else {
                $('#ajax_message').html(error_message(response.error));

            }
        }

        function defaultErrorHandler(xhr) {
            hideLoader();
            if (xhr.status === 419) {
                // CSRF token mismatch detected, redirect user to login page
                window.location.href = "{{ route('admin.login') }}";
            }
            else if (xhr.status === 422 && xhr.responseJSON.error === "empty_file") {
                $('#ajax_message').html(error_message(xhr.responseJSON.message));
            } else if (xhr.status === 422 && xhr.responseJSON.errors) {
                var errors = xhr.responseJSON.errors;
                $('.validatin-error').remove();
                for (var key in errors) {
                    if (errors.hasOwnProperty(key)) {
                        var errorMessage = '<div class="validatin-error">' + error_message(errors[key][0]) + '</div>';
                        $('[name="' + key + '"]').after(errorMessage);
                    }
                }
            } else {
                $('#ajax_message').html('Error: ' + error_message(xhr.responseJSON.message));
            }
        }

        // Usage:
        $(document).ready(function() {
            handleFormSubmission('#user_import_form', defaultSuccessHandler, defaultErrorHandler);
        });
    </script>


    <script src="{{ asset('js/feather.min.js') }}"></script>
    <script src="{{ asset('js/jquery.slimscroll.min.js') }}"></script>
    <script src="{{ asset('js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('plugins/apexchart/apexcharts.min.js') }}"></script>
    <script src="{{ asset('plugins/apexchart/chart-data.js') }}"></script>

    <script src="{{ asset('plugins/select2/js/select2.min.js') }}"></script>
    <script src="{{ asset('plugins/sweetalert/sweetalert2.all.min.js') }}"></script>
    <script src="{{ asset('plugins/sweetalert/sweetalerts.min.js') }}"></script>

    <script src="{{ asset('js/moment.min.js') }}"></script>
    <script src="{{ asset('js/daterangepicker.js') }}"></script>
    <script src="{{ asset('js/semantic.min.js') }}"></script>

    <script src="{{ asset('js/script.js') }}"></script>

</body>

</html>
