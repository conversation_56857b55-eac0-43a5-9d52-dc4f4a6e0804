@php
    $user_type = $user->user_type;
@endphp
<button class="btn btn-success mx-1 status_filter" data-status="All">All</button>
<button class="btn btn-secondary mx-1 status_filter" data-status="Pending">Pending</button>
@if ($user->user_type != 'VENDOR')
    <button class="btn btn-primary mx-2 status_filter" data-status="Approved">Other Approval
        Status
    </button>
    <button class="btn btn-danger mx-2 status_filter" data-status="Rejected">Reject</button>
@endif

@if ($user->user_type == 'VENDOR')
    <button class="btn btn-danger mx-2 status_filter" data-status="taskallocation">VE
        Placement Task Allocation
    </button>
    <button class="btn btn-danger mx-2 status_filter" data-status="Rejected">Reject</button>
@endif
<a id="resetButton" class="btn btn-warning py-2">Reset</a>
{{--<a class="btn btn-warning py-2" href="{{ route('approval_center.placement', ['status' => 'All']) }}">Reset</a>--}}
<script>
    document.addEventListener('DOMContentLoaded', (event) => {
        const resetButton = document.getElementById('resetButton');

        const currentPath = window.location.pathname;

        const url = new URL(window.location.origin + currentPath);
        url.searchParams.set('status', 'All');

        resetButton.href = url.toString();
    });

</script>
