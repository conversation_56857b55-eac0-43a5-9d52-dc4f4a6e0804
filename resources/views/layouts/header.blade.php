 <!--<header class="flex justify-between items-center py-4 px-6 bg-white border-b-4 border-indigo-600">
     <div class="flex items-center">
         <button @click="sidebarOpen = true" class="text-gray-500 focus:outline-none lg:hidden">
             <svg class="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                 <path d="M4 6H20M4 12H20M4 18H11" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                     stroke-linejoin="round"></path>
             </svg>
         </button>

         <div class="relative mx-4 lg:mx-0">
             <span class="absolute inset-y-0 left-0 pl-3 flex items-center">
                 <svg class="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none">
                     <path
                         d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z"
                         stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                     </path>
                 </svg>
             </span>

             <input class="form-input w-32 sm:w-64 rounded-md pl-10 pr-4 focus:border-indigo-600" type="text"
                 placeholder="Search">
         </div>
     </div>

     <div class="flex items-center">
         <div x-data="{ dropdownOpen: false }" class="relative">
             <button @click="dropdownOpen = ! dropdownOpen"
                 class="relative block h-8 w-8 rounded-full overflow-hidden shadow focus:outline-none">
                 <img class="h-full w-full object-cover" src="/images/{{ auth()->user()->profile }}" alt="Your avatar">
             </button>

             <div x-show="dropdownOpen" @click="dropdownOpen = false" class="fixed inset-0 h-full w-full z-10"
                 style="display: none;"></div>

             <div x-show="dropdownOpen"
                 class="absolute right-0 mt-2 w-48 bg-white rounded-md overflow-hidden shadow-xl z-10"
                 style="display: none;">
                 <a href="{{ route('admin.profile') }}"
                     class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-600 hover:text-white">Profile</a>

                 <form method="POST" action="{{ route('admin.logout') }}">
                     @csrf
                     <a href="{{ route('admin.logout') }}" onclick="event.preventDefault();
                                                this.closest('form').submit();"
                         class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-600 hover:text-white">Logout</a>
                 </form>
             </div>
         </div>
     </div>
 </header> -->
 {{--  <div class="overlay-page-loder">
     <div class="overlayDoor"></div>
     <div class="overlayContent">
         <div class="main_loader">
             <div class="inner"></div>
         </div>
     </div>
 </div>  --}}
 <!-- PAGE loder END -->

 <div class="main-wrapper">

     <!-- Main Wrapper -->
     <div class="main-wrapper">

         <!-- Header -->
         <div class="header">

             <!-- Logo -->
             <div class="header-left active">
                 <a href="{{ route('admin.dashboard') }}" class="logo logo-normal">
                     <img src="{{ asset('img/loginImg/logo-main.jpg') }}" alt="">
                 </a>
                 <a href="{{ route('admin.dashboard') }}" class="logo-small">
                     <img src="{{ asset('img/favicon-mars.png') }}" alt="">
                 </a>
                 <a id="toggle_btn" href="javascript:void(0);">
                     <i data-feather="chevrons-left" class="feather-16"></i>
                 </a>
             </div>
             <!-- /Logo -->

             <a id="mobile_btn" class="mobile_btn" href="#sidebar">
                 <span class="bar-icon">
                     <span></span>
                     <span></span>
                     <span></span>
                 </span>
             </a>
             <!-- Header Menu -->
             <ul class="nav user-menu">
                 <!-- Search -->
                 <li class="nav-item nav-searchinputs">
                     <div class="top-nav-search">
                         <a href="javascript:void(0);" class="responsive-search">
                             <i class="fa fa-search"></i>
                         </a>
                         <form action="#">
                             <div class="searchinputs">
                                 <input type="text" placeholder="Search">
                                 <div class="search-addon">
                                     <span><i data-feather="search" class="feather-14"></i></span>
                                 </div>
                             </div>
                         </form>
                     </div>
                 </li>

                 <li class="nav-item dropdown has-arrow main-drop">
                     <a href="javascript:void(0);" class="dropdown-toggle nav-link userset" data-bs-toggle="dropdown">
                         <span class="user-info">
                             <span class="user-letter">

                                 <img src="{{ asset('img/profiles/circle-user.png') }}" alt=""
                                     class="img-fluid">
                             </span>
                             <span class="user-detail">
                                 <span class="user-name">{{ Auth::user()->name }}</span>
                                 <span class="user-role">{{ Auth::user()->user_type }}</span>
                             </span>
                         </span>
                     </a>
                     <div class="dropdown-menu menu-drop-user">
                         <div class="profilename">
                             {{--  <div class="profileset">
                                 <span class="user-img"><img src="{{ asset('img/profiles/avator1.jpg')}}" alt="">
                                     <span class="status online"></span></span>
                                 <div class="profilesets">
                                     <h6>{{ Auth::user()->name }}</h6>
                                     <h5>{{ Auth::user()->user_type }}</h5>
                                 </div>
                             </div>  --}}
                             <hr class="m-0">
                             {{--  <a class="dropdown-item" href="{{ route('admin.profile') }}"> <i class="me-2"
                                     data-feather="user"></i> My
                                 Profile</a>  --}}

                             <hr class="m-0">
                             <form method="POST" action="{{ route('admin.logout') }}">
                                 @csrf
                                 <a class="dropdown-item logout pb-0" href="{{ route('admin.logout') }}"
                                     onclick="event.preventDefault();
                                                this.closest('form').submit();">
                                     <img src="{{ asset('img/icons/log-out.svg') }}" class="me-2"
                                         alt="img">Logout</a>

                                 <!-- <a href="{{ route('admin.logout') }}" onclick="event.preventDefault();
                                                this.closest('form').submit();"
                                     class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-600 hover:text-white">Logout</a> -->
                             </form>

                         </div>
                     </div>
                 </li>
             </ul>
             <!-- /Header Menu -->

             <!-- Mobile Menu -->
             <div class="dropdown mobile-user-menu">
                 <a href="javascript:void(0);" class="nav-link dropdown-toggle" data-bs-toggle="dropdown"
                     aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                 <div class="dropdown-menu dropdown-menu-right">
                     <a class="dropdown-item" href="profile.php">My Profile</a>
                     <a class="dropdown-item" href="signin.php">Logout</a>
                 </div>
             </div>
             <!-- /Mobile Menu -->
         </div>
         <!-- Header -->
