@php
    $user_type = $user->user_type;
@endphp
<div class="card" id="filter_inputs">
    <div class="card-body pb-0">
        <form action="" method="get">
            <div class="row">
                <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                    <div class="form-group">
                        <div class="field">
                            <label>Submit From Date</label>
                            <div class="ui calendar w-100" id="rangestart">
                                <div class="ui input left icon w-100">
                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                    <input type="text" placeholder="Submit From Date" name="FromDate" id="FromDate"
                                        value="{{ request()->query('FromDate') }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                    <div class="form-group">
                        <div class="field">
                            <label>Submit To Date</label>
                            <div class="ui calendar w-100" id="rangeend">
                                <div class="ui input left icon w-100">
                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                    <input type="text" placeholder="Submit To Date" name="ToDate" id="ToDate"
                                        value="{{ request()->query('ToDate') }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>Request Number</label>
                        <select class="form-select select" name="request_no">
                            <option value="">Select Request</option>
                            @foreach ($placement_request_list as $request)
                                <option value="{{ $request }}" @if (request()->query('request_no') == $request) selected @endif>
                                    {{ $request }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>DB Code</label>
                        @if (!empty($db_code_list))
                            <select class="form-select select" name="db_code">
                                <option value="">Select DB Code</option>
                                @foreach ($db_code_list as $name)
                                    <option value="{{ $name }}"
                                        @if (request()->query('db_code') == $name) selected @endif>
                                        {{ $name }}</option>
                                @endforeach
                            </select>
                        @else
                            @php
                                $db_code = request()->query('db_code');
                            @endphp
                            <input type="text" class="form-control" name="db_code" placeholder="Enter DB Code:"
                                value="{{ $db_code ?? '' }}">

                        @endif

                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>DB Name</label>
                        @if (!empty($db_name_list))
                            <select class="form-select select" name="distributor_name">
                                <option value="">Select DB Name</option>
                                @foreach ($db_name_list as $name)
                                    <option value="{{ $name }}"
                                        @if (request()->query('distributor_name') == $name) selected @endif>
                                        {{ $name }}</option>
                                @endforeach
                            </select>
                        @else
                            @php
                                $distributor_name = request()->query('distributor_name');
                            @endphp
                            <input type="text" class="form-control" name="distributor_name"
                                placeholder="Enter DB Name:" value="{{ $distributor_name ?? '' }}">

                        @endif

                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>Customer Code</label>
                        @php
                            $customerCode = request()->query('customer_code');
                        @endphp
                        <input type="text" class="form-control" name="customer_code"
                            placeholder="Enter Customer Code:" value="{{ $customerCode ?? '' }}">

                        {{--  <select class="form-select select" name="customer_code">
                            <option value="">Select Customer code</option>
                            @foreach ($customer_code_list as $customer)
                                <option value="{{ $customer }}" @if (request()->query('customer_code') == $customer) selected @endif>
                                    {{ $customer }}</option>
                            @endforeach
                        </select>  --}}
                    </div>

                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>Customer Name</label>
                        @php
                            $customer_name = request()->query('customer_name');
                        @endphp
                        <input type="text" class="form-control" name="customer_name"
                            placeholder="Enter Customer Name:" value="{{ $customer_name ?? '' }}">

                        {{--  <select class="form-select select" name="customer_name">
                            <option value="">Select Customer Name</option>
                            @foreach ($customer_name_list as $name)
                                <option value="{{ $name }}" @if (request()->query('customer_name') == $name) selected @endif>
                                    {{ $name }}</option>
                            @endforeach
                        </select>  --}}
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>Customer City</label>
                        <select class="form-select select" name="customer_city">
                            <option value="">Select Customer City</option>
                            @foreach ($city_list as $name)
                                <option value="{{ $name }}" @if (request()->query('customer_city') == $name) selected @endif>
                                    {{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>


                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>SO Territory</label>
                        <select class="form-select select" name="so_teritory">
                            <option value="">Select SO Territory</option>
                            @foreach ($so_territory_list as $territory)
                                <option value="{{ $territory }}" @if (request()->query('so_teritory') == $territory) selected @endif>
                                    {{ $territory }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>ASM Territory</label>
                        <select class="form-select select" name="asm_teritory">
                            <option value="">Select ASM Territory</option>
                            @foreach ($asm_territory_list as $territory)
                                <option value="{{ $territory }}" @if (request()->query('asm_teritory') == $territory) selected @endif>
                                    {{ $territory }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>Route Name</label>
                        @php
                            $route_name = request()->query('route_name');
                        @endphp
                        <input type="text" class="form-control" name="route_name" placeholder="Enter route name:"
                            value="{{ $route_name ?? '' }}">

                        {{--  <select class="form-select select" name="route_name">
                            <option value="">Select Route Name</option>
                            @foreach ($route_list as $name)
                                <option value="{{ $name }}" @if (request()->query('route_name') == $name) selected @endif>
                                    {{ $name }}</option>
                            @endforeach
                        </select>  --}}
                    </div>
                </div>
                @if (isset($asset_mode) && $asset_mode=='placement')
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="form-group">
                        <label>Asset Serial Number</label>
                        @php
                            $SerialNumber = request()->query('SerialNumber');
                        @endphp
                        <input type="text" class="form-control" name="SerialNumber" placeholder="Search Serial Numbers:"
                            value="{{ $SerialNumber ?? '' }}">
                    </div>
                </div>
                @endif

                <div class=" col-lg-1 col-sm-6 col-12 ms-auto d-flex align-items-end justify-content-end">
                    <div class="form-group">
                        <button class="btn submitbtn btn-filter" type="submit"><img
                                src="{{ asset('img/icons/search-whites.svg') }}" alt="img"></button>
                    </div>
                </div>
                <hr>
            </div>
        </form>
    </div>
</div>
