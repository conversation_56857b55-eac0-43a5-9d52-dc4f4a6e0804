<!-- --  Sidebar -- -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-inner slimscroll">
        <div id="sidebar-menu" class="sidebar-menu">
            <ul>
                <li>
                    <a href="{{ route('admin.dashboard') }}"><i data-feather="compass"></i><span>
                            Dashboard</span> </a>
                </li>
                @canany(['UserAccess', 'AssetMasterCreationAccess'])
                    <li class="submenu">
                        <a href="javascript:void(0);"><i data-feather="package"></i><span>Master Creation</span><span
                                class="menu-arrow"></span></a>
                        <ul>
                            @canany('UserAccess')
                                <li class="submenu-open "><a class="act-color-after"
                                        href="{{ route('admin.users.index') }}"><span class="act-color-subtop">User
                                            Creation</span></a>
                                </li>
                            @endcanany
                            @canany('AssetMasterCreationAccess')
                                <li class="submenu-open "><a href="javascript:void(0);" class="act-color-after"><span
                                            class="me-2 act-color-subtop">Asset Master creation</span><i
                                            class="arrow-right-submenu" data-feather="arrow-right"></i></a>
                                    <ul class="menu-under-submenu">
                                        @canany('AssetPriceCreationAccess')
                                            <li><a href="{{ route('master_creation.price_creation') }}"><span>Asset Price
                                                        creation</span></a></li>
                                        @endcanany
                                        @canany('AssetEligibilityVPOCriteriaAccess')
                                            <li><a href="{{ route('master_creation.eligibility_vpo') }}"><span>Asset eligibility
                                                        VPO criteria</span></a></li>
                                        @endcanany
                                        @canany('AssetMasterCFALocationMappingAccess')
                                            <li><a href="{{ route('master_creation.cfa_location_mapping') }}"><span>CFA- Location
                                                        mapping </span></a></li>
                                        @endcanany
                                    </ul>

                                </li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['VendorAssetPlacementAllocationAccess', 'VendorAssetReplacementAllocationAccess',
                    'VendorAssetPulloutAllocationAccess', 'VendorAssetMaintenanceAllocationAccess',
                    'VendorAssetAToBOutletAssetTransferAllocationAccess'])
                    <li class="submenu">
                        <a class="" href="javascript:void(0);"><i data-feather="grid"></i><span>Vendor
                                Allocation Challan</span><span class="menu-arrow"></span></a>
                        <ul class="menu-under-submenu">
                            @canany('VendorAssetPlacementAllocationAccess')
                                <li>
                                    <a href="{{ route('vendor_allocation.placement_allocation') }}"></i><span>Asset
                                            Placement
                                            Allocation</span>
                                    </a>
                                </li>
                            @endcanany
                            @canany('VendorAssetReplacementAllocationAccess')
                                <li><a href="{{ route('vendor_allocation.replacement_allocation') }}"><span>Asset
                                            Replacement
                                            Allocation</span></a></li>
                            @endcanany
                            @canany('VendorAssetPulloutAllocationAccess')
                                <li><a href="{{ route('vendor_allocation.pullout_allocation') }}"><span>Asset
                                            Pullout
                                            Allocation</span></a></li>
                            @endcanany
                            @canany('VendorAssetMaintenanceAllocationAccess')
                                <li><a href="{{ route('vendor_allocation.maintenance_allocation') }}"><span>Asset
                                            Maintenance
                                            Allocation</span></a></li>
                            @endcanany
                            @canany('VendorAssetMaintenanceAllocationAccess')
                                <li><a href="{{ route('vendor_allocation.outlet_transfer_allocation') }}"><span>A
                                            to b outlet asset transfer </span></a></li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['UploadUtilityCFAWiseInventoryAccess', 'UploadUtilityAssetToRetailsMapppingInventoryAccess'])
                    <li class="submenu">
                        <a class="" href="javascript:void(0);"><i data-feather="aperture"></i><span>Upload
                                Utility</span><span class="menu-arrow"></span></a>
                        <ul class="menu-under-submenu">
                            @canany('UploadUtilityCFAWiseInventoryAccess')
                                <li><a href="{{ route('upload_utility.cfa_wise_inventory') }}"><span>CFA
                                            wise
                                            inventory upload </span></a></li>
                            @endcanany
                            @canany('UploadUtilityAssetToRetailsMapppingInventoryAccess')
                                <li><a href="{{ route('upload_utility.retailer_mapping') }}"><span>
                                            Asset to
                                            Retailer mapping upload</span></a></li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['AssetPlacementAccess', 'AssetReplacementAccess', 'AssetPulloutAccess',
                    'AssetMaintenanceAccess', 'AssetTransferAccess'])
                    <li class="submenu">
                        <a class="" href="javascript:void(0);"><i data-feather="briefcase"></i><span>Approval
                                Centre</span><span class="menu-arrow"></span></a>
                        <ul class="menu-under-submenu">
                            @canany('AssetPlacementAccess')
                                {{--  <li><a href="{{ route('approval_center.placement') }}"></i><span>Placement</span></a>  --}}
                                <li><a href="{{ route('approval_center.placement') }}"></i><span>Placement</span></a>
                                </li>
                            @endcanany
                            @canany('AssetReplacementAccess')
                                <li><a href="{{ route('approval_center.replacement') }}"><span>replacement</span></a>
                                </li>
                            @endcanany
                            @canany('AssetPulloutAccess')
                                <li><a href="{{ route('approval_center.pullout') }}"><span>pullout</span></a>
                                </li>
                            @endcanany

                            @canany('AssetMaintenanceAccess')
                                <li><a href="{{ route('approval_center.maintenance') }}"><span>Maintenance</span></a>
                                </li>
                            @endcanany
                            @canany('AssetTransferAccess')
                                <li><a href="{{ route('approval_center.outlet_transfer') }}"><span>A
                                            to b outlet asset transfer </span></a></li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['AssetManagementListAccess', 'AssetManagementApprovalCenterAccess',
                    'AssetManagementReportAccess'])
                    <li class="submenu">
                        <a class="" href="javascript:void(0);"><i data-feather="codesandbox"></i><span>Asset
                                Management</span><span class="menu-arrow"></span></a>
                        <ul class="menu-under-submenu">
                            @canany('AssetManagementListAccess')
                                <li><a href="?"><span> Asset
                                            list
                                        </span></a></li>
                            @endcanany
                            @canany('AssetManagementApprovalCenterAccess')
                                <li><a href="?"><span>
                                            Approval
                                            centre</span></a></li>
                            @endcanany
                            @canany('AssetManagementReportAccess')
                                <li><a href="?"><span>
                                            Reports</span></a></li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['InventoryManagementWorkingAccess', 'InventoryManagementScrapAccess',
                    'InventoryManagementInwardOutwardReportAccess'])
                    <li class="submenu">

                        <a class="" href="javascript:void(0);"><i data-feather="save"></i><span>Inventory
                                Management</span><span class="menu-arrow"></span></a>
                        <ul class="menu-under-submenu">
                            @canany('InventoryManagementWorkingAccess')
                                <li><a href="{{ route('inventory_managemant.working_inventory') }}"><span>Working
                                            Inventory</span></a></li>
                            @endcanany
                            @canany('InventoryManagementScrapAccess')
                                <li><a href="{{ route('inventory_managemant.scrap') }}"><span>scrap
                                            inventory</span></a></li>
                            @endcanany
                            @canany('InventoryManagementInwardOutwardReportAccess')
                                <li><a href="{{ route('inventory_managemant.inword_outword') }}"><span>Inward
                                            and
                                            Outward Report</span></a></li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['AdminPlacementReportAccess', 'AdminReplacementReportAccess', 'AdminPulloutReportAccess',
                    'AdminMaintenanceReportAccess', 'AdminAuditReportAccess', 'AdminAssetMasterMappingReportAccess',
                    'AdminAssetTransferABReportAccess'])
                    {{--  @canany('AssetAuditAccess', 'AssetAuditEditAccess', 'AssetAuditCreateAccess', 'AssetAuditDeleteAccess')  --}}
                    <li class="submenu">
                        <a class="" href="javascript:void(0);"><i data-feather="align-justify"></i><span>Admin
                                Reports</span><span class="menu-arrow"></span></a>
                        <ul class="menu-under-submenu">
                            @canany('AdminPlacementReportAccess')
                                <li><a href="{{ route('admin_report.placement_report') }}"></i><span>Placement
                                            Report</span></a></li>
                            @endcanany
                            @canany('AdminReplacementReportAccess')
                                <li><a href="{{ route('admin_report.replacement_report') }}"><span>replacement
                                            report</span></a></li>
                            @endcanany
                            @canany('AdminPulloutReportAccess')
                                <li><a href="{{ route('admin_report.pullout_report') }}"><span>pullout
                                            Report</span></a></li>
                            @endcanany
                            @canany('AdminMaintenanceReportAccess')
                                <li><a href="{{ route('admin_report.maintenance_report') }}"><span>Maintenance
                                            Report</span></a></li>
                            @endcanany
                            @canany('AdminAuditReportAccess')
                                <li><a href="{{ route('admin_report.audit_report') }}"><span>Audit
                                            Report</span></a></li>
                            @endcanany
                            @canany('AdminAssetMasterMappingReportAccess')
                                <li><a href="{{ route('admin_report.master_mapping_report') }}"><span>Asset
                                            Master Mapping</span></a></li>
                            @endcanany
                            @canany('AdminAssetTransferABReportAccess')
                                <li><a href="{{ route('admin_report.ab_transfer_report') }}"><span>Asset
                                            Transfer AB Reports </span></a></li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['AssetAuditReportAccess', 'AssetPlacementReportAccess', 'AssetReplacementReportAccess',
                    'AssetPulloutReportAccess', 'AssetMaintenanceReportAccess', 'AssetTransferReportAccess'])
                    <li class="submenu">

                        <a class="" href="javascript:void(0);"><i data-feather="award"></i><span>Approval
                                Reports</span><span class="menu-arrow"></span></a>
                        <ul class="menu-under-submenu">
                            @canany('AssetPlacementReportAccess')
                                <li><a href="{{ route('approval_report.placement_report') }}"></i><span>Placement
                                            Report</span></a></li>
                            @endcanany
                            @canany('AssetReplacementReportAccess')
                                <li><a href="{{ route('approval_report.replacement_report') }}"><span>Replacement
                                            report</span></a></li>
                            @endcanany
                            @canany('AssetPulloutReportAccess')
                                <li><a href="{{ route('approval_report.pullout_report') }}"><span>pullout
                                            Report</span></a></li>
                            @endcanany
                            @canany('AssetMaintenanceReportAccess')
                                <li><a href="{{ route('approval_report.maintenance_report') }}"><span>Maintenance
                                            Report</span></a></li>
                            @endcanany
                            {{--  <li><a href="{{ route('approval_report.audit_report') }}"><span>Audit
                                     Report</span></a></li>  --}}
                            @canany('AssetAuditReportAccess')
                                <li><a href="{{ route('admin_report.audit_report') }}"><span>Audit
                                            Report</span></a></li>
                            @endcanany
                            @canany('AssetMasterMappingReportAccess')
                                <li><a href="{{ route('approval_report.asset_master_mapping_report') }}"><span>Asset
                                            Master
                                            Mapping</span></a></li>
                            @endcanany
                            @canany('AssetCFAwiseAssetInventoryReportAccess')
                                <li><a href="{{ route('approval_report.cfa_wise_inventory_report') }}"><span>CFA
                                            wise Asset
                                            Inventory report</span></a></li>
                            @endcanany
                        </ul>
                    </li>
                @endcanany
                @canany(['VendorReportPlacementAccess', 'VendorReportReplacementAccess', 'VendorReportPulloutAccess',
                    'VendorReportMaintenanceAccess', 'VendorReportAuditAccess', 'VendorReportAssetMasterMappingAccess',
                    'VendorReportAssetWiseInventoryReportAccess', 'VendorReportABAssetTransferReportAccess'])
                    <li class="submenu">
                        <a class="" href="javascript:void(0);">
                            <i data-feather="bookmark"></i>
                            <span>Reports</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="menu-under-submenu">
                            @canany('VendorReportPlacementAccess')
                                <li><a href="{{ route('report.placement_report') }}"><span>Placement Report</span></a></li>
                            @endcanany
                            @canany('VendorReportReplacementAccess')
                                <li><a href="{{ route('report.replacement_report') }}"><span>Replacement Report</span></a>
                                </li>
                            @endcanany
                            @canany('VendorReportPulloutAccess')
                                <li><a href="{{ route('report.pullout_report') }}"><span>Pullout Report</span></a></li>
                            @endcanany
                            @canany('VendorReportMaintenanceAccess')
                                <li><a href="{{ route('report.maintenance_report') }}"><span>Maintenance Report</span></a>
                                </li>
                            @endcanany
                            @canany('VendorReportAuditAccess')
                                <li><a href="{{ route('report.audit_report') }}"><span>Audit Report</span></a></li>
                            @endcanany
                            @canany('VendorReportAssetMasterMappingAccess')
                                <li><a href="{{ route('report.asset_master_mapping_report') }}"><span>Asset Master
                                            Mapping</span></a></li>
                            @endcanany
                            @canany('VendorReportAssetWiseInventoryReportAccess')
                                <li><a href="{{ route('report.asset_wise_inventory_report') }}"><span>Asset wise Inventory
                                            report</span></a></li>
                            @endcanany
                            @canany('VendorReportABAssetTransferReportAccess')
                                <li><a href="{{ route('report.ab_transfer_report') }}"><span>Asset transfer (A-B)
                                            report</span></a></li>
                            @endcanany
                        </ul>
                    </li>

                @endcanany
            </ul>
        </div>
    </div>
</div>
