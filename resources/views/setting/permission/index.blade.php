<x-app-layout>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Permission </h4>
                    <h6>Permission List </h6>
                </div>

            </div>
            <div class="card">
                <div class="card-body">

                    <div class="table-responsive ">
                        <!-- SO Creation Data -->
                        <table class="table user-so-table">
                            <thead>
                            <tr>

                                <th>Sr No.</th>
                                <th>Permission Name</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                                @can('Permission access')
                                @foreach($permissions as $key=>$permission)
                                <tr>
                                    <td>{{ $key+1; }}</td>
                                  <td>{{ $permission->name }}</td>
                                  <td class="py-4 px-6 border-b border-grey-light text-right">
                                    @can('Permission edit')
                                    <a href="{{route('admin.permissions.edit',$permission->id)}}" class="text-grey-lighter font-bold py-1 px-3 rounded text-xs bg-green hover:bg-green-dark text-blue-400">Edit</a>
                                    @endcan

                                    @can('Permission delete')
                                    <form action="{{ route('admin.permissions.destroy', $permission->id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('delete')
                                        <button class="text-grey-lighter font-bold py-1 px-3 rounded text-xs bg-blue hover:bg-blue-dark text-red-400">Delete</button>
                                    </form>
                                    @endcan
                                  </td>
                                </tr>
                                @endforeach
                              @endcan
                            </tbody>
                        </table>

                    </div>
                    <div class="row btn-approve-reject">
                        <div class="col-12 my-2">
                            @can('UserAccess')
                                <div class="text-right">
                                   {!! $permissions->withQueryString()->links('pagination::bootstrap-5') !!}
                                </div>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
