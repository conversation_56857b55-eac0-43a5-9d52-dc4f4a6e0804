<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>User Select </h4>
                    <h6>MasterCration/UserCration/user select </h6>

                </div>
            </div>
            <div class="row justify-content-between my-3 uploader-data-top">

                <div class="card">
                    <div class="card-body">
                        <div id="error-message" class="text-center alert alert-danger" style="display: none;">
                            Form for selected role does not exist
                        </div>
                        <form method="POST" action="{{ route('admin.roles.update', $role->id) }}">
                            @csrf
                            @method('put')
                            <input type="hidden" id="selectedRole" name="roles[]" value="">

                            {{--  #so Creation  --}}
                            <div class="row">

                                <div class="col-lg-6 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label>Role Name *</label>
                                        <input id="role_name" type="text" name="name"
                                            value="{{ old('name', $role->name) }}" placeholder="Placeholder"
                                            class="form-control" />
                                        @error('name')
                                            <span class="validatin-error">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-lg-6 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label>Select Hierarchy *</label>
                                        <select id="hierarchy_view" name="hierarchy_view" class="form-select select">
                                            <option value="" disabled selected>Select Hierarchy</option>
                                            @foreach ($hierarchy as $h)
                                                <option value="{{ $h }}"
                                                    {{ old('hierarchy_view') == $h || $role->hierarchy_view == $h ? 'selected' : '' }}>
                                                    {{ $h }}
                                            @endforeach
                                        </select>
                                        @error('hierarchy_view')
                                            <span class="validatin-error">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12 col-sm-6 col-12">
                                    <div class=" gap-4">
                                        <h4 class="text-xl my-4 text-gray-600">Permissions</h4>

                                        <th>
                                            <label class="checkboxs">
                                                <input type="checkbox" id="select-all">
                                                <span class="checkmarks"></span>
                                            </label>
                                        </th>
                                        <div class="row">
                                            @foreach ($permissions as $permission)
                                                <div class="col-md-6 col-sm-12 ">
                                                    <label class="inline-flex items-center mt-3">
                                                        <input type="checkbox"
                                                            class="form-checkbox h-5 w-5 text-blue-600"
                                                            name="permissions[]" value="{{ $permission->id }}"
                                                            @if (in_array($permission->id, old('permissions', $role->permissions->pluck('id')->toArray()))) checked @endif>
                                                        <span class="ml-2 text-gray-700">{{ $permission->name }}</span>
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>

                                    </div>
                                </div>

                            </div>


                            <div class="d-block" style="margin-top: 25px">
                                <div class="col-lg-4 col-sm-6 col-12">
                                    <button class="btn btn-submit me-2" type="submit">Submit</button>
                                    <a href="#" class="btn btn-cancel">Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {{--  user import model   --}}
            @include('setting.user.modal.user_modal_import')

            {{--  user import History   --}}
            @include('setting.user.modal.userUploadHistory')
        </div>

</x-app-layout>
