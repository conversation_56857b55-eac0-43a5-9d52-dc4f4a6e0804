<x-app-layout>
    <style>
        .asset-badge{
            background-color: #ff9f4373 !important;
            color: #000;
        }
    </style>
    <div class="page-wrapper">

        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>User Roles</h4>
                    <h6>Role List</h6>
                </div>
                <div>
                    <!-- Buttons from the first HTML code -->

                    <a class="btn btn-primary" href="{{route('admin.roles.create')}}">
                        <i class="fa fa-user" aria-hidden="true"></i> Create Role
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">

                    <!-- Role table -->
                    <div class="table-responsive">
                        <table class="table user-so-table">
                            <thead>
                                <tr>
                                    <th>Sr No.</th>
                                    <th>Role Name</th>
                                    <th>Permissions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @can('Role access')
                                @foreach($roles as $key=>$role)
                                <tr>
                                    <td>{{ $key+1 }}</td>
                                    <td>{{ $role->name }}</td>
                                    <td class="py-4 px-6 border-b border-grey-light">
                                        <div class="d-flex flex-wrap">
                                            @foreach($role->permissions as $permission)
                                                <span class="badge badge-pill me-1 mb-1 asset-badge">{{ $permission->name }}</span>
                                                {{--  <span class="badge badge-pill bg-secondary me-1 mb-1 asset-badge">{{ $permission->name }}</span>  --}}
                                            @endforeach
                                        </div>
                                    </td>
                                    <td class="py-4 px-6 border-b border-grey-light text-right">
                                        @can('Role edit')
                                        <a href="{{ route('admin.roles.edit',$role->id) }}" class="text-grey-lighter font-bold py-1 px-3 rounded text-xs bg-green hover:bg-green-dark text-blue-400">Edit</a>
                                        @endcan
                                        @can('Role delete')
                                        <form action="{{ route('admin.roles.destroy', $role->id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('delete')
                                            <button class="text-grey-lighter font-bold py-1 px-3 rounded text-xs bg-blue hover:bg-blue-dark text-red-400">Delete</button>
                                        </form>
                                        @endcan
                                    </td>
                                </tr>
                                @endforeach
                                @endcan
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="row btn-approve-reject">
                        <div class="col-12 my-2">
                            {{--  @can('User access')  --}}
                            <div class="text-right">
                                {!! $roles->withQueryString()->links('pagination::bootstrap-5') !!}
                            </div>
                            {{--  @endcan  --}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
