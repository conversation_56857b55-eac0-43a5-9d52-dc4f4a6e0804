<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>User Select </h4>
                    <h6>MasterCration/UserCration/user select </h6>

                </div>
            </div>
            <div class="row justify-content-between my-3 uploader-data-top">
                @if (\Session::has('success'))
                    <div class="alert alert-success" role="alert">
                        {!! \Session::get('success') !!}
                    </div>
                @endif

                @if (\Session::has('error'))
                    <div class="alert alert-danger" role="alert">
                        {!! \Session::get('error') !!}
                    </div>
                @endif
                <div class=" col-md-3 col-sm-6">

                    <div class="form-group">
                        <label for="target_User" class="page-header mb-2">Select User Role:</label>
                        <select class="form-select select" id="target_User" name="user_type">
                            @foreach ($roles as $role)
                                @php
                                    $templateFormat = isset($uploadFormats[$role->name])
                                        ? asset($uploadFormats[$role->name])
                                        : '';
                                    $retailer_lat_long = isset($uploadFormats['retailer_lat_long'])
                                        ? asset($uploadFormats['retailer_lat_long'])
                                        : '';
                                    $retailer_gst_update = isset($uploadFormats['retailer_gst_update'])
                                        ? asset($uploadFormats['retailer_gst_update'])
                                        : '';
                                    $asset_retailer_mapping = isset($uploadFormats['asset_retailer_mapping'])
                                        ? asset($uploadFormats['asset_retailer_mapping'])
                                        : '';
                                    $db_to_cfa_mapping = isset($uploadFormats['db_to_cfa_mapping'])
                                        ? asset($uploadFormats['db_to_cfa_mapping'])
                                        : '';
                                @endphp
                                <option value="{{ $role->name }}" data-hierarchy="{{ $role->hierarchy_view }}"
                                    data-templateformat="{{ $templateFormat ?? '' }}"
                                    {{ old('user_type') == $role->name || (empty(old('user_type')) && $role->name == 'SO') ? 'selected' : '' }}>
                                    {{ $role->name }}
                                </option>
                            @endforeach
                            <option value="db_to_cfa_mapping" data-templateformat="{{ $db_to_cfa_mapping ?? '' }}">
                                Distributor to CFA Mapping</option>
                            <option value="retailer_lat_long" data-templateformat="{{ $retailer_lat_long ?? '' }}">
                                Customer Lat & Long Update</option>
                            <option value="retailer_gst_update" data-templateformat="{{ $retailer_gst_update ?? '' }}">
                                Customer GST Update</option>
                            <option value="asset_retailer_mapping"
                                data-templateformat="{{ $asset_retailer_mapping ?? '' }}">Asset Retailer Mapping
                            </option>
                        </select>

                    </div>
                </div>
                <!-- Button trigger modal -->
                <div class="col-md-6 col-sm-6 d-flex align-center justify-content-end page-btn uploader-file">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#uploadFileleModal">
                        <i class="fa fa-upload" aria-hidden="true"></i> Upload File
                    </button>
                    {{--  <button type="button" class="btn btn-primary ms-2" data-bs-toggle="modal"
                        data-bs-target="#upload_history_view"><i class="fa fa-history" aria-hidden="true"></i>
                        Upload History
                    </button>  --}}
                </div>
                <div class="card">
                    <div class="card-body">
                        {{--  <div id="error-message" class="text-center alert alert-danger" style="display: none;">
                        Form for selected role does not exist
                    </div>  --}}
                        <div class="page-title">
                            <h6>Latest Upload History </h6><br>
                        </div>
                        <div class="table-responsive shadow border p-2">
                            <style>
                                .batch-id-column {
                                    min-width: 200px;
                                    word-break: break-all;
                                    font-family: monospace;
                                    font-size: 12px;
                                }
                            </style>
                            <table class="table  ">
                                <thead>
                                    <tr>
                                        <th>BatchID</th>
                                        <th> UPLOADED DATE &amp; TIME</th>
                                        <th>TOTAL RECORDS</th>
                                        <th>INCORRECT RECORDS</th>
                                        <th>SUCCESS RECORDS </th>
                                        <th>STATUS</th>
                                        <th>UPLOAD TYPE</th>
                                        {{--  <th>UPLOAD BY</th>  --}}
                                        {{--  <th>Client IP</th>  --}}
                                        <th>ACTION</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (!empty($uploadHistory) && $uploadHistory->count() > 0)
                                        @php
                                            $ErrorLogCount = $uploadHistory->assetLog->count();
                                            $successCount = $uploadHistory->totalRecordscount - $ErrorLogCount;
                                        @endphp
                                        <tr>
                                            <td class="batch-id-column" title="{{ $uploadHistory->batchID ?? '' }}">
                                                {{ $uploadHistory->batchID ?? '' }}
                                            </td>
                                            <td>{{ $uploadHistory->uploadDateTime ?? '' }}</td>
                                            <td>{{ $uploadHistory->totalRecordscount ?? 0 }}</td>
                                            <td>{{ $ErrorLogCount ?? 0 }}</td>
                                            <td>{{ $successCount ?? 0 }}</td>
                                            <td>Success</td>
                                            <td>{{ $uploadHistory->uploadType ?? '' }}</td>
                                            {{--  <td>{{ $uploadHistory->uploadByName??''}}</td>  --}}
                                            {{--  <td>{{ $uploadHistory->requestIP??''}}</td>  --}}
                                            <td>
                                                @if ($successCount == $uploadHistory->totalRecordscount)
                                                    Completed
                                                @else
                                                    <a
                                                        href="{{ route('admin.import.batch_upload_history_download', ['batchID' => $uploadHistory->batchID]) }}">
                                                        <button class="btn btn-primary"> <i class="fa fa-download me-2"
                                                                aria-hidden="true"></i>
                                                            Download</button>
                                                    </a>
                                                @endif
                                            </td>
                                        </tr>
                                    @else
                                        <tr>
                                            <td colspan="8" style="text-align: center;">No Upload History available
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        {{--  <form method="POST" action="{{ route('admin.users.store') }}" class="mb-2 formUser">  --}}
                        {{--  @csrf  --}}
                        {{--  @method('post')  --}}
                        {{--  <input type="hidden" id="selectedRole" name="user_type" value="">  --}}

                        {{--  #so Creation  --}}
                        {{--  <div class="row" id="common_user_form">
                            @include('setting.user.userForm.so_creation')
                        </div>  --}}

                        {{--  #Retailer Creation  --}}
                        {{--  <div class="row user-fields" id="RETAILERFields">
                            @include('setting.user.userForm.retailers_creation')
                        </div>  --}}

                        {{--  #CFA Creation  --}}
                        {{--  <div class="row user-fields" id="CFAFields">
                            @include('setting.user.userForm.cfa_creation')
                        </div>  --}}


                        {{--  <div class="d-block">
                            <div class="col-lg-4 col-sm-6 col-12">
                                <button class="btn btn-submit me-2" type="submit">Submit</button>
                                <a href="#" class="btn btn-cancel">Cancel</a>
                            </div>
                        </div>  --}}
                        {{--  </form>  --}}
                    </div>
                </div>
            </div>
            {{--  user import model   --}}
            {{--  @include('setting.user.modal.user_modal_import')  --}}
            @include('setting.user.modal.user_modal_import', [
                'redirectRoute' => route('admin.users.create'),
            ])

            {{--  user import History   --}}
            {{--  @include('setting.user.modal.userUploadHistory')  --}}
        </div>
        <script>
            $('#uploadFileleModal').on('hidden.bs.modal', function() {
                $("#ajax_message").empty();
                $('.validatin-error').remove();
                $(this).find('form').trigger('reset');

                // Clear the file input field
                $('#user_import_file').val('');

                // Reset the custom label text
                $('.js-fileName').text('Choose a file...');

                // If you need to reset the form explicitly
                var form = $(this).find('form')[0];
                if (form) {
                    form.reset();
                }
            });
        </script>
</x-app-layout>
