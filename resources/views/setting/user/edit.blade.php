<x-app-layout>
   <div>
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200">
            <div class="container mx-auto px-6 py-1 pb-16">
              <div class="bg-white shadow-md rounded my-6 p-5">
                <form method="POST" action="{{ route('admin.users.update',$user->id)}}">
                  @csrf
                  @method('put')
                  <div class="grid grid-cols-2 gap-4">
                    <div class="flex flex-col space-y-2">

                        <label for="region" class="text-gray-700 select-none font-medium">Select Roles </label>
                        <select id="roles" name="roles[]"
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200">
                            @foreach ($roles as $role)
                            @php
                                 $selectRoles= !empty($user->roles->first()->id)?$user->roles->first()->id:'';
                            @endphp
                                <option value="{{ $role->id }}" data-hierarchy="{{ $role->hierarchy_view }}"
                                    {{ (old('roles') && old('roles') == $role->id) || $selectRoles== $role->id ? 'selected' : '' }}>
                                    {{ $role->name }}
                            @endforeach

                        </select>
                    </div>

                    <div class="flex flex-col space-y-2">
                        <label for="name" class="text-gray-700 select-none font-medium">Customer Name</label>
                        <input id="name" type="text" name="name" value="{{ old('name',$user->name??'') }}"
                            placeholder="Enter Customer Name"
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('name')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>


                    <div class="flex flex-col space-y-2">
                        <label for="email" class="text-gray-700 select-none font-medium">Email</label>
                        <input id="email" type="text" name="email" value="{{ old('email',$user->email??'') }}"
                            placeholder="Enter email"
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('email')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>


                    <div class="flex flex-col space-y-2">
                        <label for="mobile_number" class="text-gray-700 select-none font-medium">Mobile
                            number</label>
                        <input id="mobile_number" type="text" name="mobile_number"
                            value="{{ old('mobile_number',$user->mobile_number??'') }}" placeholder="Enter mobile number"
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('mobile_number')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="flex flex-col space-y-2">
                        <label for="address_1" class="text-gray-700 select-none font-medium">Address 1
                        </label>
                        <input id="address_1" type="text" name="address_1"
                            value="{{ old('address_1',$user->address_1??'') }}" placeholder="Enter Address 1"
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('address_1')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="flex flex-col space-y-2">
                        <label for="city" class="text-gray-700 select-none font-medium">City </label>
                        <input id="city" type="text" name="city" value="{{ old('city',$user->city??'') }}"
                            placeholder="Enter city "
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('city')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="flex flex-col space-y-2">
                        <label for="state" class="text-gray-700 select-none font-medium">State </label>
                        <input id="state" type="text" name="state" value="{{ old('state',$user->state??'') }}"
                            placeholder="Enter state "
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('state')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="flex flex-col space-y-2">
                        <label for="pin_code" class="text-gray-700 select-none font-medium">Pincode </label>
                        <input id="pin_code" type="text" name="pin_code" value="{{ old('pin_code',$user->pin_code??'') }}"
                            placeholder="Enter pin code "
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('pin_code')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="flex flex-col space-y-2">
                        <label for="region" class="text-gray-700 select-none font-medium">Region </label>
                        <input id="region" type="text" name="region" value="{{ old('region',$user->region??'') }}"
                            placeholder="Enter Region "
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                        @error('region')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>


                    <div class="flex flex-col space-y-2">
                        <label for="region" class="text-gray-700 select-none font-medium">Status </label>
                        <select id="status" name="status"
                            class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200">
                            <option value="1" {{ old('status',$user->status==1?'selected':'') }}>Active</option>
                            <option value="0" {{ old('status',$user->status==0?'selected':'') }}>Inactive</option>

                        </select>
                        @error('status')
                            <span class="validatin-error">{{ $message }}</span>
                        @enderror
                    </div>



                </div>
                <div class="mt-4 mb-4">
                  <button type="submit" class="bg-blue-500 text-white font-bold px-5 py-1 rounded focus:outline-none shadow hover:bg-blue-500 transition-colors ">Submit</button>
                </div>
              </div>


            </div>
        </main>
    </div>
</div>

</x-app-layout>
