<x-app-layout>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>So User Update</h4>
                    <h6>
                        <span style='color:green';>When the SO user code has been updated, the corresponding SO user hierarchy code will be updated automatically </span>
                    </h6>
                </div>

            </div>
            @if (\Session::has('success'))
                <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                    <strong>Success!</strong> {!! \Session::get('success') !!}
                </div>
            @endif

            @if (\Session::has('error'))
                <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                    {!! \Session::get('error') !!}
                </div>
            @endif
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.update_user_info') }}"
                        class="mb-2 formUser">
                        @csrf
                        @method('post')
                        <input type="hidden" name="old_user_id" value="{{ $user_details->user_id??'' }}">

                        <div class="row">

                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">SO name *</label>
                                    <input type="text" class="form-control" name="name"
                                        placeholder="Enter SO Name" value="{{ $user_details->name??'' }}" required

                                        required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">SO user code *</label>
                                    <input type="text" class="form-control" name="user_id"
                                        placeholder="Enter SO User Code" value="{{ $user_details->user_id??'' }}" required

                                        required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">Email </label>
                                    <input type="text" class="form-control" name="email"
                                        placeholder="Enter Email" value="{{ $user_details->email??'' }}"
                                    required>

                                </div>
                            </div>
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">Mobile number </label>
                                    <input type="text" class="form-control" name="mobile_number"
                                        placeholder="Enter Mobile numbe" value="{{ $user_details->mobile_number??'' }}"
                                        {{--  minlength="10" maxlength="10" title="Mobile number must be 10 digits"  --}}

                                        required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">Status </label>
                                    <select id="status" name="status"
                                    class="form-control">
                                    <option value="1" {{ old('status',$user_details->status==1?'selected':'') }}>Active</option>
                                    <option value="0" {{ old('status',$user_details->status==0?'selected':'') }}>Inactive</option>

                                </select>
                                </div>
                            </div>

                            <div class="d-flex col-12 justify-content-lg-end justify-content-center ">
                                <a href="{{ url()->previous() }}" class="btn btn-cancel">Cancel</a>
                                <button class="btn btn-submit mx-2" type="submit">Submit</button>

                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
