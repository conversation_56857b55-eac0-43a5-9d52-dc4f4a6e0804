<x-app-layout>
    <style>

    </style>
    <div>
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200">
            <div class="container mx-auto px-6 py-1 pb-16">
                <div id="ajax_message"></div>
                <div class="bg-white shadow-md rounded my-6 p-5">

                    <h2 class="text-2xl font-semibold mb-4">User Master Import </h2>
                    <form method="POST" id="user_import_form" action="{{ route('admin.import.user_master_import') }}"
                        enctype="multipart/form-data">
                        @csrf
                        @method('post')

                        <div class="grid grid-cols-2 gap-4">
                            <div class="flex flex-col space-y-2">
                                <label for="region" class="text-gray-700 select-none font-medium">Select Roles
                                </label>
                                <select id="roles" name="user_type"
                                    class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200">
                                    @foreach ($roles as $role)
                                        <option value="{{ $role->name }}" data-hierarchy="{{ $role->hierarchy_view }}"
                                            {{ old('roles') == $role->name ? 'selected' : '' }}>
                                            {{ $role->name }}
                                    @endforeach
                                    <input type="hidden" id="selectedRole" name="selectedRole" value="">
                                </select>
                            </div>
                            <div class="flex flex-col space-y-2">
                                <label for="user_import_file" class="text-gray-700 select-none font-medium">Upload
                                    file</label>
                                <input id="user_import_file" type="file" name="user_import_file"
                                    value="{{ old('user_import_file') }}" placeholder="Enter user_import_file"
                                    class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                                @error('user_import_file')
                                    <span class="validatin-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="mt-4 mb-4">
                            <button type="submit"
                                class="bg-blue-500 text-white font-bold px-5 py-1 rounded focus:outline-none shadow hover:bg-blue-500 transition-colors ">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        $('#user_import_form').on('submit', function(event) {
            event.preventDefault();
            showLoader()
            // Clear existing error messages
            $('#ajax_message').html('');

            var formData = new FormData(this);
            formData.append('user_import_file', $('#user_import_file')[0].files[0]);

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    hideLoader();
                    // Handle success response
                    $('#ajax_message').html(success_message(response.success));
                },
                error: function(xhr, status, error) {
                    hideLoader();
                    // Handle error response
                    if (xhr.status === 422 && xhr.responseJSON.error === "empty_file") {
                        $('#ajax_message').html(error_message(xhr.responseJSON.message));
                    } else if (xhr.status === 422 && xhr.responseJSON.errors) {
                        var errors = xhr.responseJSON.errors;
                        console.log("errors >>>", errors);
                        $('.validatin-error').remove();
                        // Iterate over properties of the errors object
                        for (var key in errors) {
                            if (errors.hasOwnProperty(key)) {
                var errorMessage = '<div class="validatin-error">' + errors[key][0] + '</div>';
                // Find the input field with corresponding name attribute
                $('[name="' + key + '"]').after(errorMessage);
            }
                        }
                    } else {
                        $('#ajax_message').html(error_message(xhr.responseJSON.message));
                    }
                }


            });
        });
    </script>


</x-app-layout>
