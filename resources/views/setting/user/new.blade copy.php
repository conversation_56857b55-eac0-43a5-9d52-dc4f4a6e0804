<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>User Select </h4>
                    <h6>MasterCration/UserCration/user select </h6>

                </div>
            </div>
            <div class="row justify-content-between my-3 uploader-data-top">
                <div class=" col-md-3 col-sm-6">
                    <div class="form-group">
                        <label for="target_User" class="page-header mb-2">Select User Role:</label>
                        <select class="form-select select" id="target_User" name="user_type">
                            @foreach ($roles as $role)
                                <option value="{{ $role->name }}" data-hierarchy="{{ $role->hierarchy_view }}"
                                    {{ old('user_type') == $role->name || empty(old('user_type')) && $role->name == 'SO' ? 'selected' : '' }}>
                                    {{ $role->name }}
                                </option>
                            @endforeach
                            <option value="db_to_cfa_mapping">Distributor to CFA Mapping</option>
                            <option value="retailer_lat_long">Customer Lat & Long Update</option>
                            <option value="retailer_gst_update">Customer GST Update</option>
                            <option value="asset_retailer_mapping">Asset Retailer Mapping </option>
                        </select>

                    </div>
                </div>
                <!-- Button trigger modal -->
                <div class="col-md-6 col-sm-6 d-flex align-center justify-content-end page-btn uploader-file">
                   <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#uploadFileleModal">
                        <i class="fa fa-upload" aria-hidden="true"></i> Upload File
                    </button>
                   <button type="button" class="btn btn-primary ms-2" data-bs-toggle="modal"
                                data-bs-target="#upload_history_view"><i class="fa fa-history" aria-hidden="true"></i>
                            Upload History
                    </button>
                </div>
            {{--  <div class="card">  --}}
                {{--  <div class="card-body">  --}}
                    {{--  <div id="error-message" class="text-center alert alert-danger" style="display: none;">
                        Form for selected role does not exist
                    </div>  --}}
                    {{--  <form method="POST" action="{{ route('admin.users.store') }}" class="mb-2 formUser">  --}}
                        {{--  @csrf  --}}
                        {{--  @method('post')  --}}
                        {{--  <input type="hidden" id="selectedRole" name="user_type" value="">  --}}

                        {{--  #so Creation  --}}
                        {{--  <div class="row" id="common_user_form">
                            @include('setting.user.userForm.so_creation')
                        </div>  --}}

                        {{--  #Retailer Creation  --}}
                        {{--  <div class="row user-fields" id="RETAILERFields">
                            @include('setting.user.userForm.retailers_creation')
                        </div>  --}}

                        {{--  #CFA Creation  --}}
                        {{--  <div class="row user-fields" id="CFAFields">
                            @include('setting.user.userForm.cfa_creation')
                        </div>  --}}


                        {{--  <div class="d-block">
                            <div class="col-lg-4 col-sm-6 col-12">
                                <button class="btn btn-submit me-2" type="submit">Submit</button>
                                <a href="#" class="btn btn-cancel">Cancel</a>
                            </div>
                        </div>  --}}
                    {{--  </form>  --}}
                {{--  </div>  --}}
            {{--  </div>  --}}
        </div>
            {{--  user import model   --}}
            @include('setting.user.modal.user_modal_import')

            {{--  user import History   --}}
            @include('setting.user.modal.userUploadHistory')
    </div>

</x-app-layout>
