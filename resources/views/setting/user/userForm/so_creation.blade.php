<div class="page-header justify-content-center my-md-3">
    <h4>User <span id="form_title">SO</span> Creation </h4>
</div>

<div class="col-lg-6 col-sm-6 col-12 channelFields">
    <div class="form-group">
        <label>Channel</label>
        <input type="text" class="form-control" name="channel_code" value="{{ old('channel_code') }}" placeholder="Enter  Channle code:">
        @error('channel_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class="col-lg-6 col-sm-6 col-12 teritoryFields">
    <div class="form-group ">
        <label>Teritory Code *</label>
        <div class="position-relative">
            <select class="custom_select_input">
                <option disabled selected value="default">Choose option</option>
                <option selected value="SO-TT-Bhagalpur">SO-TT-Bhagalpur</option>
                <option value="SMR-TT-Hisar">SMR-TT-Hisar</option>
                <option value="SO-MT-Chandigarh">SO-MT-Chandigarh</option>
                <option value="SMR-AC-Rajasthan">SMR-AC-Rajasthan</option>
                <option value="ASE-TT-Raipur">ASE-TT-Raipur</option>
                <option value="SO-DCOM-N&E">SO-DCOM-N&E</option>
                <option value="SO-TT-Coimbatore 2">SO-TT-Coimbatore 2</option>
            </select>
            <input name="teritory_code" placeholder="Enter Your SO Teritory Code" value="{{ old('teritory_code') }}"></input>

        </div>
        @error('teritory_code')
                <span class="validatin-error">{{ $message }}</span>
            @enderror
    </div>
</div>
<div class="col-lg-6 col-sm-6 col-12">
    <div class="form-group">
        <label>User Code *</label>
        <input type="text" class="form-control" name="user_code" value="{{ old('user_code') }}" placeholder="Enter  User Code:">
        @error('user_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class=" col-lg-6 col-sm-6 col-12">
    <div class="form-group">
        <label> Name *</label>
        <input type="text" class="form-control" name="name" placeholder="Enter  Name:" value="{{ old('name') }}">
        @error('name')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>

</div>
<div class=" col-lg-6 col-sm-6 col-12">
    <div class="form-group">
        <label>Email Address</label>
        <input type="email" class="form-control" name="email" value="{{ old('email') }}" placeholder="Enter Email Address:">
        @error('email')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class="col-lg-6 col-sm-6 col-12">
    <div class="form-group">
        <label>Mobile Number</label>
        <input type="tel" class="form-control" name="mobile_number" value="{{ old('mobile_number') }}" placeholder="Enter mobile number:">
        @error('mobile_number')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class="col-lg-6 col-sm-6 col-12 regionNameFields">
    <div class="form-group">
        <label>Region Name</label>
        <input type="text" class="form-control" name="region_name"  value="{{ old('region_name') }}" placeholder="Enter Region name:">
        @error('region_name')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class="col-lg-6 col-sm-6 col-12 regionCodeFields">
    <div class="form-group ">
        <label>Region Code </label>

        <div class="position-relative">
            <select class="custom_select_input">
                <option disabled selected value="default">Choose option</option>
                <option selected value="RSMTT_EAST">RSMTT_EAST</option>
                <option value="RSM_TT_North">RSM_TT_North</option>
                <option value="RSM_MT">RSM_MT</option>
                <option value="RSM_TT_West">RSM_TT_West</option>
                <option value="RSM_D.COM">RSM_D.COM</option>
                <option value="RSM_TT_South">RSM_TT_South</option>
            </select>
            <input placeholder="Enter Your RSM/RBDM Region Code" value="{{ old('region_code') }}" name="region_code"></input>

        </div>
        @error('region_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>

</div>
<div class="col-lg-6 col-sm-6 col-12 areaCodeFields">
    <div class="form-group ">
        <label>Area Code </label>

        <div class="position-relative">
            <select class="custom_select_input">
                <option disabled selected value="default">Choose option</option>
                <option selected value="RSMTT_EAST">RSMTT_EAST</option>
                <option value="RSM_TT_North">RSM_TT_North</option>
                <option value="RSM_MT">RSM_MT</option>
                <option value="RSM_TT_West">RSM_TT_West</option>
                <option value="RSM_D.COM">RSM_D.COM</option>
                <option value="RSM_TT_South">RSM_TT_South</option>
            </select>
            <input placeholder="Enter Your Area Code" id="area_Code" name="area_code" value="{{ old('area_code') }}"></input>

        </div>
        @error('area_code')
                <span class="validatin-error">{{ $message }}</span>
            @enderror
    </div>

</div>
<div class=" col-lg-6 col-sm-6 col-12 cfaCodeFields">
    <div class="form-group">
        <label>CFA Code</label>
        <input type="text" class="form-control" name="cfa_code" value="{{ old('cfa_code') }}" placeholder="Enter CFA Code:">
        @error('cfa_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class=" col-lg-6 col-sm-6 col-12">
    <div class="form-group">
        <label>City</label>
        <input type="text" class="form-control" name="city" value="{{ old('city') }}"  placeholder="Enter City Name:">
        @error('city')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class=" col-lg-6 col-sm-6 col-12">
    <div class="form-group">
        <label>State</label>
        <input type="text" class="form-control" name="state" value="{{ old('state') }}"  placeholder="Enter State Name :">
        @error('state')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class=" col-lg-6 col-sm-6 col-12">
    <div class="form-group">
        <label>Pincode</label>
        <input type="tel" class="form-control" name="pincode" value="{{ old('pincode') }}"  placeholder="Enter Pincode:">
        @error('pincode')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>

<div class="col-lg-6 col-sm-6 col-12 DistributorFields">
    <div class="form-group">
        <label>Distributor Code *</label>
        <input type="text" class="form-control" value="{{ old('distributor_code') }}" name="distributor_code"
            placeholder="Enter Distributor Code:">
        @error('distributor_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>

<div class="col-lg-6 col-sm-6 col-12 SOFields">
    <div class="form-group">
        <label>SO Code *</label>
        <input type="text" class="form-control" value="{{ old('so_code') }}" name="so_code"
            placeholder="Enter ASM Code:">
        @error('so_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class="col-lg-6 col-sm-6 col-12 ASMFields">
    <div class="form-group">
        <label>ASM Code *</label>
        <input type="text" class="form-control" value="{{ old('asm_code') }}" name="asm_code"
            placeholder="Enter ASM Code:">
        @error('asm_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>
<div class="col-lg-6 col-sm-6 col-12 RSMFields">
    <div class="form-group">
        <label>RSM Code *</label>
        <input type="text" class="form-control" value="{{ old('rsm_code') }}" name="rsm_code"
            placeholder="Enter RSM Code:">
        @error('rsm_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>


<div class="col-lg-6 col-sm-6 col-12 RBDMFields">
    <div class="form-group">
        <label>RBDM Code *</label>
        <input type="text" class="form-control" value="{{ old('rbdm_code') }}" name="rbdm_code"
            placeholder="Enter RSM Code:">
        @error('rbdm_code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>




{{--  <div class="col-lg-6 col-sm-6 col-12 DSRFields">
    <div class="form-group">
        <label>DSR Code *</label>
        <input type="text" class="form-control" value="{{ old('dsr_Code') }}" name="dsr_Code"
            placeholder="Enter DSR Code:">
        @error('dsr_Code')
            <span class="validatin-error">{{ $message }}</span>
        @enderror
    </div>
</div>  --}}
