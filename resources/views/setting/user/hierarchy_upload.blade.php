<x-app-layout>
    <style>

    </style>
    <div>
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200">
            <div class="container mx-auto px-6 py-1 pb-16">
                <div id="ajax_message"></div>
                <div class="bg-white shadow-md rounded my-6 p-5">

                    <h2 class="text-2xl font-semibold mb-4">Upload Hierarchy </h2>
                    <form method="POST" id="user_hierarchy" action="{{ route('admin.hierarchy.hierarchy_upload_store') }}"
                        enctype="multipart/form-data">
                        @csrf
                        @method('post')
                        <div class="grid grid-cols-2 gap-4">
                            <div class="flex flex-col space-y-2">
                                <label for="hierarchy_file" class="text-gray-700 select-none font-medium">Upload
                                    file</label>
                                <input id="hierarchy_file" type="file" name="hierarchy_file"
                                    value="{{ old('hierarchy_file') }}" placeholder="Enter hierarchy_file"
                                    class="px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200" />
                                @error('hierarchy_file')
                                    <span class="validatin-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="mt-4 mb-4">
                            <button type="submit"
                                class="bg-blue-500 text-white font-bold px-5 py-1 rounded focus:outline-none shadow hover:bg-blue-500 transition-colors ">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function success_message(message) {
            return `<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline">${message}!</span>
            </div>`;
        }

        function error_message(message) {
            return `<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">${message}.</span>
            </div>`;
        }

        $('#user_hierarchy').on('submit', function(event) {
            event.preventDefault();
            showLoader()
            // Clear existing error messages
            $('#ajax_message').html('');

            var formData = new FormData();
            formData.append('hierarchy_file', $('#hierarchy_file')[0].files[0]);

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    hideLoader();
                    // Handle success response
                    $('#ajax_message').html(success_message(response.success));
                },
                error: function(xhr, status, error) {
                    hideLoader();
                    // Handle error response
                    if (xhr.status === 422 && xhr.responseJSON.error === "No file selected.") {
                        $('#ajax_message').html(error_message(xhr.responseJSON.error));
                    } else if (xhr.status === 422 && xhr.responseJSON.validation_error) {
                        var errors = xhr.responseJSON.validation_error;
                        // Clear existing error messages
                        $('.error-message').remove();
                        // Append the error messages after the parent container
                        errors.forEach(function(error) {
                            $('#ajax_message').append('<div class="error-message">' + error + '</div>');
                        });
                    } else {
                        $('#ajax_message').html(error_message(xhr.responseText));
                    }
                }
            });
        });


        // Show loader and overlay
function showLoader() {
    document.getElementById('loader-overlay').style.display = 'block';
}

// Hide loader and overlay
function hideLoader() {
    document.getElementById('loader-overlay').style.display = 'none';
}
    </script>


</x-app-layout>
