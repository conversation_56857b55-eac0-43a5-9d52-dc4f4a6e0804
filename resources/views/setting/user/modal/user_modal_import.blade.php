{{--  user import model   --}}
<div class="modal fade" id="uploadFileleModal" tabindex="-1"
     aria-labelledby="uploadFileleModalLabel" aria-hidden="true" data-bs-keyboard="false"
     data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content rounded shadow">
            <div class="modal-header">
                <h5 class="modal-title" id="modal_title">Upload User file</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="ajax_message"></div>
                <form method="POST" class="uploadfile-excle border rounded shadow p-3"
                      id="user_import_form" action="{{ isset($route) ? $route : route('admin.import.user_master_import') }}"
                      enctype="multipart/form-data">
                    @csrf
                    @method('post')
                    <input type="hidden" id="user_type" name="user_type" value="{{ $userType??''}}">
                    <input type="hidden" id="redirectRoute" name="redirectRoute" value="{{ $redirectRoute??''}}">
                    <div class="form-group ">
                        @php
                        $attribute= isset($file_name) ? $file_name : 'user_import_file';
                        @endphp
                        <input id="user_import_file" type="file" name="{{$attribute}}"
                               class="upload-input-file" accept=".xlsx, .xls, .csv,">
                        <label for="user_import_file" class="btn btn-tertiary js-labelFile">
                            <i class="icons fa fa-check"></i>
                            <span class="js-fileName">Choose a file</span>
                        </label>
                        <div class="d-flex my-2 justify-content-end">
                            <a class="sample-download text-decoration-underline" id="template_download">
                                <i class="fa fa-download me-2" aria-hidden="true"></i>
                                Sample Download</a>
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="modal-footer d-flex justify-content-end">
                        <button class="btn btn-secondary" data-bs-target="#createUserModalToggle"
                                data-bs-toggle="modal" data-bs-dismiss="modal">Cancel</button>
                        <button class="btn btn-primary swl-img-poup" type="submit">Submit</button>

                    </div>
                </form>

            </div>
        </div>
    </div>
</div>
{{-- End user import model   --}}
