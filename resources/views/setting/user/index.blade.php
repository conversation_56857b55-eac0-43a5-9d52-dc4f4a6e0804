<x-app-layout>
    @php
        $fromDate = request()->query('from_date');
        $toDate = request()->query('to_date');
        $userType = request()->query('user_type');
        $keyword = request()->query('keyword');
    @endphp
    <div class="page-wrapper">

        <div class="content">

            <div class="page-header">
                <div class="page-title">
                    <h4>User Creation </h4>
                    <h6>Master Cration/User Cration </h6>
                </div>
                <div>

                    {{--  <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#uploadFileleModal">
                        <i class="fa fa-upload" aria-hidden="true"></i> Upload Hierarchy
                    </button>  --}}
                    {{--  <button type="button" class="btn btn-primary my-2" data-bs-toggle="modal"
                        data-bs-target="#upload_history_view"><i class="fa fa-history" aria-hidden="true"></i>
                        View Upload Hierarchy History
                    </button>  --}}
                    @canany('AssetMasterCreationAccess')
                    <a class="btn btn-primary" href="{{ route('admin.users.create') }}">
                        <i class="fa fa-user" aria-hidden="true"></i>
                        Create New User
                    </a>
                    @endcanany
                </div>
            </div>
            <div class="card">
                @if (\Session::has('success'))
                    <div class="alert alert-success" role="alert">
                        {!! \Session::get('success') !!}
                    </div>
                @endif

                @if (\Session::has('error'))
                    <div class="alert alert-danger" role="alert">
                        {!! \Session::get('error') !!}
                    </div>
                @endif
                <div class="card-body">
                    <div class="table-top ">
                        <div class="search-set">
                            <form action="" method="GET">
                                <div class="form-group d-block d-md-flex d-lg-flex mb-0">
                                    <div class="d-flex">
                                        <div class="field">
                                            <label>Submit From Date</label>

                                            <div class="ui calendar" id="rangestart">
                                                <div class="ui input left icon">
                                                    <!-- <i class="calendar icon"></i> -->
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit From Date"
                                                        name="from_date" value="{{ $fromDate ?? '' }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field mx-2">
                                            <label>Submit To Date</label>
                                            <div class="ui calendar" id="rangeend">
                                                <div class="ui input left icon">
                                                    <!-- <i class="calendar icon"></i> -->
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit To Date" name="to_date"
                                                        value="{{ $toDate ?? '' }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" mx-2 form-group mb-0">
                                            <label>Select User </label>
                                            <select class="form-select select " name="user_type" id="user_type">
                                                @foreach ($roles as $role)
                                                    <option value="{{ $role->name }}"
                                                        data-hierarchy="{{ $role->hierarchy_view }}"
                                                        {{ old('roles') == $role->name || (empty($userType) && $role->name == 'SO') || $userType == $role->name ? 'selected' : '' }}>
                                                        {{ $role->name }}
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class=" mx-2 form-group mb-0">
                                            <label>Select name | UserCode </label>
                                            <input type="text" placeholder="serach name | userCode" name="keyword"
                                                value="{{ $keyword ?? '' }}">
                                        </div>
                                    </div>
                                    <div class="set-search-btn d-flex">
                                        <button type="submit" class="btn btn-primary py-2" data-bs-toggle="tooltip"
                                            data-bs-placement="top"><i class="fa fa-search" aria-hidden="true"></i>
                                        </button>
                                        <a class="btn btn-primary py-2 mx-2 "
                                            href="{{ route('admin.users.index', ['user_type' => request()->query('user_type')]) }}">Reset</a>

                                    </div>
                                </div>


                            </form>


                        </div>
                        <a class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top"
                            href="{{ route('admin.user_creation_report_download', [
                                'from_date' => request()->query('from_date') ?? '',
                                'to_date' => request()->query('to_date') ?? '',
                                'user_type' => request()->query('user_type') ?? '',
                                'keyword' => request()->query('keyword') ?? '',
                            ]) }}"
                            title="Download Excel">
                            <i class="fa fa-download me-2" aria-hidden="true"></i>Download
                        </a>



                    </div>
                    <div class="table-responsive ">
                        <!-- SO Creation Data -->
                        <table class="table user-so-table">
                            <thead>
                                <tr>

                                    {{--  <th>Sr No.</th>  --}}
                                    @foreach ($headerColumns as $column)
                                        <th>{{ $column }}</th>
                                    @endforeach
                                    @if ($user_type == 'SO' && $auth->user_type == 'SUPERADMIN')
                                        <th>Action</th>
                                    @endif
                                    {{--  <th>Action</th>  --}}
                                </tr>
                            </thead>
                            <tbody>
                                @if ($users->isNotEmpty())
                                    @foreach ($users as $key => $user)
                                        <tr>
                                            {{--  <td>{{ $key + 1 }}</td>  --}}
                                            {{-- Loop through header columns --}}
                                            @foreach ($dbColumns as $column)
                                                {{-- Check if column contains dot (indicating nested property) --}}
                                                @if (strpos($column, '.') !== false)
                                                    {{-- Extract nested property --}}
                                                    @php
                                                        $nestedProperties = explode('.', $column);
                                                        $value = $user;
                                                        foreach ($nestedProperties as $nestedProperty) {
                                                            $value = $value->{$nestedProperty} ?? '';
                                                        }
                                                    @endphp
                                                    <td>{{ $value }}</td>
                                                @else
                                                    {{-- Directly access property --}}
                                                    <td>{{ $user->$column ?? '' }}</td>
                                                @endif
                                            @endforeach
                                            @if ($user_type == 'SO' && $auth->user_type == 'SUPERADMIN')
                                                <td>
                                                    <a class="me-2"
                                                        href="{{ route('admin.user_edit', ['user_type' => $user_type, 'user_code' => $user->user_id]) }}">
                                                        <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                                    </a>
                                                </td>
                                            @endif
                                            {{--  <td></td>  --}}
                                            {{--  <td>
                                            <a class="me-2"
                                               href="{{ route('admin.users.show', ['user_type' => $user_type, 'user' => $user->id]) }}">
                                                <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                            </a>
                                        </td>  --}}
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="10">
                                            No data found!
                                        </td>

                                    </tr>
                                @endif
                            </tbody>
                        </table>

                    </div>
                    <div class="row btn-approve-reject">
                        {{-- <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start justify-content-md-start">
                            <button class="btn btn-primary">Approved</button>
                            <button class="btn btn-secondary mx-2">Reject</button>
                        </div> --}}
                        <div class="col-12 my-2">
                            @can('UserAccess')
                                <div class="text-right">
                                    {{--                                         {{ $users->links() }} --}}
                                    {!! $users->withQueryString()->links('pagination::bootstrap-5') !!}
                                </div>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{--  user import model   --}}
    @include('setting.user.modal.user_modal_import', [
        'file_name' => 'hierarchy_file',
        'route' => route('admin.hierarchy.hierarchy_upload_store'),
    ])


    {{--  user import History   --}}
    @include('setting.user.modal.userUploadHistory')

</x-app-layout>
