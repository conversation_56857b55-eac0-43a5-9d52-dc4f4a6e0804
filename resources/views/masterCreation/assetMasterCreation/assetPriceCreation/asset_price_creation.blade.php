<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Asset Price Creation</h4>
                    <h6>View/Asset Price Creation</h6>
                </div>
                <!-- <div class="page-btn">
                <a href="addcategory.php" class="btn btn-added">
                    <img src="assets/img/icons/plus.svg" class="me-1" alt="img">Add Category
                </a>
            </div> -->
            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            <button class="btn btn-secondary">Pending</button>
                            <button class="btn btn-primary mx-2">Approval Status</button>
                            <button class="btn btn-danger">Reject</button>
                        </div>
                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-sm-2">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{ asset('img/icons/search-white.svg') }}"
                                        alt="img"></a>
                            </div>
                            <div class="wordset">
                                <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="excel"><i class="fa fa-download me-2"
                                        aria-hidden="true"></i>Download</button>
                            </div>
                        </div>
                    </div>
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit From Date</label>
                                                <div class="ui calendar w-100" id="rangestart">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit From Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit To Date</label>
                                                <div class="ui calendar w-100" id="rangeend">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit To Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>DB Code</label>
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer code</label>
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request No.</label>
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>SO Name</label>
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Distributor Name</label>
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request Status</label>
                                            <select class="form-select select">
                                                <option>Task Pending</option>
                                                <option selected>Task Completed</option>
                                                <option value="?">Pending For ASM Approval</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>ASM Name</label>
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer City</label>
                                            <input type="text" class="form-control" placeholder="Customer City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Route Name</label>
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class=" col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">
                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="assets/img/icons/search-whites.svg" alt="img"></button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table  datanew">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Submit Date</th>
                                    <th>Request No</th>
                                    <th>Request type</th>
                                    <th>Task Status</th>
                                    <th>ASM Name</th>
                                    <th>ASM contact</th>
                                    <th>SO Name</th>
                                    <th>SO contact</th>
                                    <th>Applied Chiller Type</th>
                                    <th>Retailer Name</th>
                                    <th>Retailer Code</th>
                                    <th>Distributor Name</th>
                                    <th>Retailer City Name</th>
                                    <th>Retailer Address</th>
                                    <th>Retailer Contact </th>
                                    <th>Retailer pincode</th>
                                    <th>AE Approved Date</th>
                                    <th>Days After Approval</th>
                                    <th>Expected Deployment Date</th>
                                    <th>Assigned Organization </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>
                                        <!-- <a class="me-2 confirm-text" href="javascript:void(0);">
                                        <img src="assets/img/icons/delete.svg" alt="img">
                                    </a> -->
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>APR10006443</td>
                                    <td>New/Replacement/Transfer</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Bhaskar Baruah</td>
                                    <td></td>
                                    <td>Shyam Neopane</td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>S S</td>
                                    <td>1760857710_E000052</td>
                                    <td>Manipur Enterprises</td>
                                    <td>Imphal</td>
                                    <td>QWQV+FXG, Brahma</td>
                                    <td></td>
                                    <td></td>
                                    <td>2023-04-04 15:30:56</td>
                                    <td>303</td>
                                    <td>2024-01-30</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="master_creation/AssetMasterCreation/asset_price_creation.edit.php">
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">
                        <div class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start">
                            <button class="btn btn-primary">Approve</button>
                            <button class="btn btn-secondary mx-2">Reject</button>
                        </div>
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-md-end justify-content-sm-end justify-content-lg-end">
                            <nav aria-label="Page navigation example">
                                <ul class="pagination round-pagination  bx-pull-right">
                                    <li class="page-item"><a class="page-link" href="javascript:;">Previous</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;javascript:;">1</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="javascript:;">2</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">3</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <!-- Modal -->
                    <div class="modal fade" id="viewPlacementModal" tabindex="-1"
                        aria-labelledby="viewPlacementModalLabel" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewPlacementModalLabel">View Price Creation</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>CustomerCode:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CustomerName:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>AssetNumber:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>AssetBarcode:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ChillerTypeCode:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ChillerTypeName:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Region:</h4>
                                                                    <h6 class="manitoryblue">lorem50</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASMCode:</h4>
                                                                    <h6 class="manitoryblue">lorem50</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASMName:</h4>
                                                                    <h6 class="manitoryblue">lorem50</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SOCode:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SOName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CFA:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DistributorCode:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DistributorName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DSRCode:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>DSRName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RouteName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CustomerAddress:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>City:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>State:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>VPO(INR)Last3PAverage:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SOcontact:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RetailerContact:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customerclass:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customergroup:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CustomerChannel:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RequestNo.ofPlacement:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DateofPlacement:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Createdby(VendorID):</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Date&TimeofPlacement:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
