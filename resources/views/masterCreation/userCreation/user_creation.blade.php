<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>User Creation </h4>
                    <h6>MasterCration/UserCration </h6>
                </div>
                <div>
                    <a class="btn btn-primary" href="{{ route('master_creation.create_new_user') }}"><i class="fa fa-user"
                            aria-hidden="true"></i> Create New User</a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="table-top ">
                        <div class="search-set">
                            <form action="?">
                                <div class="form-group d-flex mb-0">
                                    <div class="field">
                                        <label>Submit From Date</label>
                                        <div class="ui calendar" id="rangestart">
                                            <div class="ui input left icon">
                                                <!-- <i class="calendar icon"></i> -->
                                                <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                <input type="text" placeholder="Submit From Date">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="field mx-2">
                                        <label>Submit To Date</label>
                                        <div class="ui calendar" id="rangeend">
                                            <div class="ui input left icon">
                                                <!-- <i class="calendar icon"></i> -->
                                                <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                <input type="text" placeholder="Submit To Date">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <div class=" mx-2 filter-table-user-select form-group mb-0">
                                <label>Select User Type</label>
                                <select class="form-select select " name="request_status" id="target_user_table">
                                    <option selected value="1">SO</option>
                                    <!-- <option value="2"> AE</option> -->
                                    <option value="3">ASM</option>
                                    <option value="4">RSM/RBDM</option>
                                    <option value="5">CFA</option>
                                    <option value="6"> DB</option>
                                    <option value="7">DSR</option>
                                </select>
                            </div>
                        </div>
                        <div class="wordset">
                            <button class="btn btn-primary mt-sm-4" data-bs-toggle="tooltip" data-bs-placement="top"
                                title="excel"><i class="fa fa-download me-2" aria-hidden="true"></i>Download</button>
                        </div>
                    </div>
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <select class="form-select select">
                                                <option>Request Status</option>
                                                <option>Fruits</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="Custo. City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <input type="text" name="daterange" value="01/01/2023 - 01/31/2024" />
                                        </div>
                                    </div>
                                    <div class="col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">

                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="assets/img/icons/search-whites.svg" alt="img"></button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive ">
                        <!-- SO Creation Data -->
                        <table class="table user-so-table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>

                                    <th>ASM Area Code</th>
                                    <th>ASM Name</th>
                                    <th>SO Teritory Code</th>
                                    <th>SO User Code</th>
                                    <th>SO Name</th>
                                    <th>Email Address</th>
                                    <th>Mobile Number </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>ASM-TT-Bihar</td>
                                    <td>Amartya Kumar</td>
                                    <td>SO-TT-Bhagalpur</td>
                                    <td>40518306</td>
                                    <td>Vacant_Bhagalpur</td>
                                    <td><EMAIL></td>
                                    <td>9666656500</td>
                                    <td>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#so_view_user">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                        <!-- asm Creation Data -->
                        <table class="table user-asm-table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>RSM Region Code</th>
                                    <th>ASM Area Code</th>
                                    <th>ASM Code</th>
                                    <th>ASM Name</th>
                                    <th>Email Address</th>
                                    <th>Mobile Number </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>ASM-TT-Bihar</td>
                                    <td>Amartya Kumar</td>
                                    <td>SO-TT-Bhagalpur</td>
                                    <td>Vacant_Bhagalpur</td>
                                    <td><EMAIL></td>
                                    <td>9666656500</td>
                                    <td>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#asm_view_user">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                        <!-- rsm_rbdm Creation Data -->
                        <table class="table user-rsm_rbdm-table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Region Name</th>
                                    <th>RSM/RBDM Code</th>
                                    <th>RSM/RBDM Region Code</th>
                                    <th>RSM/RBDM Name</th>
                                    <th>Email Address</th>
                                    <th>Mobile Number </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>ASM-TT-Bihar</td>
                                    <td>Amartya Kumar</td>
                                    <td>SO-TT-Bhagalpur</td>
                                    <td>Vacant_Bhagalpur</td>
                                    <td><EMAIL></td>
                                    <td>9666656500</td>
                                    <td>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#rsm_rbdm_view_user">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                        <!-- cfa Creation Data -->
                        <table class="table user-cfa-table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>CFA/Plant Code Code</th>
                                    <th>Business Name</th>
                                    <th>Registered Name</th>
                                    <th>Register Number</th>
                                    <th>Address 1</th>
                                    <th>Address 2</th>
                                    <th>City</th>
                                    <th>State</th>
                                    <th>Country</th>
                                    <th>Pin Code</th>
                                    <th>Effective From</th>
                                    <th>Effective To</th>
                                    <th>GST No</th>
                                    <th>Email Address</th>
                                    <th>Mobile Number </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td></td>
                                    <td>Dayal Enterprises</td>
                                    <td>MARS INTERNATIONAL INDIA PRIVATE LIMITED</td>
                                    <td></td>
                                    <td>Khasra no. 299, Village accheja, Dadri road</td>
                                    <td>Gautam Buddha Nagar, Uttar Pradesh, 203287</td>
                                    <td>Ghaziabad</td>
                                    <td>Uttarpradesh</td>
                                    <td>India</td>
                                    <td>201003</td>
                                    <td>25-Apr-23</td>
                                    <td></td>
                                    <td>09AAACE6794J1Z4</td>
                                    <td><EMAIL></td>
                                    <td>9666656500</td>
                                    <td>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#cfa_view_user">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                        <!-- db Creation Data -->
                        <table class="table user-db-table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>SO Teritory Code</th>
                                    <th>CHANNEL</th>
                                    <th>CFA Code</th>
                                    <th>Distributor Code</th>
                                    <th>Distributor Name</th>
                                    <th>City</th>
                                    <th>Email Address</th>
                                    <th>Mobile Number </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>SO-TT-Bhagalpur</td>
                                    <td>10 - TT</td>
                                    <td>IN74</td>
                                    <td>1761261310</td>
                                    <td>MJ Traders</td>
                                    <td>Noida</td>
                                    <td><EMAIL></td>
                                    <td>9666656500</td>
                                    <td>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewPlacementModal">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                        <!-- dsr Creation Data -->
                        <table class="table user-dsr-table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>SO Teritory Code</th>
                                    <th>DSR Code</th>
                                    <th>DSR Name</th>
                                    <th>Email Address</th>
                                    <th>Mobile Number </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>SO-TT-Bhagalpur</td>
                                    <td></td>
                                    <td></td>
                                    <td><EMAIL></td>
                                    <td>9666656500</td>
                                    <td>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#dsr_view_user">
                                            <img src="assets/img/icons/eye.svg" alt="img">
                                        </a>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start justify-content-md-start">
                            <button class="btn btn-primary">Approved</button>
                            <button class="btn btn-secondary mx-2">Reject</button>
                        </div>
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-md-end justify-content-sm-end justify-content-lg-end">
                            <nav aria-label="Page navigation example">
                                <ul class="pagination round-pagination  bx-pull-right">
                                    <li class="page-item"><a class="page-link" href="javascript:;">Previous</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;javascript:;">1</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="javascript:;">2</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">3</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <!-- SO-view-modal Data -->
                    <div class="modal fade" id="so_view_user" tabindex="-1" aria-labelledby="so_view_userLabel"
                        data-bs-keyboard="false" data-bs-backdrop="static" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="so_view_userLabel">View Create SO User Data
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-12 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>ASM Area Code:</h4>
                                                                    <h6>ASM-TT-Bihar
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Name:</h4>
                                                                    <h6>Amartya Kumar
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Teritory Code:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO User Code:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Name:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Email Address:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Mobile Number:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Create At Date&Time:</h4>
                                                                    <h6>01/04/2024 10:48 AM</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- asm-view-modal Data -->
                    <div class="modal fade" id="asm_view_user" tabindex="-1" aria-labelledby="asm_view_userLabel"
                        data-bs-keyboard="false" data-bs-backdrop="static" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="asm_view_userLabel">View Create ASM User Data
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-12 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>RSM Region Code:</h4>
                                                                    <h6>ASM-TT-Bihar</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Area Code:</h4>
                                                                    <h6>ASM-TT-Bihar</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Name:</h4>
                                                                    <h6>Amartya Kumar</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Code:</h4>
                                                                    <h6>SO-TT-Bhagalpur</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Email Address:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Mobile Number:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Create At Date&Time:</h4>
                                                                    <h6>01/04/2024 10:48 AM</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- rsm_rbdm-view-modal Data -->
                    <div class="modal fade" id="rsm_rbdm_view_user" tabindex="-1"
                        aria-labelledby="rsm_rbdm_view_userLabel" data-bs-keyboard="false" data-bs-backdrop="static"
                        aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="rsm_rbdm_view_userLabel">View Create RSM/RBDM User
                                        Data
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-12 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>Region Name:</h4>
                                                                    <h6>ASM-TT-Bihar</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RSM/RBDM Code:</h4>
                                                                    <h6>Amartya Kumar
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RSM/RBDM Region Code:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RSM/RBDM Name:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Email Address:</h4>
                                                                    <h6 class="manitoryblue"><EMAIL></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Mobile Number:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Create At Date&Time:</h4>
                                                                    <h6>01/04/2024 10:48 AM</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- cfa-view-modal Data -->
                    <div class="modal fade" id="cfa_view_user" tabindex="-1" aria-labelledby="cfa_view_userLabel"
                        data-bs-keyboard="false" data-bs-backdrop="static" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered  modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="cfa_view_userLabel">View Create CFA User Data
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>CFA/Plant Code Code:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Registered Name:</h4>
                                                                    <h6>MARS INTERNATIONAL INDIA PRIVATE LIMITED</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Address 1:</h4>
                                                                    <h6>Khasra no. 299, Village accheja, Dadri road</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>City:</h4>
                                                                    <h6>Ghaziabad</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Country:</h4>
                                                                    <h6>India</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Effective From:</h4>
                                                                    <h6>25-Apr-23</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>GST No:</h4>
                                                                    <h6>09AAACE6794J1Z4</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>Mobile Number:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>

                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>Business Name:</h4>
                                                                    <h6>Dayal Enterprises</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Register Number:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Address 2:</h4>
                                                                    <h6>Gautam Buddha Nagar, Uttar Pradesh, 203287</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>State:</h4>
                                                                    <h6>Uttarpradesh</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Pin Code:</h4>
                                                                    <h6>201003</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Effective To:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Email Address:</h4>
                                                                    <h6 class="manitoryblue"><EMAIL></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Create At Date&Time:</h4>
                                                                    <h6>01/04/2024 10:48 AM</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- db-view-modal Data -->
                    <div class="modal fade" id="db_view_user" tabindex="-1" aria-labelledby="db_view_userLabel"
                        data-bs-keyboard="false" data-bs-backdrop="static" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="db_view_userLabel">View Create RSM/RBDM User Data
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-12 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>SO Teritory Code:</h4>
                                                                    <h6>SO-TT-Bhagalpur</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CHANNEL:</h4>
                                                                    <h6>10 - TT</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CFA Code:</h4>
                                                                    <h6>IN74</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Code:</h4>
                                                                    <h6>1761261310</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Name:</h4>
                                                                    <h6>MJ Traders</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>City:</h4>
                                                                    <h6>Noida</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Email Address:</h4>
                                                                    <h6 class="manitoryblue"><EMAIL></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Mobile Number:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Create At Date&Time:</h4>
                                                                    <h6>01/04/2024 10:48 AM</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- dsr-view-modal Data -->
                    <div class="modal fade" id="dsr_view_user" tabindex="-1" aria-labelledby="dsr_view_userLabel"
                        data-bs-keyboard="false" data-bs-backdrop="static" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="dsr_view_userLabel">View Create DSR User Data
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-12 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>SO Teritory Code:</h4>
                                                                    <h6>SO-TT-Bhagalpur</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DSR Code:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DSR Name:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Email Address:</h4>
                                                                    <h6 class="manitoryblue"><EMAIL></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Mobile Number:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Create At Date&Time:</h4>
                                                                    <h6>01/04/2024 10:48 AM</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</x-app-layout>
