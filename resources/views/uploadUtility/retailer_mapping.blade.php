<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Asset Retailer Mapping Upload</h4>
                    <h6>View/Search Report</h6>
                </div>
                <div class="col-md-6 col-sm-6 d-flex align-center justify-content-end page-btn uploader-file">
                    <!-- Button trigger modal -->
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#uploadFileleModal">
                        <i class="fa fa-upload" aria-hidden="true"></i> Upload File
                    </button>

                    <!-- Modal -->
                    <div class="modal fade" id="uploadFileleModal" tabindex="-1"
                        aria-labelledby="uploadFileleModalLabel" aria-hidden="true" data-bs-keyboard="false"
                        data-bs-backdrop="static">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content rounded shadow">
                                <div class="modal-header">
                                    <h5 class="modal-title">Upload User file</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form action="?" class="uploadfile-excle border rounded shadow p-3">
                                        <div class="form-group ">
                                            <input type="file" name="file" id="file"
                                                class="upload-input-file" accept=".xlsx, .xls, .csv,">
                                            <label for="file" class="btn btn-tertiary js-labelFile">
                                                <i class="icons fa fa-check"></i>
                                                <span class="js-fileName">Choose a file</span>
                                            </label>
                                            <div class="d-flex my-2 justify-content-end">
                                                <a class="sample-download text-decoration-underline">
                                                    <i class="fa fa-download me-2" aria-hidden="true"></i>
                                                    Sample Download</a>
                                            </div>
                                        </div>
                                        <hr class="my-2">
                                        <div class="modal-footer d-flex justify-content-end">
                                            <button class="btn btn-secondary" data-bs-target="#createUserModalToggle"
                                                data-bs-toggle="modal" data-bs-dismiss="modal">Cancel</button>
                                            <button class="btn btn-primary">Submit</button>

                                        </div>
                                    </form>

                                    <div class="sample-formet-download">

                                        <div class="table-responsive">
                                            <h5 class="modal-title text-center my-3">Dawonload Sample File Formet
                                            </h5>
                                            <hr>
                                            <table class="table  ">
                                                <thead>
                                                    <tr>
                                                        <th>Region</th>
                                                        <th>RSM Name</th>
                                                        <th>ASM Area Code</th>
                                                        <th>ASM Name</th>
                                                        <th>SO TTY.Code</th>
                                                        <th>SO Name</th>
                                                        <th>CFA Code</th>
                                                        <th>CFA Name</th>
                                                        <th>Ware House code</th>
                                                        <th>Distributor Code</th>
                                                        <th>Distributor Name</th>
                                                        <th>Salesman Code</th>
                                                        <th>Salesman Name</th>
                                                        <th>Route Name</th>
                                                        <th>Retailer code</th>
                                                        <th>Retailer Name</th>
                                                        <th>Class Name</th>
                                                        <th>Retailer Channel</th>
                                                        <th>Retailer Subchannel</th>
                                                        <th>Retailer Category</th>
                                                        <th>Contact Person</th>
                                                        <th>Customer Address</th>
                                                        <th>City</th>
                                                        <th>State</th>
                                                        <th>Asset Number</th>
                                                        <th>Asset Barcode</th>
                                                        <th>Chiller Type Code</th>
                                                        <th>Chiller Type Name</th>
                                                        <th>VPO (INR) 3P Last Average</th>
                                                        <th>Request No. of Placement</th>
                                                        <th>Outlets creation date</th>
                                                        <th>Date of Placement</th>
                                                        <th>Pin Code</th>
                                                        <th>Created by</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>West</td>
                                                        <td>Saurabh Sharma</td>
                                                        <td>ASM-TT-Gr MUMBAI &amp; Goa</td>
                                                        <td>Sandip Sangale</td>
                                                        <td>SO-TT-Navi Mumbai</td>
                                                        <td>Devender Puri</td>
                                                        <td>IN91</td>
                                                        <td>TCI Cold Chain solutions Ltd</td>
                                                        <td></td>
                                                        <td>1758449110</td>
                                                        <td>Laxmi Traders</td>
                                                        <td>DSR_000027</td>
                                                        <td>Rahul Gaikwad</td>
                                                        <td>Belapur - Goan</td>
                                                        <td>RETN_003256</td>
                                                        <td>Chandan Chemist</td>
                                                        <td>Platinum</td>
                                                        <td>Other Service</td>
                                                        <td>Retailer</td>
                                                        <td>Chemist</td>
                                                        <td>abc</td>
                                                        <td>8SANEGURUJIMARG</td>
                                                        <td>Mumbai</td>
                                                        <td>Maharashtra</td>
                                                        <td>1500002415</td>
                                                        <td>M0002415</td>
                                                        <td>CT004</td>
                                                        <td>30 LTR</td>
                                                        <td>0</td>
                                                        <td></td>
                                                        <td>12/17/22</td>
                                                        <td></td>
                                                        <td></td>
                                                        <td></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="upload-history">
                        <button type="button" class="btn btn-primary ms-2" data-bs-toggle="modal"
                            data-bs-target="#upload_history_view"> <i class="fa fa-history" aria-hidden="true"></i>
                            Upload History
                        </button>

                        <!-- Modal -->
                        <div class="modal fade" id="upload_history_view" tabindex="-1"
                            aria-labelledby="upload_history_viewLabel" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-dialog modal-xl">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">View Upload History File</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                            aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="table-responsive shadow border p-2">
                                            <table class="table  ">
                                                <thead>
                                                    <tr>
                                                        <th> UPLOADED DATE &amp; TIME</th>
                                                        <th>TOTAL RECORDS</th>
                                                        <th>INCORRECT RECORDS</th>
                                                        <th>SUCCESS RECORDS </th>
                                                        <th>STATUS</th>
                                                        <th>UPLOAD TYPE</th>
                                                        <th>ACTION</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>2023-03-14 3:15PM</td>
                                                        <td>5</td>
                                                        <td>4</td>
                                                        <td>2</td>
                                                        <td>Success</td>
                                                        <td>Test.xlsx</td>
                                                        <td> <button class="btn btn-success"> <i
                                                                    class="fa fa-download me-2" aria-hidden="true"></i>
                                                                Download</button></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="modal-footer justify-content-center">
                                        <button type="button" class="btn btn-secondary"
                                            data-bs-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            <button class="btn btn-secondary">Pending</button>
                            <button class="btn btn-primary mx-2">Approval Status</button>
                            <button class="btn btn-danger">Reject</button>
                        </div>
                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-sm-2">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{ asset('img/icons/search-white.svg') }}"
                                        alt="img"></a>
                            </div>
                            <div class="wordset">
                                <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="excel"><i class="fa fa-download me-2"
                                        aria-hidden="true"></i>Download</button>
                                <!-- <ul>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                                src="{{ asset('img/icons/pdf.svg') }}" alt="img"></a>
                                    </li>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                                src="{{ asset('img/icons/excel.svg') }}" alt="img"></a>
                                    </li>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                                src="{{ asset('img/icons/printer.svg') }}" alt="img"></a>
                                    </li>
                                </ul> -->
                            </div>
                        </div>
                    </div>
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit From Date</label>
                                                <div class="ui calendar w-100" id="rangestart">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon"
                                                            aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit From Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit To Date</label>
                                                <div class="ui calendar w-100" id="rangeend">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon"
                                                            aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit To Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>DB Code</label>
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer code</label>
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request No.</label>
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>SO Name</label>
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Distributor Name</label>
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request Status</label>
                                            <select class="form-select select">
                                                <option>Task Pending</option>
                                                <option selected>Task Completed</option>
                                                <option value="?">Pending For ASM Approval</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>ASM Name</label>
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer City</label>
                                            <input type="text" class="form-control" placeholder="Customer City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Route Name</label>
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class=" col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">
                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="assets/img/icons/search-whites.svg" alt="img"></button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table  datanew">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Region</th>
                                    <th>RSM Name</th>
                                    <th>ASM Area Code</th>
                                    <th>ASM Name</th>
                                    <th>SO TTY.Code</th>
                                    <th>SO Name</th>
                                    <th>CFA Code</th>
                                    <th>CFA Name</th>
                                    <th>Ware House code</th>
                                    <th>Distributor Code</th>
                                    <th>Distributor Name</th>
                                    <th>Salesman Code</th>
                                    <th>Salesman Name</th>
                                    <th>Route Name</th>
                                    <th>Retailer code</th>
                                    <th>Retailer Name</th>
                                    <th>Class Name</th>
                                    <th>Retailer Channel</th>
                                    <th>Retailer Subchannel</th>
                                    <th>Retailer Category</th>
                                    <th>Contact Person</th>
                                    <th>Customer Address</th>
                                    <th>Pin Code</th>
                                    <th>City</th>
                                    <th>State</th>
                                    <th>Asset Number</th>
                                    <th>Asset Barcode</th>
                                    <th>Chiller Type Code</th>
                                    <th>Chiller Type Name</th>
                                    <th>VPO (INR) 3P Last Average</th>
                                    <th>Request No. of Placement</th>
                                    <th>Outlets creation date</th>
                                    <th>Date of Placement</th>
                                    <th>Created by</th>
                                    <th>Created Time</th>
                                    <th>Updated by</th>
                                    <th>Updated Time</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>West</td>
                                    <td>Saurabh Sharma</td>
                                    <td>ASM-TT-Gr MUMBAI &amp; Goa</td>
                                    <td>Sandip Sangale</td>
                                    <td>SO-TT-Navi Mumbai</td>
                                    <td>Devender Puri</td>
                                    <td>IN91</td>
                                    <td>TCI Cold Chain solutions Ltd</td>
                                    <td></td>
                                    <td>1758449110</td>
                                    <td>Laxmi Traders</td>
                                    <td>DSR_000027</td>
                                    <td>Rahul Gaikwad</td>
                                    <td>Belapur - Goan</td>
                                    <td>RETN_003256</td>
                                    <td>Chandan Chemist</td>
                                    <td>Platinum</td>
                                    <td>Other Service</td>
                                    <td>Retailer</td>
                                    <td>Chemist</td>
                                    <td>abc</td>
                                    <td>8SANEGURUJIMARG</td>
                                    <td></td>
                                    <td>Mumbai</td>
                                    <td>Maharashtra</td>
                                    <td>1500002415</td>
                                    <td>M0002415</td>
                                    <td>CT004</td>
                                    <td>30 LTR</td>
                                    <td>0</td>
                                    <td></td>
                                    <td>12/17/22</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class=" btn-approve-reject my-2">
                        <div class="d-flex justify-content-center justify-content-sm-end justify-content-lg-end">
                            <nav aria-label="Page navigation example">
                                <ul class="pagination round-pagination  bx-pull-right">
                                    <li class="page-item"><a class="page-link" href="javascript:;">Previous</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;javascript:;">1</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="javascript:;">2</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">3</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</x-app-layout>
