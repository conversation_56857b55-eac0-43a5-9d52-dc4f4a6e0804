<x-app-layout>
    <style>
        .active_class {
            background: rgb(1, 215, 185) !important;
            border-color: transparent !important;
            color: rgb(255, 255, 255) !important;
        }
    </style>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>CFA Wise Inventory Upload</h4>
                    <h6>View/Search Report</h6>
                </div>
                <div class="col-md-6 col-sm-6 d-flex align-center justify-content-end page-btn uploader-file">
                    <!-- Button trigger modal -->
                    @if ($user->user_type == 'AE' || $user->user_type == 'VENDOR')
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#uploadFileleModal">
                            <i class="fa fa-upload" aria-hidden="true"></i> Upload File </button>
                        <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                            title="excel"><i class="fa fa-download me-2" aria-hidden="true"></i>Download</button>
                    @endif

                    <!-- Modal -->
                    {{--  user import model   --}}
                    @include('setting.user.modal.user_modal_import', [
                        'userType' => 'asset_inventory_upload',
                        'redirectRoute' => route('upload_utility.cfa_wise_inventory'),
                    ])
                </div>

            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class=" row table-top ">
                    <div class="card">
                        <div class="card-body">
                            {{--  <div id="error-message" class="text-center alert alert-danger" style="display: none;">
                            Form for selected role does not exist
                        </div>  --}}
                            <div class="page-title">
                                <h6>Latest Upload History </h6><br>
                            </div>
                            <div class="table-responsive shadow border p-2">
                                <table class="table  ">
                                    <thead>
                                        <tr>
                                            <th>BatchID</th>
                                            <th> UPLOADED DATE &amp; TIME</th>
                                            <th>TOTAL RECORDS</th>
                                            <th>INCORRECT RECORDS</th>
                                            <th>SUCCESS RECORDS </th>
                                            <th>STATUS</th>
                                            <th>UPLOAD TYPE</th>
                                            {{--  <th>UPLOAD BY</th>  --}}
                                            {{--  <th>Client IP</th>  --}}
                                            <th>ACTION</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (!empty($uploadHistory) && $uploadHistory->count() > 0)
                                            @php
                                                $ErrorLogCount = $uploadHistory->assetLog->count();
                                                $successCount = $uploadHistory->totalRecordscount - $ErrorLogCount;
                                            @endphp
                                            <tr>
                                                <td>{{ $uploadHistory->batchID ?? '' }}</td>
                                                <td>{{ $uploadHistory->uploadDateTime ?? '' }}</td>
                                                <td>{{ $uploadHistory->totalRecordscount ?? 0 }}</td>
                                                <td>{{ $ErrorLogCount ?? 0 }}</td>
                                                <td>{{ $successCount ?? 0 }}</td>
                                                <td>Success</td>
                                                <td>{{ $uploadHistory->uploadType ?? '' }}</td>
                                                {{--  <td>{{ $uploadHistory->uploadByName??''}}</td>  --}}
                                                {{--  <td>{{ $uploadHistory->requestIP??''}}</td>  --}}
                                                <td>
                                                    @if ($successCount == $uploadHistory->totalRecordscount)
                                                        Completed
                                                    @else
                                                        <a
                                                            href="{{ route('admin.import.batch_upload_history_download', ['batchID' => $uploadHistory->batchID]) }}">
                                                            <button class="btn btn-primary"> <i class="fa fa-download me-2"
                                                                    aria-hidden="true"></i>
                                                                Download</button>
                                                        </a>
                                                    @endif
                                                </td>
                                            </tr>
                                        @else
                                            <tr>
                                                <td colspan="8" style="text-align: center;">No Upload History available
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                    @php
                        $status = request()->query('status') ?? 'all';
                    @endphp
                    <div class="search-set">
                        <form action="" method="GET">

                            <div class="form-group d-block d-md-flex d-lg-flex mb-0">
                                <div class="d-flex">
                                    <div class=" mx-3 form-group mb-0" style="min-width: 250px !important;">
                                        <label>Asset Type </label>
                                        <select id="chiller_type" class="form-select" name="act">
                                            <option value="{{ encrypt('all') }}">All</option>
                                            @if (!empty($assetChillerType))
                                                @foreach ($assetChillerType as $chillerType)
                                                    @php
                                                        $chiller = request()->query('act') ? decrypt(request()->query('act')) : '';
                                                        $selected = $chiller == $chillerType;
                                                    @endphp
                                                    <option value="{{ encrypt($chillerType) }}" {{ $selected ? 'selected' : '' }}>
                                                        {{ $chillerType }}
                                                    </option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>



                                    <div class=" mx-3 form-group mb-0" style="min-width: 250px !important;">
                                        <label>Warehouse Code </label>
                                        <select id="warehouse_code" class="form-select" name="warehouse_code">
                                            <option value="all">All</option>
                                            @if (!empty($warehouse_code))
                                                @foreach ($warehouse_code as $wc)
                                                    @php
                                                        $chiller = request()->query('warehouse_code') ? request()->query('warehouse_code') : '';
                                                        $selected = $chiller == $wc;
                                                    @endphp
                                                    <option value="{{ $wc }}" {{ $selected ? 'selected' : '' }}>
                                                        {{ $wc }}
                                                    </option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                    <div class=" mx-2 form-group mb-0">
                                        <label>Keyword (Asset Serial Number ,Barcode)</label>
                                        <input type="text" placeholder="Search .." name="keyword"
                                            value="{{ request()->keyword ?? '' }}">
                                    </div>
                                </div>
                                <div class="set-search-btn d-flex">
                                    <button type="submit" class="btn btn-primary py-2" data-bs-toggle="tooltip"
                                        data-bs-placement="top"><i class="fa fa-search" aria-hidden="true"></i>
                                    </button>
                                    <a class="btn btn-primary py-2 mx-2 "
                                        href="{{ route('upload_utility.cfa_wise_inventory') }}">Reset</a>

                                </div>
                            </div>


                        </form>


                    </div>

                </div>
                @if (\Session::has('success'))
                    <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                        <strong>Success!</strong> {!! \Session::get('success') !!}
                    </div>
                @endif

                @if (\Session::has('error'))
                    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                        {!! \Session::get('error') !!}
                    </div>
                @endif
                <div class="ajax_message"></div>
                <div class="table-responsive">
                    <div class="page-title">
                        <h6>CFA Wise Inventory Upload</h6><br>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                @if ($user->user_type == 'AE'|| $user->user_type == 'VENDOR')
                                <th>
                                    <label class="checkboxs">
                                        <input type="checkbox" id="select-all" class="approvalCheckbox">
                                        <span class="checkmarks"></span>
                                    </label>
                                </th>
                                @endif
                                <th>AssetDescription</th>
                                <th>AssetType</th>
                                <th>Asset Status</th>
                                <th>Asset Price</th>
                                <th>ModelName</th>
                                <th>Vendor</th>
                                <th>Quantity</th>
                                <th>WarehouseCode</th>
                                <th>ManufacturedYear</th>
                                <th>Asset Serial Number</th>
                                <th>Asset Barcode</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (!empty($inventoryList) && $inventoryList->count() > 0)
                                @foreach ($inventoryList as $inventory)
                                    @php
                                        $assetStatus =
                                            $inventory->asset_approval_status == 'pending'
                                                ? 'Pending for AE approval '
                                                : ($inventory->asset_approval_status == 'approved'
                                                    ? 'Asset Approved'
                                                    : 'Asset Rejected');
                                    @endphp
                                    <tr>
                                        @if ($user->user_type == 'AE'|| $user->user_type == 'VENDOR')
                                        <td>
                                            <label class="checkboxs ">
                                                @php
                                                    $disabled =
                                                        $user->user_type == 'AE' &&
                                                        $inventory->asset_approval_status == 'pending'
                                                            ? ''
                                                            : 'disabled';
                                                @endphp

                                                <input type="checkbox" name="" class="approvalCheckbox"
                                                    value="{{ $inventory->id ?? '' }}" {{ $disabled }}>
                                                <span class="checkmarks"></span>
                                            </label>
                                        </td>
                                        @endif

                                        <td>{{ $inventory->description ?? '' }}</td>
                                        <td>{{ $inventory->asset_type ?? '' }}</td>
                                        <td>{{ $assetStatus ?? '' }}</td>
                                        <td>{{ $inventory->asset_price ?? '' }}</td>
                                        <td>{{ $inventory->model_name ?? '' }}</td>
                                        <td>{{ $inventory->vendor ?? '' }}</td>
                                        <td>{{ $inventory->quantity ?? '' }}</td>
                                        <td>{{ $inventory->warehouse_code ?? '' }}</td>
                                        <td>{{ $inventory->manufactured_year ?? '' }}</td>
                                        <td>{{ $inventory->serial_number ?? '' }}</td>
                                        <td>{{ $inventory->barcode ?? '' }}</td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="11" style="text-align: center;">No Data available</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
                <div class="row btn-approve-reject">

                    @if ($user->user_type == 'AE')
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start">
                            <button class="btn btn-primary approvalStatusBtn" data-status='Approved'>Approve</button>
                            <button class="btn btn-secondary mx-2 approvalStatusBtn"
                                data-status='Rejected'>Reject</button>
                        </div>
                    @endif
                    <div class="col-12 my-2">
                        <div class="text-right">
                            {!! $inventoryList->withQueryString()->links('pagination::bootstrap-5') !!}
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
    </div>
    <script>
        $(document).ready(function() {
            $(".status_filter").click(function() {
                let status = $(this).data("status");
                let urlParams = new URLSearchParams(window.location.search);
                urlParams.set('status', status);
                window.location.search = urlParams.toString();
            });

            $('#chiller_type').change(function() {
                var selectedType = $(this).val();
                var currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('act', selectedType);
                window.location.href = currentUrl.toString();
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            function approlalData(selectedIds, status) {
                $('.ajax_message').html('');
                $('.validatin-error').remove();
                if (selectedIds.length > 0) {
                    showLoader();


                    // Prompt for rejection reason if status is reject
                    if (status == 'Rejected') {
                        var rejectionReason = prompt('Please enter the reason for rejection:');
                        if (rejectionReason === null || rejectionReason === "") {
                            // If rejection reason is empty or cancelled, do not proceed
                            hideLoader();
                            return;
                        }
                    }

                    $.ajax({
                        url: "{{ route('upload_utility.asset_inventory_approval') }}",
                        method: 'POST',
                        dataType: 'JSON',
                        data: {
                            selectedIds: selectedIds,
                            status: status,
                            rejection_reason: (status == 'Rejected') ? rejectionReason : null
                        },
                        success: function(response) {
                            hideLoader();
                            if (response.success) {
                                $('.ajax_message').html(success_message(response
                                    .success));
                                $('html, body').scrollTop(0);
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                $('.ajax_message').html(error_message(response
                                    .error));
                                $('html, body').scrollTop(0);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log("headewr", xhr
                                .responseJSON);
                            hideLoader();
                            $('.ajax_message').html('Error: ' + error_message(xhr
                                .responseJSON.error));
                            $('html, body').scrollTop(0);
                        }
                    });

                } else {
                    $('.ajax_message').html(error_message('Please select at least one row.'));
                    $('html, body').scrollTop(0);
                    setTimeout(function() {
                        $('.ajax_message').html('');
                    }, 4000);
                    return false;

                }
            }
            $('.approvalStatusBtn').click(function(e) {
                let selectedIds = [];
                let status = $(this).attr('data-status');
                $('.approvalCheckbox:checked').each(function() {
                    selectedIds.push($(this).val());
                });
                approlalData(selectedIds, status);
                // Send AJAX request to Laravel route
            });
        });
    </script>
</x-app-layout>
