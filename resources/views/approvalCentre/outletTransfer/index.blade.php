<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>AB Outlet Asset Transfer</h4>
                    <h6>View/AB Outlet Asset Transfer</h6>
                </div>

            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            <button class="btn btn-secondary">Pending</button>
                            <button class="btn btn-primary mx-2">Approval Status</button>
                            <button class="btn btn-danger">Reject</button>
                        </div>
                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-sm-2">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{ asset('img/icons/search-white.svg') }}"
                                        alt="img"></a>
                            </div>
                            <div class="wordset">
                                <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="excel"><i class="fa fa-download me-2"
                                        aria-hidden="true"></i>Download</button>
                                <!-- <ul>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                                src="{{ asset('img/icons/pdf.svg') }}" alt="img"></a>
                                    </li>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                                src="{{ asset('img/icons/excel.svg') }}" alt="img"></a>
                                    </li>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                                src="{{ asset('img/icons/printer.svg') }}" alt="img"></a>
                                    </li>
                                </ul> -->
                            </div>
                        </div>
                    </div>
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit From Date</label>
                                                <div class="ui calendar w-100" id="rangestart">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit From Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit To Date</label>
                                                <div class="ui calendar w-100" id="rangeend">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit To Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>DB Code</label>
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer code</label>
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request No.</label>
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>SO Name</label>
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Distributor Name</label>
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request Status</label>
                                            <select class="form-select select">
                                                <option>Task Pending</option>
                                                <option selected>Task Completed</option>
                                                <option value="?">Pending For ASM Approval</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>ASM Name</label>
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer City</label>
                                            <input type="text" class="form-control" placeholder="Customer City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Route Name</label>
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class=" col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">
                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="{{ asset('img/icons/search-whites.svg') }}"
                                                    alt="img"></button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table  datanew">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Region</th>
                                    <th>ASM Territory</th>
                                    <th>SO Territory</th>
                                    <th>CFA</th>
                                    <th>Submit Date</th>
                                    <th>Request No</th>
                                    <th>Pullout Outlet code</th>
                                    <th>Placement Outlet code</th>
                                    <th>Applied Chiller Type</th>
                                    <th>Request Status</th>
                                    <th>Approval Type</th>
                                    <th>Current VPO</th>
                                    <th>Expected VPO</th>
                                    <th>ASM Name</th>
                                    <th>SO Name</th>
                                    <th>Distributor Name</th>
                                    <th>Distributor Code</th>
                                    <th>Route Name</th>
                                    <th>Customer Code</th>
                                    <th>Customer Name</th>
                                    <th>Customer City Name</th>
                                    <th>Customer Address</th>
                                    <th>Pin Code</th>
                                    <th>Customer contact</th>
                                    <th>Remarks</th>
                                    <th>Deployment date</th>

                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>AC</td>
                                    <td>ASM-AC-South</td>
                                    <td>ASM-AC-Dehradun</td>
                                    <td>Delhi+NCR+LKN</td>
                                    <td>2024-01-29</td>
                                    <td>ARR10000109</td>
                                    <td></td>
                                    <td></td>
                                    <td>280 L</td>
                                    <td>Pending for ASM Approval</td>
                                    <td>Exceptional</td>
                                    <td>0.00</td>
                                    <td>2500.00</td>
                                    <td>Puja Ghosh</td>
                                    <td>Abhishek</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>RETN_793507</td>
                                    <td>Mahadev Enterprises</td>
                                    <td>Haridwar</td>
                                    <td>Haridwar</td>
                                    <td></td>
                                    <td></td>
                                    <td>Outlet closure</td>
                                    <td>2024-01-29 17:06:09</td>

                                    <td>
                                        <a class="me-2" href="ApprovalCentre/ab_outlet.edit.php">
                                            <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewTransferModal">
                                            <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                        </a>

                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start">
                            <button class="btn btn-primary">Approve</button>
                            <button class="btn btn-secondary mx-2">Reject</button>
                        </div>
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-sm-end justify-content-lg-end">
                            <nav aria-label="Page navigation example">
                                <ul class="pagination round-pagination  bx-pull-right">
                                    <li class="page-item"><a class="page-link" href="javascript:;">Previous</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;javascript:;">1</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="javascript:;">2</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">3</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <!-- Modal -->
                    <div class="modal fade" id="viewTransferModal" data-bs-keyboard="false"
                        data-bs-backdrop="static" tabindex="-1" aria-labelledby="viewTransferModalLabel"
                        aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewTransferModalLabel">View Approval AB Outlet Asset
                                        Transfer
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">

                                                                <li>
                                                                    <h4>REGION:</h4>
                                                                    <h6>West TT</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Territory:</h4>
                                                                    <h6>ASM-TT-MP</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Territory:</h4>
                                                                    <h6>SO-TT-Jabalpur</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CFA:</h4>
                                                                    <h6>Indore</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Submit Date:</h4>
                                                                    <h6>2023-02-15 00:00:00</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Request No:</h4>
                                                                    <h6>APR10004065</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Pullout Outlet code:</h4>
                                                                    <h6></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Placement Outlet code:</h4>
                                                                    <h6></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Applied Chiller Type:</h4>
                                                                    <h6>280 LTR</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Request Status:</h4>
                                                                    <h6>Task Completed</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Approval Type:</h4>
                                                                    <h6>Exceptional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Current VPO:</h4>
                                                                    <h6>63.00</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Expected VPO:</h4>
                                                                    <h6>2500.00</h6>
                                                                </li>

                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>ASM Name:</h4>
                                                                    <h6>Umesh Soni</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Name:</h4>
                                                                    <h6>Manoj Gautam</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Name:</h4>
                                                                    <h6>GROW MORE ENTERPRISES</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Code:</h4>
                                                                    <h6>1760285010</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Route Name:</h4>
                                                                    <h6>RANJHI BASTI</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Code:</h4>
                                                                    <h6>1760285010_E000188</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Name:</h4>
                                                                    <h6>Komal traders</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer City Name:</h4>
                                                                    <h6>JABALPUR</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Address:</h4>
                                                                    <h6>1397, Shukla Nag</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer contact:</h4>
                                                                    <h6></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Remarks:</h4>
                                                                    <h6>Outlet Closure</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Deployment date:</h4>
                                                                    <h6>5-Jan-24</h6>
                                                                </li>


                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-end">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
