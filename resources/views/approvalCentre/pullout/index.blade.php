<x-app-layout>
    @php
        $roleCheck = ['SO', 'ASM', 'RSM', 'AE', 'VE', 'VENDOR'];
        $user = Auth::user();
    @endphp
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Pullout</h4>
                    <h6>View/Pullout</h6>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col-auto status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            @include('layouts.loginBasisApprovalFilter')
                        </div>
                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-2">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            {{--  <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{asset('img/icons/search-white.svg')}}"
                                        alt="img"></a>
                            </div>  --}}
                            @php
                              $requestQuery =  request()->query('FromDate');
                            @endphp
                            <div class="wordset">
                                <a href="{{ route('approval_center.pullout_request_report_download',
                                         [
                                            'so_teritory' =>  request()->query('so_teritory') ??'',
                                            'asm_teritory' =>  request()->query('asm_teritory') ??'',
                                            'db_code' =>  request()->query('db_code') ??'',
                                            'distributor_name' =>  request()->query('distributor_name') ??'',
                                            'customer_name' =>  request()->query('customer_name') ??'',
                                            'customer_city' =>  request()->query('customer_city') ??'',
                                            'customer_code' =>  request()->query('customer_code') ??'',
                                            'route_name' =>  request()->query('route_name') ??'',
                                            'request_no' =>  request()->query('request_no') ??'',
                                            'status' =>  request()->query('status') ??'',
                                            'request_status' =>  request()->query('request_status') ??'',
                                            'FromDate' =>  request()->query('FromDate') ??'',
                                            'ToDate' =>  request()->query('ToDate') ??'',
                                        ]) }}"
                                   class=" btn btn-primary " data-bs-toggle="tooltip" data-bs-placement="top"
                                   title="Pullout Report Download">
                                    <i class="fa fa-download me-2" aria-hidden="true"></i>Download

                                </a>
{{--                                <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"--}}
{{--                                        title="excel"><i class="fa fa-download me-2"--}}
{{--                                                         aria-hidden="true"></i>Download--}}
{{--                                </button>--}}
                            </div>
                        </div>
                    </div>
                    {{--  Filter  --}}
                    @include('layouts.approval_filter')
                    {{--  table   --}}
                    @if (\Session::has('success'))
                        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                            <strong>Success!</strong> {!! \Session::get('success') !!}
                        </div>
                    @endif

                    @if (\Session::has('error'))
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            {!! \Session::get('error') !!}
                        </div>
                    @endif
                    <div class="table-responsive">
                        <div id="ajax_message"></div>
                        <table class="table ">
                            <thead>
                            <tr>
                                @if (in_array($user->user_type, $roleCheck))
                                    {{--  @if (in_array($user->user_type, $roleCheck) && $user->user_type != 'VENDOR')  --}}
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                @endif
                                <th>Region</th>
                                <th>ASM Territory</th>
                                <th>SO territory</th>
                                <th>CFA</th>
                                <th>Assigned organization</th>
                                <th>Submit Date</th>
                                <th>ASM Name</th>
                                <th>SO Name</th>
                                <th>Request No</th>
                                <th>Request Status</th>
                                <th>Asset Serial number</th>
                                <th>Asset Barcode</th>
                                <th>Chiller Type</th>
                                <th>Deployment Date</th>
                                <th>Customer Code</th>
                                <th>Customer Name</th>
                                <th>Customer City Name</th>
                                <th>State</th>
                                <th>Distributor Code</th>
                                <th>Distributor Name</th>
                                <th>Route Name</th>
                                <th>Current VPO</th>
                                <th>Customer Address</th>
                                <th>Pin Code</th>
                                <th>Pullout date &amp; time</th>
                                <th>Pullout Status</th>
                                <th>Pullout Reason</th>
                                <th>Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if (is_array($assetPlacementListView) && !empty($assetPlacementListView))

                                @foreach ($assetPlacementListView as $asset)
                                    <tr data-pending-from="{{ $asset->pending_from ?? '' }}">
                                        @if (in_array($user->user_type, $roleCheck))
                                            <td>
                                                <label class="checkboxs">

                                                    @if (request()->query('status')=='taskallocation' ||
                                                            (($asset->asset_assigned_status == 'Pending' &&
                                                                empty($asset->approved_by_user_id) &&
                                                                empty($asset->rejected_by_user_id) &&
                                                                $asset->pending_from == $user->user_type) ||
                                                                $asset->pending_from == $user->user_type))
                                                        @php
                                                            // $requestID=$user->user_type=="VENDOR"?$asset->request_number:$asset->id
                                                            $requestID=$asset->request_number;
                                                        @endphp
                                                        <input type="checkbox" class="approvalCheckbox"
                                                               value="{{ $requestID }}">

                                                    @else
                                                        <input type="checkbox" class="approvalCheckbox"
                                                               value="{{ $asset->id }}" disabled>
                                                    @endif
                                                    <span class="checkmarks"></span>

                                                </label>
                                            </td>
                                        @endif
                                        <td>{{ $asset->region ?? '' }}</td>
                                        <td>{{ $asset->asm_area_code ?? '' }}</td>
                                        <td>{{ $asset->teritory_code ?? '' }}</td>
                                        <td>{{ $asset->cfa_code ?? '' }}</td>
                                        <td>{{ $asset->assigned_organization ?? '' }}</td>
                                        <td>{{ $asset->created_at ?? '' }}</td>
                                        <td>{{ $asset->asm_name ?? '' }}</td>
                                        <td>{{ $asset->so_name ?? '' }}</td>
                                        <td>{{ $asset->request_number ?? '' }}</td>
                                        <td>{{ $asset->taskStatus ?? '' }}</td>
                                        <td>{{ $asset->serial_number ?? '' }}</td>
                                        <td>{{ $asset->barcode ?? '' }}</td>
                                        <td>{{ $asset->eligible_chiller_type ?? '' }}</td>
                                        <td>{{ $asset->deployment_date ?? '' }}</td>
                                        <td>{{ $asset->outlet_code ?? '' }}</td>
                                        <td>{{ $asset->retailer_name ?? '' }}</td>
                                        <td>{{ $asset->city ?? '' }}</td>
                                        <td>{{ $asset->state ?? '' }}</td>
                                        <td>{{ $asset->db_code ?? '' }}</td>
                                        <td>{{ $asset->db_name ?? '' }}</td>
                                        <td>{{ $asset->route_name ?? '' }}</td>
                                        <td>{{ $asset->current_vpo ?? '' }}</td>
                                        <td>{{ $asset->address_1 ?? '' }}</td>
                                        <td>{{ $asset->pin_code ?? '' }}</td>
                                        <td>{{ $asset->pullout_date_time ?? '' }}</td>
                                        <td>{{ $asset->deploymentStatus ?? '' }}
                                        <td>{{ $asset->pullout_reason ?? '' }}
                                        <td>
                                            <a class="me-2 view-pullout-model" href="#" data-bs-toggle="modal"
                                               data-asset="{{ json_encode([
                                                        'region' => e($asset->region ?? ''),
                                                        'asm_area_code' => e($asset->asm_area_code ?? ''),
                                                        'teritory_code' => e($asset->teritory_code ?? ''),
                                                        'cfa_code' => e($asset->cfa_code ?? ''),
                                                        'created_at' => e($asset->created_at ?? ''),
                                                        'asm_name' => e($asset->asm_name ?? ''),
                                                        'so_name' => e($asset->so_name ?? ''),
                                                        'request_number' => e($asset->request_number ?? ''),
                                                        'taskStatus' => e($asset->taskStatus ?? ''),
                                                        'serial_number' => e($asset->serial_number ?? ''),
                                                        'barcode' => e($asset->barcode ?? ''),
                                                        'eligible_chiller_type' => e($asset->eligible_chiller_type ?? ''),
                                                        'deployment_date' => e($asset->deployment_date ?? ''),
                                                        'outlet_code' => e($asset->outlet_code ?? ''),
                                                        'retailer_name' => e($asset->retailer_name ?? ''),
                                                        'city' => e($asset->city ?? ''),
                                                        'state' => e($asset->state ?? ''),
                                                        'db_code' => e($asset->db_code ?? ''),
                                                        'db_name' => e($asset->db_name ?? ''),
                                                        'route_name' => e($asset->route_name ?? ''),
                                                        'current_vpo' => e($asset->current_vpo ?? ''),
                                                        'address_1' => e($asset->address_1 ?? ''),
                                                        'pin_code' => e($asset->pin_code ?? ''),
                                                        'pullout_date_time' => e($asset->pullout_date_time ?? ''),
                                                        'pullout_reason' => e($asset->pullout_reason ?? ''),
                                                    ]) }}">
                                                <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                            </a>
                                            @if (
                                                $user->user_type == 'VENDOR' &&
                                                    in_array($asset->pending_from, ['VENDOR', 'VE']) &&
                                                    $asset->task_status == 'Pending')
                                                <a class="me-2"
                                                   href="{{ route('vendor_allocation.placement_allocation_edit', ['id' => encrypt($asset->request_number),'request_type'=>'pullout']) }}">
                                                    <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="10">
                                        No data found!
                                    </td>

                                </tr>
                            @endif
                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">
                        @if (in_array($user->user_type, $roleCheck) && $user->user_type !== 'VENDOR')
                            <div
                                class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start">
                                <button class="btn btn-primary approvalStatusBtn"
                                        data-status='Approved'>Approve</button>
                                <button class="btn btn-secondary mx-2 approvalStatusBtn"
                                        data-status='Rejected'>Reject</button>
                            </div>
                        @endif
                        @if (in_array($user->user_type, $roleCheck) && $user->user_type == 'VENDOR')
                            <div
                                class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start">

                                @if (request()->query('status')!='taskallocation')
                                    <button class="btn btn-primary assignedExecutive" data-status='assignedExecutive'>Assigned
                                        Executive</button>
                                    <button class="btn btn-secondary mx-2 approvalStatusBtn"
                                            data-status='Rejected'>Reject</button>
                                @endif
                                  @if (request()->query('status')=='taskallocation')
                                    <button class="btn btn-primary assignedExecutive" data-status='downloadChallan'>Download
                                        Challan</button>
                                    <button class="btn btn-secondary mx-2 approvalStatusBtn"
                                            data-status='Rejected'>Reject</button>
                                @endif
                            </div>
                        @endif
                        <div class="col-12 my-2">
                            {{-- Check if pagination exists --}}
                            @if ($assetPlacementList->lastPage() > 1)
                                <div class="text-right">
                                    {!! $assetPlacementList->withQueryString()->links('pagination::bootstrap-5') !!}
                                    {{--  {{$assetPlacementList->links()}}  --}}
                                </div>
                            @endif

                        </div>

                    </div>
                    <!-- Modal -->
                    <div class="modal fade" id="viewPulloutModal" data-bs-keyboard="false" data-bs-backdrop="static"
                         tabindex="-1" aria-labelledby="viewPulloutModalLabel" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewPulloutModalLabel">View Approval Pullout
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                            aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">

                                                                <li>
                                                                    <h4>REGION:</h4>
                                                                    <h6 id="region"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Territory:</h4>
                                                                    <h6 id="asm_area_code"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Territory:</h4>
                                                                    <h6 id="teritory_code"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CFA:</h4>
                                                                    <h6 id="cfa_code"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Submit Date:</h4>
                                                                    <h6 id="created_at"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Name:</h4>
                                                                    <h6 id="asm_name"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Name:</h4>
                                                                    <h6 id="so_name"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Request No:</h4>
                                                                    <h6 id="request_number"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Request Status:</h4>
                                                                    <h6 id="taskStatus"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Asset Serial number:</h4>
                                                                    <h6 id="serial_number"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Asset Barcode:</h4>
                                                                    <h6 id="barcode"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Chiller Type:</h4>
                                                                    <h6 id="eligible_chiller_type"></h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">

                                                                <li>
                                                                    <h4>Current VPO:</h4>
                                                                    <h6 id="current_vpo"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Name:</h4>
                                                                    <h6 id="db_name"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Code:</h4>
                                                                    <h6 id="db_code"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Route Name:</h4>
                                                                    <h6 id="route_name"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Code:</h4>
                                                                    <h6 id="outlet_code"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Name:</h4>
                                                                    <h6 id="retailer_name"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer City Name:</h4>
                                                                    <h6 id="city"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Address:</h4>
                                                                    <h6 id="address_1"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>State:</h4>
                                                                    <h6 id="state"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Deployment date:</h4>
                                                                    <h6 id="deployment_date"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Pullout date &amp; time:</h4>
                                                                    <h6 id="pullout_date_time"></h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Pullout Reason &amp; time:</h4>
                                                                    <h6 id="pullout_reason"></h6>
                                                                </li>

                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-end">
                                                        <button type="button" class="btn btn-secondary"
                                                                data-bs-dismiss="modal">Close
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            $('.approvalStatusBtn').click(function(e) {

                $('#ajax_message').html('');
                $('.validatin-error').remove();
                var selectedIds = [];
                $('.approvalCheckbox:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                // Send AJAX request to Laravel route
                if (selectedIds.length > 0) {
                    showLoader();
                    let status = $(this).attr('data-status');

                    // Prompt for rejection reason if status is reject
                    if (status == 'Rejected') {
                        var rejectionReason = prompt('Please enter the reason for rejection:');
                        if (rejectionReason === null || rejectionReason === "") {
                            // If rejection reason is empty or cancelled, do not proceed
                            hideLoader();
                            return;
                        }
                    }

                    $.ajax({
                        url: "{{ route('approval_center.replacement_approved_reject') }}",
                        method: 'POST',
                        dataType: 'JSON',
                        data: {
                            selectedIds: selectedIds,
                            status: status,
                            approvalType:'pullout',
                            rejection_reason: (status == 'Rejected') ? rejectionReason : null,

                        },
                        success: function(response) {
                            hideLoader();
                            if (response.success) {
                                $('#ajax_message').html(success_message(response
                                    .success));
                                $('html, body').scrollTop(0);
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                $('#ajax_message').html(error_message(response
                                    .error));
                                $('html, body').scrollTop(0);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log("headewr", xhr
                                .responseJSON);
                            hideLoader();
                            $('#ajax_message').html('Error: ' + error_message(xhr
                                .responseJSON.error));
                            $('html, body').scrollTop(0);
                        }
                    });

                } else {
                    $('#ajax_message').html(error_message('Please select at least one Placement Request.'));
                    $('html, body').scrollTop(0);
                    setTimeout(function() {
                        $('#ajax_message').html('');
                    }, 2000);
                    return false;

                }
            });
        });
    </script>
    <script>
        $(".assignedExecutive").click(function() {
            let sIds = [];
            $('.approvalCheckbox:checked').each(function() {
                sIds.push($(this).val());
            });
            let buttonStatus=$(this).attr('data-status');
            if (sIds.length > 0) {
                let idsString = sIds.join('#$');
                let redirectUrl = buttonStatus === 'assignedExecutive'
                    ? "{{ route('vendor_allocation.assigned_executive_allocation') }}?request_type=pullout&aprID=" + encodeURIComponent(idsString)
                    : "{{ route('admin.download.multiple_challan_pdf') }}?request_type=pullout&aprID=" + encodeURIComponent(idsString);

                window.location.href = redirectUrl;
            } else {
                alert('Please select at least one checkbox.');
            }
        });

        $(".status_filter").click(function() {
            let status = $(this).data("status");
            let urlParams = new URLSearchParams(window.location.search);
            urlParams.set('status', status);
            window.location.search = urlParams.toString();
        });
    </script>
    <script>
        $(document).ready(function() {
            $('.view-pullout-model').on('click', function() {
                // Retrieve assetData from data-asset attribute
                var assetData = JSON.parse($(this).attr('data-asset'));
                // Loop through each key-value pair in assetData
                $.each(assetData, function(key, value) {
                    if (key === 'signature' || key === 'competitor_chiller_photo' || key === 'chiller_location_photo'
                        || key === 'address_proof' || key === 'retailer_photo'
                    ) {
                        if(!value || value === null || value=='NULL'){
                            $('.'+key).hide();
                        }else{
                            $('.'+key).show();
                            $('#'+key).attr('src', value);
                        }
                    } else {
                        $('#' + key).text(value);
                    }

                });
                $('#viewPulloutModal').modal('show');
            });
        });
    </script>
</x-app-layout>
