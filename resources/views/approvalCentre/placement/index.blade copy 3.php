<x-app-layout>
    @php
        $roleCheck = ['SO', 'ASM', 'RSM', 'AE', 'VE', 'VENDOR'];
    @endphp
    @inject('taskStatusService', 'App\Services\PlacementRequestService\TaskStatusService');
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Placement</h4>
                    <h6>View/Placement</h6>
                </div>

            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col-auto status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            <button class="btn btn-success mx-1 status_filter" data-status="All">All</button>
                            <button class="btn btn-secondary mx-1 status_filter" data-status="Pending">Pending</button>
                            @if ($user->user_type !='VENDOR')
                            <button class="btn btn-primary mx-2 status_filter" data-status="Approved">Other Approval
                                Status</button>
                            <button class="btn btn-danger mx-2 status_filter" data-status="Rejected">Rejected</button>
                            @endif

                            <a class="btn btn-warning py-2"
                                href="{{ route('approval_center.placement', ['status' => 'All']) }}">Reset</a>
                        </div>

                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-2">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            {{--  <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{asset('img/icons/search-white.svg')}}"
                                        alt="img"></a>
                            </div>  --}}
                            <div class="wordset">
                                <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="excel"><i class="fa fa-download me-2"
                                        aria-hidden="true"></i>Download</button>
                            </div>
                        </div>
                    </div>
                    {{--  Filter  --}}
                    {{--  @include('layouts.approval_filter')  --}}
                    {{-- end  Filter  --}}

                    {{--  table   --}}
                    @if (\Session::has('success'))
                        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                            <strong>Success!</strong> {!! \Session::get('success') !!}
                        </div>
                    @endif

                    @if (\Session::has('error'))
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            {!! \Session::get('error') !!}
                        </div>
                    @endif

                    <div class="table-responsive">

                        <div id="ajax_message"></div>
                        <table class="table ">
                            <thead>
                                <tr>
                                    @if (in_array($user->user_type, $roleCheck) && $user->user_type !='VENDOR')
                                        <th>
                                            <label class="checkboxs">
                                                <input type="checkbox" id="select-all">
                                                <span class="checkmarks"></span>
                                            </label>
                                        </th>
                                    @endif
                                    <th>Region</th>
                                    <th>Request Number</th>
                                    <th>Submit Date</th>
                                    <th>Request Status</th>
                                    <th>Request Status</th>
                                    <th>Expected VPO</th>
                                    <th>Customer Code</th>
                                    <th>Customer Name</th>
                                    <th>RTMM Name</th>
                                    <th>ASM Name</th>
                                    <th>ASM Territory</th>
                                    <th>SO Name</th>
                                    <th>SO Territory</th>
                                    <th>Distributor Name</th>
                                    <th>Distributor Code</th>
                                    <th>DSR Name</th>
                                    <th>DSR Code</th>
                                    <th>CFA</th>
                                    <th>Channel Code</th>
                                    <th>Outlet classification</th>
                                    <th>Customer Category</th>
                                    <th>Route Name</th>
                                    <th>Customer City </th>
                                    {{--  <th>Customer Address</th>  --}}
                                    <th>Address 1</th>
                                    {{--  <th>Address 1</th>  --}}
                                    <th>Address 2</th>
                                    <th>RTMM Approval Time</th>
                                    <th>ASM Approval Time</th>
                                    <th>SO Placement Time</th>
                                    <th>Deployment date</th>
                                    <th>Deployment Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if ($assetPlacementList->isNotEmpty())
                                @foreach ($assetPlacementList as $asset)
                                        @php
                                            $outlet = $asset->outlet;
                                            $retailer = $asset->retailer;
                                            $approval_history = $asset->approvalHistory;
                                            $asm_approval = $approval_history
                                                ->filter(function ($record) {
                                                    return $record['user_role'] === 'ASM';
                                                })
                                                ->first();

                                            $rsm_approval = $approval_history
                                                ->filter(function ($record) {
                                                    return $record['user_role'] === 'RSM';
                                                })
                                                ->first();
                                            $distributor = $asset->distributor;
                                            $cfa = $distributor->cfa;
                                            $so = $asset->so;
                                            $asm = $asset->asm;
                                            //echo "<pre>";print_R($outlet->toArray());
                                            $rsm = $asset->rsm;
                                            $dsr = $asset->dsr;
                                            $taskStatus = $taskStatusService->getTaskStatus($asset);
                                            $deploymentStatus = $taskStatusService->deploymentStatus($asset);

                                        @endphp
                                        @php
                                            // Determine if the current user approved or rejected the asset
                                            $userApproved =
                                                ($asset->approved_by_user_id === $user->user_id &&
                                                    $asset->asset_assigned_status != 'Rejected') ||
                                                $asset->approved_by_user_id === $user->user_id;

                                            $userRejected =
                                                ($asset->rejected_by_user_id === $user->user_id &&
                                                    $asset->asset_assigned_status == 'Rejected') ||
                                                $asset->rejected_by_user_role != $user->user_type;
                                            $userApprovedOrRejected = $userApproved || $userRejected;
                                        @endphp
                                        <tr>
                                            @if (in_array($user->user_type, $roleCheck) && $user->user_type !='VENDOR')
                                                <td>
                                                    <label class="checkboxs">

                                                        @if (
                                                            ($asset->asset_assigned_status == 'Pending' &&
                                                                empty($asset->approved_by_user_id) &&
                                                                empty($asset->rejected_by_user_id) &&
                                                                $asset->pending_from == $user->user_type) ||
                                                                $asset->pending_from == $user->user_type)
                                                            {{-- Remove the disabled attribute when the conditions are met --}}
                                                            <input type="checkbox" class="approvalCheckbox"
                                                                value="{{ $asset->id }}">
                                                        @else
                                                            {{-- Keep the disabled attribute when the conditions are not met --}}
                                                            <input type="checkbox" class="approvalCheckbox"
                                                                value="{{ $asset->id }}" disabled>
                                                        @endif
                                                        <span class="checkmarks"></span>

                                                    </label>
                                                </td>
                                            @endif
                                            <td>{{ $outlet->region ?? '' }}</td>

                                            <td>{{ $asset->request_number ?? '' }}</td>
                                            <td>{{ date('Y-m-d H:i:s', strtotime($asset->created_at)) }}</td>

                                            <td>{{ $taskStatus ?? '' }}</td>
                                            <td>{{ $asset->request_type ?? '' }}</td>
                                            {{--  <td>{{ $asset->eligible_chiller_type ?? '' }}</td>  --}}
                                            <td>{{ $asset->expected_vpo ?? '' }}</td>
                                            <td>{{ $retailer->user_id ?? '' }}</td>
                                            <td>{{ $retailer->name ?? '' }}</td>
                                            <td>{{ $rsm->name ?? '' }}</td>
                                            <td>{{ $asm->name ?? '' }}</td>
                                            <td>{{ $asm->area_code ?? '' }}</td>
                                            <td>{{ $so->name ?? '' }}</td>
                                            <td>{{ $so->teritory_code ?? '' }}</td>
                                            <td>{{ $distributor->name ?? '' }}</td>
                                            <td>{{ $distributor->user_id ?? '' }}</td>
                                            <td>{{ $dsr->name ?? '' }}</td>
                                            <td>{{ $dsr->user_id ?? '' }}</td>
                                            <td>{{ $cfa->user_id ?? '' }}</td>
                                            <td>{{ $outlet->channel_code ?? '' }}</td>
                                            <td>{{ $outlet->class_name ?? '' }}</td>
                                            <td>{{ $outlet->category_name ?? '' }}</td>
                                            <td>{{ $outlet->route_name ?? '' }}</td>
                                            <td>{{ $retailer->city ?? '' }}</td>
                                            <td>{{ $outlet->invoiceaddress ?? '' }}</td>
                                            {{--  outlet address 1  --}}
                                            {{--  <td>{{ $outlet->address_1 ?? '' }}</td>  --}}
                                             {{--  Placement customer address  --}}
                                            <td>{{ $asset->customer_address ?? '' }}</td>
                                            <td>{{ $rsm_approval->action_updated_time ?? '' }}</td>
                                            <td>{{ $asm_approval->action_updated_time ?? '' }}</td>
                                            <td>{{ $asset->created_at ?? '' }}</td>
                                            <td>{{ $asset->is_deploy == true ? $asset->deployment_date:'' }}</td>
                                            <td>{{$deploymentStatus??''}}
                                            </td>
                                            <td>
                                                {{--  <a class="me-2"
                                                    href="{{ route('approval_center.placement_edit', ['id' => 1]) }}">
                                                    <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                                </a>  --}}
                                                <a class="me-2 view-placement" href="#" data-bs-toggle="modal"
                                                    data-asset="{{ json_encode([
                                                        'request_number' => e($asset->request_number ?? ''),
                                                        'submit_date' => e(date('Y-m-d H:i:s', strtotime($asset->created_at))),
                                                        'request_status' => e($taskStatus ?? ''),
                                                        'expected_vpo' => e($asset->expected_vpo ?? ''),
                                                        'customer_code' => e($retailer->user_id ?? ''),
                                                        'customer_name' => e($retailer->name ?? ''),
                                                        'asm_name' => e($asm->name ?? ''),
                                                        'so_name' => e($so->name ?? ''),
                                                        'distributor_name' => e($distributor->name ?? ''),
                                                        'distributor_code' => e($distributor->user_id ?? ''),
                                                        'route_name' => e($outlet->route_name ?? ''),
                                                        'customer_city' => e($retailer->city ?? ''),
                                                        'customer_address' => e($outlet->invoiceaddress ?? ''),
                                                        'customer_mobile_number' => e($retailer->mobile_number ?? ''),
                                                        'rtmm_approval_time' => e($rsm_approval->action_updated_time ?? ''),
                                                        'remarks' => e($asset->remarks ?? ''),
                                                        'signature' => e($asset->signature ? asset($asset->signature) : ''),
                                                        'deployment_status' => e($deploymentStatus),
                                                    ]) }}">
                                                    <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                                </a>
                                                @if ($user->user_type == 'VENDOR' && in_array($asset->pending_from, ['VENDOR', 'VE']) && $asset->task_status=='Pending')
                                                <a class="me-2" href="{{ route('vendor_allocation.placement_allocation_edit', ['id' => encrypt($asset->request_number)]) }}">
                                                    <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                                </a>
                                            @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="10">
                                            No data found!
                                        </td>

                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>

                    {{-- end table   --}}
                    <div class="row btn-approve-reject">
                        @if (in_array($user->user_type, $roleCheck) && $user->user_type !== 'VENDOR')
                            <div
                                class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start">
                                <button class="btn btn-primary approvalStatusBtn"
                                    data-status='Approved'>Approved</button>
                                <button class="btn btn-secondary mx-2 approvalStatusBtn"
                                    data-status='Rejected'>Reject</button>
                            </div>
                        @endif
                        <div class="col-12 my-2">
                            {{--  @can('User access')  --}}
                            <div class="text-right">
                                {!! $assetPlacementList->withQueryString()->links('pagination::bootstrap-5') !!}
                            </div>
                            {{--  @endcan  --}}
                        </div>
                    </div>
                    <!-- Modal -->
                    <div class="modal fade" id="viewPlacementApprovalModal" data-bs-keyboard="false"
                        data-bs-backdrop="static" tabindex="-1" aria-labelledby="viewPlacementApprovalModalLabel"
                        aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewPlacementApprovalModalLabel">View Approval
                                        Placement
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    {{--  <form>  --}}
                                    <div class="row">
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="request_number" class="form-label">Request No:</label>
                                                <input type="text" class="form-control" id="request_number" readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="submit_date" class="form-label">Submit Date:</label>
                                                <input type="text" class="form-control" id="submit_date" readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="request_status" class="form-label">Request Status:</label>
                                                <input type="text" class="form-control" id="request_status"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="expected_vpo" class="form-label">Expected VPO:</label>
                                                <input type="text" class="form-control" id="expected_vpo"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="customer_code" class="form-label">Customer Code:</label>
                                                <input type="text" class="form-control" id="customer_code"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="customer_name" class="form-label">Customer Name:</label>
                                                <input type="text" class="form-control" id="customer_name"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="asm_name" class="form-label">ASM Name:</label>
                                                <input type="text" class="form-control" id="asm_name" readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="so_name" class="form-label">SO Name:</label>
                                                <input type="text" class="form-control" id="so_name" readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="distributor_name" class="form-label">Distributor
                                                    Name:</label>
                                                <input type="text" class="form-control" id="distributor_name"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="distributor_code" class="form-label">Distributor
                                                    Code:</label>
                                                <input type="text" class="form-control" id="distributor_code"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="route_name" class="form-label">Route Name:</label>
                                                <input type="text" class="form-control" id="route_name" readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="customer_city" class="form-label">Customer City
                                                    Name:</label>
                                                <input type="text" class="form-control" id="customer_city"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="customer_address" class="form-label">Customer
                                                    Address:</label>
                                                <input type="text" class="form-control" id="customer_address"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="customer_mobile_number" class="form-label">Customer
                                                    contact:</label>
                                                <input type="text" class="form-control"
                                                    id="customer_mobile_number" readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="remarks" class="form-label">Remarks:</label>
                                                <input type="text" class="form-control manitorygreen"
                                                    id="remarks" readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="rtmm_approval_time" class="form-label">RTMM Approval
                                                    Time:</label>
                                                <input type="text" class="form-control" id="rtmm_approval_time"
                                                    readonly>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="deployment_status" class="form-label">Deployment
                                                    Status:</label>
                                                <input type="text" class="form-control" id="deployment_status"
                                                    readonly>
                                            </div>
                                        </div>

                                        <div class="col-lg-3 col-sm-6">
                                            <div class="mb-3">
                                                <label for="signature_image" class="form-label">Image:</label>
                                            </div>
                                            <div class="mb-3">
                                                <img src="" alt="Image" id="signature_image"
                                                    class="img-fluid" width="80" height="80">
                                            </div>
                                        </div>

                                    </div>
                                    {{--  </form>  --}}
                                </div>
                                <div class="modal-footer justify-content-end">
                                    <button type="button" class="btn btn-secondary"
                                        data-bs-dismiss="modal">Close</button>

                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            $('.approvalStatusBtn').click(function(e) {

                $('#ajax_message').html('');
                $('.validatin-error').remove();
                var selectedIds = [];
                $('.approvalCheckbox:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                // Send AJAX request to Laravel route
                if (selectedIds.length > 0) {
                    showLoader();
                    let status = $(this).attr('data-status');

                    // Prompt for rejection reason if status is reject
                    if (status == 'Rejected') {
                        var rejectionReason = prompt('Please enter the reason for rejection:');
                        if (rejectionReason === null || rejectionReason === "") {
                            // If rejection reason is empty or cancelled, do not proceed
                            hideLoader();
                            return;
                        }
                    }

                    $.ajax({
                        url: "{{ route('approval_center.replacement_approved_reject') }}",
                        method: 'POST',
                        dataType: 'JSON',
                        data: {
                            selectedIds: selectedIds,
                            status: status,
                            rejection_reason: (status == 'Rejected') ? rejectionReason : null
                        },
                        success: function(response) {
                            hideLoader();
                            if (response.success) {
                                $('#ajax_message').html(success_message(response
                                    .success));
                                    $('html, body').scrollTop(0);
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                $('#ajax_message').html(error_message(response
                                    .error));
                                    $('html, body').scrollTop(0);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log("headewr", xhr
                                .responseJSON);
                            hideLoader();
                            $('#ajax_message').html('Error: ' + error_message(xhr
                                .responseJSON.error));
                                $('html, body').scrollTop(0);
                        }
                    });

                } else {
                    $('#ajax_message').html(error_message('Please select at least one Placement Request.'));
                    $('html, body').scrollTop(0);
                    setTimeout(function() {
                        $('#ajax_message').html('');
                    }, 2000);
                    return false;

                }
            });
        });
    </script>
    <script>
        $("#assign_ve").click(function() {
            let veCode = $("#ve_code").val();
            alert(veCode);
        });
        $(".status_filter").click(function() {
            let status = $(this).data("status");
            let urlParams = new URLSearchParams(window.location.search);
            urlParams.set('status', status);
            window.location.search = urlParams.toString();
        });
    </script>
    <script>
        $(document).ready(function() {
            $('.view-placement').on('click', function() {
                // Retrieve assetData from data-asset attribute
                var assetData = JSON.parse($(this).attr('data-asset'));
                // Loop through each key-value pair in assetData
                $.each(assetData, function(key, value) {
                    if (key === 'signature') {
                        $('#signature_image').attr('src', value);
                    } else {
                        $('#' + key).val(value);
                    }

                });
                $('#viewPlacementApprovalModal').modal('show');
            });
        });
    </script>
</x-app-layout>
