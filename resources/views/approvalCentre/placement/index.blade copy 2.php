<x-app-layout>
    @php
        $roleCheck = ['SO', 'ASM', 'RSM', 'AE', 'VE', 'VENDOR'];
    @endphp
    @inject('taskStatusService', 'App\Services\PlacementRequestService\TaskStatusService');
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Placement</h4>
                    <h6>View/Placement</h6>
                </div>

            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            <button class="btn btn-success mx-1 status_filter" data-status="All">All</button>
                            <button class="btn btn-secondary mx-1 status_filter" data-status="Pending">Pending</button>
                            <button class="btn btn-primary mx-2 status_filter" data-status="Approved">Other Approval Status</button>
                            <button class="btn btn-danger status_filter" data-status="Rejected">Rejected</button>
                        </div>

                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-sm-2">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            {{--  <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{asset('img/icons/search-white.svg')}}"
                                        alt="img"></a>
                            </div>  --}}
                            <div class="wordset">
                                <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="excel"><i class="fa fa-download me-2"
                                        aria-hidden="true"></i>Download</button>
                            </div>
                        </div>
                    </div>
                    {{--  Filter  --}}
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit From Date</label>
                                                <div class="ui calendar w-100" id="rangestart">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit From Date" name="FromDate" id="FromDate">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit To Date</label>
                                                <div class="ui calendar w-100" id="rangeend">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit To Date" name="ToDate" id="ToDate">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>DB Code</label>
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer code</label>
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request No.</label>
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>SO Name</label>
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Distributor Name</label>
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request Status</label>
                                            <select class="form-select select">
                                                <option>Task Panding</option>
                                                <option selected>Task Completed</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>ASM Name</label>
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer City</label>
                                            <input type="text" class="form-control" placeholder="Customer City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Route Name</label>
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class=" col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">
                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="{{ asset('img/icons/search-whites.svg') }}"
                                                    alt="img"></button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    {{-- end  Filter  --}}

                    {{--  table   --}}
                    <div class="table-responsive">
                        <div id="ajax_message"></div>
                        <table class="table ">
                            <thead>
                                <tr>
                                    @if (in_array($user->user_type, $roleCheck))
                                        <th>
                                            <label class="checkboxs">
                                                <input type="checkbox" id="select-all">
                                                <span class="checkmarks"></span>
                                            </label>
                                        </th>
                                    @endif
                                    <th>Request NUmber</th>
                                    <th>Submit Date</th>
                                    <th>Request Status</th>
                                    {{--  <th>Approval Type</th>  --}}
                                    <th>Request Status</th>
                                    {{--  <th>Applied Chiller Type</th>  --}}
                                    {{--  <th>Current VPO</th>  --}}
                                    <th>Expected VPO</th>
                                    <th>Customer Code</th>
                                    <th>Customer Name</th>
                                    <th>ASM Name</th>
                                    <th>SO Name</th>
                                    <th>Distributor Name</th>
                                    <th>Distributor Code</th>
                                    <th>Route Name</th>
                                    <th>Customer City </th>
                                    <th>Customer Address</th>
                                    <th>RTMM Approval Time</th>
                                    <th>Deployment date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if ($assetPlacementList->isNotEmpty())
                                    @foreach ($assetPlacementList as $asset)
                                        @php
                                            $outlet = $asset->outlet;
                                            $retailer = $outlet->retailer;

                                            $distributor = $asset->outlet->dsr->distributor;
                                            $so = $asset->outlet->dsr->distributor->so;
                                            $asm = $asset->outlet->dsr->distributor->so->asm;
                                            $rsm = $asset->outlet->dsr->distributor->so->asm->rsm;

                                            $taskStatus = $taskStatusService->getTaskStatus($asset);;

                                        @endphp
                                        @php
                                            // Determine if the current user approved or rejected the asset
                                            $userApproved =
                                                ($asset->approved_by_user_id === $user->user_id &&
                                                    $asset->asset_assigned_status != 'Rejected') ||
                                                $asset->approved_by_user_id === $user->user_id;

                                            $userRejected =
                                                ($asset->rejected_by_user_id === $user->user_id &&
                                                    $asset->asset_assigned_status == 'Rejected') ||
                                                $asset->rejected_by_user_role != $user->user_type;
                                            $userApprovedOrRejected = $userApproved || $userRejected;
                                        @endphp
                                        <tr>
                                            @if ($userApprovedOrRejected || in_array($user->user_type, $roleCheck))
                                                <td>
                                                    <label class="checkboxs">

                                                        @if (
                                                            ($asset->asset_assigned_status == 'Pending' &&
                                                                empty($asset->approved_by_user_id) &&
                                                                empty($asset->rejected_by_user_id) &&
                                                                $asset->pending_from == $user->user_type
                                                                ) ||
                                                                $asset->pending_from == $user->user_type)
                                                            {{-- Remove the disabled attribute when the conditions are met --}}
                                                            <input type="checkbox" class="approvalCheckbox"
                                                                value="{{ $asset->id }}">
                                                        @else
                                                            {{-- Keep the disabled attribute when the conditions are not met --}}
                                                            <input type="checkbox" class="approvalCheckbox"
                                                                value="{{ $asset->id }}" disabled>
                                                        @endif
                                                        <span class="checkmarks"></span>

                                                    </label>
                                                </td>
                                            @endif
                                            <td>{{ $asset->request_number ?? '' }}</td>
                                            <td>{{ date('Y-m-d H:i:s', strtotime($asset->created_at)) }}</td>

                                            <td>Task {{ $taskStatus ?? '' }}</td>
                                            <td>{{ $asset->request_type ?? '' }}</td>
                                            {{--  <td>{{ $asset->eligible_chiller_type ?? '' }}</td>  --}}
                                            <td>{{ $asset->expected_vpo ?? '' }}</td>
                                            <td>{{ $retailer->user_id ?? '' }}</td>
                                            <td>{{ $retailer->name ?? '' }}</td>
                                            <td>{{ $asm->name ?? '' }}</td>
                                            <td>{{ $so->name ?? '' }}</td>
                                            <td>{{ $distributor->name ?? '' }}</td>
                                            <td>{{ $distributor->user_id ?? '' }}</td>
                                            <td>{{ $outlet->route_name ?? '' }}</td>
                                            <td>{{ $retailer->city ?? '' }}</td>
                                            <td>{{ $retailer->location_name ?? '' }}</td>
                                            <td>{{ $asset->approved_time ?? '' }}</td>
                                            <td>{{ $asset->is_deploy == true ? 'Deployed' : 'Waiting for deployment' }}
                                            </td>
                                            <td>
                                                <a class="me-2" href="{{ route('approval_center.placement_edit', ['id' => 1]) }}">
                                                    <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                                </a>
                                                <a class="me-2" href="#" data-bs-toggle="modal"
                                                    data-bs-target="#viewPlacementApprovalModal">
                                                    <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="10">
                                            No data found!
                                        </td>

                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>

                    {{-- end table   --}}
                    <div class="row btn-approve-reject">
                        @if (in_array($user->user_type, $roleCheck))
                            <div
                                class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start">
                                <button class="btn btn-primary approvalStatusBtn"
                                    data-status='Approved'>Approved</button>
                                <button class="btn btn-secondary mx-2 approvalStatusBtn"
                                    data-status='Rejected'>Reject</button>
                            </div>
                        @endif
                        <div class="col-12 my-2">
                            @can('User access')
                                <div class="text-right">
                                    {!! $assetPlacementList->withQueryString()->links('pagination::bootstrap-5') !!}
                                </div>
                            @endcan
                        </div>
                    </div>
                    <!-- Modal -->
                    <div class="modal fade" id="viewPlacementApprovalModal" data-bs-keyboard="false"
                        data-bs-backdrop="static" tabindex="-1" aria-labelledby="viewPlacementApprovalModalLabel"
                        aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewPlacementApprovalModalLabel">View Approval
                                        Placement
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">

                                                                <li>
                                                                    <h4>REGION:</h4>
                                                                    <h6>West TT</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Territory:</h4>
                                                                    <h6>ASM-TT-MP</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Territory:</h4>
                                                                    <h6>SO-TT-Jabalpur</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CFA:</h4>
                                                                    <h6>Indore</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Request No:</h4>
                                                                    <h6>APR10004065</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Submit Date:</h4>
                                                                    <h6>2023-02-15 00:00:00</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Approval Type:</h4>
                                                                    <h6>Exceptional</h6>
                                                                    <!-- <h6 class="manitorygreen">This Field is required</h6> -->
                                                                </li>
                                                                <li>
                                                                    <h4>Request Status:</h4>
                                                                    <h6>Task Completed</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Applied Chiller Type:</h4>
                                                                    <h6>New 60 LTR (VT)</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Current VPO:</h4>
                                                                    <h6>63.00</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Expected VPO:</h4>
                                                                    <h6>3000</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Code:</h4>
                                                                    <h6>1760285010_E000188</h6>
                                                                </li>



                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>Customer Name:</h4>
                                                                    <h6>Komal traders</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Name:</h4>
                                                                    <h6>Umesh Soni</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Name:</h4>
                                                                    <h6>Manoj Gautam</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Name:</h4>
                                                                    <h6>GROW MORE ENTERPRISES</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Code:</h4>
                                                                    <h6>1760285010</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Route Name:</h4>
                                                                    <h6>RANJHI BASTI</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer City Name:</h4>
                                                                    <h6>JABALPUR</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer Address:</h4>
                                                                    <h6>1397, Shukla Nag</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customer contact:</h4>
                                                                    <h6>xxxxxxxxxx</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Remarks:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RTMM Approval Time:</h4>
                                                                    <h6>2023-01-12 16:58:07</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Deployment date:</h4>
                                                                    <h6>5-Jan-24</h6>
                                                                </li>

                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-end">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            $('.approvalStatusBtn').click(function(e) {

                $('#ajax_message').html('');
                $('.validatin-error').remove();
                var selectedIds = [];
                $('.approvalCheckbox:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                // Send AJAX request to Laravel route
                if (selectedIds.length > 0) {
                    showLoader();
                    let status = $(this).attr('data-status');

                    // Prompt for rejection reason if status is reject
                    if (status == 'Rejected') {
                        var rejectionReason = prompt('Please enter the reason for rejection:');
                        if (rejectionReason === null || rejectionReason === "") {
                            // If rejection reason is empty or cancelled, do not proceed
                            hideLoader();
                            return;
                        }
                    }

                    $.ajax({
                        url: "{{ route('approval_center.replacement_approved_reject') }}",
                        method: 'POST',
                        dataType: 'JSON',
                        data: {
                            selectedIds: selectedIds,
                            status: status,
                            rejection_reason: (status == 'Rejected') ? rejectionReason : null
                        },
                        success: function(response) {
                            hideLoader();
                            if (response.success) {
                                $('#ajax_message').html(success_message(response
                                    .success));
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                $('#ajax_message').html(error_message(response
                                    .error));
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log("headewr", xhr
                                .responseJSON);
                            hideLoader();
                            $('#ajax_message').html('Error: ' + error_message(xhr
                                .responseJSON.error));
                        }
                    });

                } else {
                    $('#ajax_message').html(error_message('Please select at least one Placement Request.'));
                    setTimeout(function() {
                        $('#ajax_message').html('');
                    }, 2000);
                    return false;

                }
            });
        });
    </script>
    <script>
        $(".status_filter").click(function() {
            let status = $(this).data("status");
            let urlParams = new URLSearchParams(window.location.search);
            urlParams.set('status', status);
            window.location.search = urlParams.toString();
        });
    </script>
</x-app-layout>
