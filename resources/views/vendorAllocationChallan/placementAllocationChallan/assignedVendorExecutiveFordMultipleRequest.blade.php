<x-app-layout>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>VE Placement Task Allocation</h4>
                </div>

            </div>
            @if (\Session::has('success'))
                <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                    <strong>Success!</strong> {!! \Session::get('success') !!}
                </div>
            @endif

            @if (\Session::has('error'))
                <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                    {!! \Session::get('error') !!}
                </div>
            @endif
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ route('vendor_allocation.allocate_multiple_placement_task') }}"
                        class="mb-2 formUser">
                        @csrf
                        @method('post')
                        <input type="hidden" name="request_type" value="{{ $request_type??'' }}">
                        <div class="row">
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">Selected Request Number for Task Allocation  *</label>
                                    @foreach ($selectdRequestNumber as $key=>$apr)
                                    <input type="hidden" name="request_number[]" id="" value="{{$apr}}">
                                    <p>{{ $key+1 }}: {{ $apr }}</p>
                                    @endforeach

                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">Expected Deployment Date *</label>
                                    <input type="date" class="form-control" name="ExpectedDeploymentDate"
                                        placeholder="Enter Expected Deployment Date" value="{{ date('Y-m-d') }}"
                                        min="{{ date('Y-m-d') }}" max="{{ date('Y-m-d', strtotime('+7 days')) }}"
                                        required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="assigned_organization">Assigned executive *</label>
                                    <select class="form-control select" name="assigned_organization"
                                        id="assigned_organization" required>
                                        <option value="">choose executive</option>
                                        @foreach ($vendorExecutiveList as $ve)
                                            <option value="{{ $ve }}">
                                                {{ $ve }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="d-flex col-12 justify-content-lg-end justify-content-center ">
                                <a href="{{ url()->previous() }}" class="btn btn-cancel">Cancel</a>
                                <button class="btn btn-submit mx-2" type="submit">Submit</button>

                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
