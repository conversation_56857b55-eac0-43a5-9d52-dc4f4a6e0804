<x-app-layout>
    @inject('taskStatusService', 'App\Services\PlacementRequestService\TaskStatusService');
    @php
        $taskStatus = $taskStatusService->getTaskStatus($placementRequest);
    @endphp
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>VE Placement Task Allocation</h4>
                </div>
                {{--  @if (!empty($placementRequest->assigned_organization) && $placementRequest->request_module !='pullout')  --}}
                @if (!empty($placementRequest->assigned_organization))
                <div class="page-title">
                    <a class="btn btn-submit"
                        href="{{ route('admin.download.generate_delivery_challan', ['id' => encrypt($placementRequest->request_number),'request_module'=>$placementRequest->request_module]) }}"><i
                            class="fa fa-download " aria-hidden="true"></i>
                         Generate Delivery & Chiller Installation Challan
                    </a>

                </div>
                @endif
            </div>
            @if (\Session::has('success'))
                <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                    <strong>Success!</strong> {!! \Session::get('success') !!}
                </div>
            @endif

            @if (\Session::has('error'))
                <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                    {!! \Session::get('error') !!}
                </div>
            @endif
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ route('vendor_allocation.allocate_placement_task') }}"
                        class="mb-2 formUser">
                        @csrf
                        @method('post')
                        {{--  <form action="" method="post">  --}}
                        <input type="hidden" name="request_number" value="{{ $placementRequest->request_number }}">
                        <div class="row">
                            <input type="hidden" name="request_module" value="{{ $placementRequest->request_module }}">
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="submitDate">Submit Date:</label>
                                    <input type="date" class="form-control" name="SubmitDate"
                                        value="{{ \Carbon\Carbon::parse($placementRequest->created_at)->format('Y-m-d') }}"
                                        disabled>

                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="RequestNo">Request No:</label>
                                    <input type="text" class="form-control" disabled name="RequestNo"
                                        value="{{ $placementRequest->request_number }}"
                                        placeholder="Enter Request No: ">
                                </div>
                            </div>
                            <div class=" col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="TaskStatus">Request type:</label>
                                    <input type="text" class="form-control" name="Requesttype" value="{{ $placementRequest->placement_request_type }}"
                                        placeholder="Enter Request type:" disabled>
                                </div>
                            </div>
                            <div class=" col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="TaskStatus">Task Status:</label>
                                    <input type="text" class="form-control" name="TaskStatus"
                                        value="{{ $taskStatus }}" placeholder="Enter Task Status:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ASMName">ASM Name:</label>
                                    <input type="text" class="form-control" name="ASMName"
                                        value="{{ $placementRequest->asm_name }}" placeholder="Enter ASM Name:"
                                        disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ASMcontact">ASM Contact</label>
                                    <input type="text" class="form-control" name="ASMcontact"
                                        value="{{ $placementRequest->asm_mobile_number }}"
                                        placeholder="Enter ASM Contact" disabled>
                                </div>
                            </div>

                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="SOName">SO Name:</label>
                                    <input type="text" class="form-control" name="SOName"
                                        value="{{ $placementRequest->so_name }}" placeholder="Enter SO Name:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="SOcontact">SO contact:</label>
                                    <input type="text" class="form-control" name="SOcontact"
                                        value="{{ $placementRequest->so_mobile_number }}"
                                        placeholder="Enter SO contact:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="SOcontact">Applied Chiller Type:</label>
                                    <input type="text" class="form-control" name="SOcontact"
                                        value="{{ $placementRequest->eligible_chiller_type }}"
                                        placeholder="Applied Chiller Type:" disabled>
                                </div>
                            </div>

                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="RetailerCode">Retailer Code:</label>
                                    <input type="text" class="form-control" name="RetailerCode"
                                        value="{{ $placementRequest->retailer_code }}"
                                        placeholder="Enter Retailer Code:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="RetailerName">Retailer Name:</label>
                                    <input type="text" class="form-control" name="RetailerName"
                                        value="{{ $placementRequest->retailer_name }}"
                                        placeholder="Enter Retailer Name:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="DistributorName">Distributor Name:</label>
                                    <input type="text" class="form-control" name="DistributorName"
                                        value="{{ $placementRequest->distributor_name }}"
                                        placeholder="Enter Distributor Name:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="RetailerCity">Retailer City:</label>
                                    <input type="text" class="form-control" name="RetailerCity"
                                        value="{{ $placementRequest->retailer_city }}"
                                        placeholder="Enter Retailer City:" disabled>
                                </div>
                            </div>
                            <div class=" col-12">
                                <div class="form-group">
                                    <label for="RetailerAddress">Retailer Address:</label>
                                    <textarea class="form-control" rows="2" name="RetailerAddress" placeholder="Enter Retailer Address" disabled>{{ $placementRequest->retailer_address }}</textarea>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="Retailercontact">Retailer contact:</label>
                                    <input type="text" class="form-control" name="Retailercontact"
                                        value="{{ $placementRequest->retailer_contact }}"
                                        placeholder="Enter Retailer contact:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="Retailerpincode">Retailer pincode:</label>
                                    <input type="tet" class="form-control" name="Retailerpincode"
                                        value="{{ $placementRequest->retailer_pin_code }}"
                                        placeholder="Enter Retailer pincode:" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="AEApprovedDate">AE Approved Date:</label>
                                    <input type="date" class="form-control" name="AEApprovedDate"
                                        value="{{ \Carbon\Carbon::parse($placementRequest->ae_approval_date)->format('Y-m-d') }}"
                                        placeholder="Enter AE Approved Date:" disabled>
                                </div>
                            </div>


                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="DaysAfterApproval">Days After Approval:</label>
                                    <input type="text" class="form-control" name="DaysAfterApproval"
                                        value="{{ $placementRequest->day_after_approval }}"
                                        placeholder="Enter Days After Approval" disabled>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="ExpectedDeploymentDate">Expected Deployment Date *</label>
                                    <input type="date" class="form-control" name="ExpectedDeploymentDate"
                                        placeholder="Enter Expected Deployment Date" value="{{ \Carbon\Carbon::parse($placementRequest->expected_deployment_date)->format('Y-m-d') }}"
                                        min="{{ date('Y-m-d') }}"
                                        max="{{ date('Y-m-d', strtotime('+7 days')) }}"
                                        required>
                                </div>
                            </div>
                            @php
                                // echo "<pre>";print_r($placementRequest->toArray());
                            @endphp
                            <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="assigned_organization">Assigned executive *</label>
                                    <select class="form-control select" name="assigned_organization"
                                        id="assigned_organization" required>
                                        <option value="">choose executive</option>
                                        @foreach ($placementRequest->vendorExecutiveList as $ve)
                                            <option value="{{ $ve }}"
                                                @if ($placementRequest->assigned_organization == $ve) selected @endif>
                                                {{ $ve }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            {{--  <div class="col-lg-4 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="AssignedOrganization">Assigned Organization *</label>
                                    <input type="text" class="form-control" data-bs-toggle="modal"
                                        data-bs-target="#AssignedModal" name="AssignedOrganization"
                                        placeholder="Please Select">
                                </div>


                                <!-- Modal -->
                                <div class="modal fade" id="AssignedModal" tabindex="-1"
                                    aria-labelledby="AssignedModalLabel" data-bs-backdrop="static"
                                    aria-hidden="true">
                                    <div class="modal-dialog modal-lg modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="AssignedModalLabel">Assigned Organization
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                    aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="filter">
                                                    <div class="row g-3 d-flex align-items-center">
                                                        <div class="col-auto">
                                                            <input type="text" class="form-control"
                                                                placeholder="Organization Code">
                                                        </div>
                                                        <div class="col-auto ">
                                                            <input type="text" placeholder="Organization Name"
                                                                class="form-control">
                                                        </div>
                                                        <div class="col d-flex justify-content-end">

                                                            <button type="button" class="btn btn-mars-color"><i
                                                                    class="fa fa-search" aria-hidden="true"></i>
                                                                Search</button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th scope="col">No.</th>
                                                                <th scope="col">Single Choice</th>
                                                                <th scope="col">Organization Code</th>
                                                                <th scope="col">Organization Name</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <th scope="row">1</th>
                                                                <td>
                                                                    <label class="checkboxs">
                                                                        <input type="checkbox">
                                                                        <span class="checkmarks"></span>
                                                                    </label>
                                                                </td>
                                                                <td>VEHYD1</td>
                                                                <th>VEHYD1</th>
                                                            </tr>
                                                        <tbody>
                                                    </table>
                                                    <hr>
                                                </div>
                                            </div>
                                            <div class="modal-footer justify-content-end">
                                                <button type="button" class="btn btn-mars-color">Confirm</button>
                                                <button type="button" class="btn btn-secondary"
                                                    data-bs-dismiss="modal">Clear</button>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>  --}}
                            <div class="d-flex col-12 justify-content-lg-end justify-content-center ">
                                <a href="{{ url()->previous() }}" class="btn btn-cancel">Cancel</a>
                                <button class="btn btn-submit mx-2" type="submit">Submit</button>

                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
