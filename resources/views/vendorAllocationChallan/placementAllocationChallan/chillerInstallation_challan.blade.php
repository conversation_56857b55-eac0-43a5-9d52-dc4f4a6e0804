<!doctype html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            background-color: #ffffff
        }

        .generate-chiller-challan .logo-text-chiller {
            font-weight: bold;
            font-size: 1.3rem;
            color: #02008f;
            font-family: system-ui;
            letter-spacing: 3px;
            text-align: start
        }

        .generate-chiller-challan .logo-text-small-chiller {
            float: left;
            font-size: 1rem;
            font-weight: 900;
            color: #070689;
            font-family: system-ui;
            letter-spacing: 2px;
            padding: 1rem;
            border-left: 2px solid #02008f;
            text-align: start;
            margin: 0%
        }

        .generate-chiller-challan .logo-text-small-chiller span:first-child {
            color: #db288b;
        }

        .generate-chiller-challan .logo-text-small-chiller span:last-child {
            color: #edb60c;
        }

        .generate-chiller-challan .generate-challan-title {
            font-size: 2rem;
            text-align: center;
            font-weight: 900;
            font-family: serif;
            color: #000;
            margin: 1rem 0
        }

        .table,
        .table-text {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        table tbody tr td {
            font-size: 14px;
        }

        .margin_top {
            margin-top: 30px;
        }

        .table_text_label {
            width: 20%;
            padding: 10px 10px 10px 0;
        }

        .align-span {
            position: absolute;
            left: 25%;
            transform: translate(-0%, -100%);
        }

        .align-span-request {
            position: absolute;
            left: 40%;
            transform: translate(-0%, -100%);
        }

        .align-hr-request {
            margin: 0;
            float: right;
            width: 60%;
            border: none;
            border-bottom: 2px solid #000000;
        }

        .align-hr {
            margin: 0;
            float: right;
            width: 75%;
            border: none;
            border-bottom: 2px solid #000000;
        }

        .chiller-barcode-box {
            display: inline-block;
            border: 1px solid #000;
            width: 60%;
            height: 70px;
            float: right;
        }

        .provided_heading {
            text-align: center;
            margin: 35px;
            color: #000;
            font-weight: 800;
            font-family: ui-serif;
        }

        .form-check-input {
            border: 1px solid #000;
            width: 120px;
            height: 40px;
            float: right;

        }

        .table_check_label {
            padding: 13px 0;
            float: left;

        }

        .table_check_label2 {
            padding: 13px 0;
            float: right;
            margin-left: 0px;
        }

        /*table-tow-delivery-challan*/
        p {
            margin: 0%
        }

        .challan-margin {
            margin-top: 30px
        }

        .table-tow-border,
        .table-tow-border tr td {
            border: 1px solid #000;
        }

        .hr_style {
            margin: 10px 0;
            border-bottom: 0.1px solid#000;
        }

        .table-tow-border .hadding {
            text-align: center;
            font-size: 14px;
            color: #000;
            font-weight: 600;
        }

        .table-tow-border .sub-hadding {
            margin: 12px;
            font-size: 12px;
            display: flex;
        }

        .table-tow-border .hadding-text {
            margin: 12px;
            font-size: 14px;
            display: flex;
        }

        .duplecate {
            display: flex;
            margin: 2px;
        }

        .duplecate-check {
            width: 10px;
            height: 10px;
            display: inline-block;
            border: 1px solid #000;
            margin: 0 2px;
        }

        .duplecate-span {
            color: #db288b;
            font-size: 12px;
            margin: 10px 0;
            font-weight: 600;
        }

        .text_center {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            padding: 10px
        }
    </style>
</head>

<body>
    @foreach ($pdfDataList as $pdfData)
        <table class="table-text">
            <thead class="generate-chiller-challan">
                <tr>
                    <td>
                        <h2 class="logo-text-chiller">MARS WRIGLEY</h2>
                    </td>
                    <td>
                        <h6 class="logo-text-small-chiller">Better <span>moments make <br> the
                                world</span> <span>smile</span>
                        </h6>
                    </td>
                    <td>
                        <div style="border-bottom: 2px solid #000; width: 100%; display: inline-block;"> </div>
                    </td>

                </tr>
                <tr>
                    <td colspan="3">
                        <!-- Generate Challan -->
                        <div class="generate-challan-title">
                            <p style="margin: 30px 0">CHILLER INSTALLATION RECEIPT</p>
                        </div>
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div>
                            <label class="table_text_label">Request No :</label>
                            <div style="position: relative;">
                                <span class="align-span-request">{{ $pdfData['request_number'] }}</span>
                            </div>
                            <hr class="align-hr-request">
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class="margin_top">
                            <label class="table_text_label">Date:</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['date'] }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="margin_top">
                            <label class="table_text_label">DMS CUSTOMER CODE:</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['customer_code'] }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>
                    </td>
                </tr>

                <tr>
                    <td colspan="3">
                        <div class="margin_top">
                            <label class="table_text_label">Outlet Name:</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['outlet_name'] }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="margin_top">
                            <label class="table_text_label">Outlet Address :</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['customer_address'] }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="margin_top">
                            <label class="table_text_label">Owner Name :</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['outlet_name'] }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>

                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="margin_top">
                            <label class="table_text_label">Mobile Number :</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['mobile_number'] }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>

                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="margin_top">
                            <label class="table_text_label">Chiller Type :</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['chiller_type'] }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>

                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="margin_top" style="margin-bottom: 60px; position:relative; ">
                            <label class="table_text_label" style="top: 20px; position: absolute;">CHILLER BARCODE
                                :</label>
                            <div class="chiller-barcode-box">{{ $pdfData['barcode']??'' }}</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="margin_top">
                            <label class="table_text_label">Chiller Serial Number :</label>
                            <div style="position: relative;">
                                <span class="align-span">{{ $pdfData['serial_number']??'' }}</span>
                            </div>
                            <hr class="align-hr">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <h3 class="provided_heading">
                            Accessories Provided (Please Tick)</h3>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div>
                            <label class="table_check_label"> Extension Card</label>
                            <div class="form-check-input"></div>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div>
                            <label class="table_check_label2">Any Other (Pls Mention) </label>
                            <div style="   border: 1px solid #000;width: 120px; height: 40px; float:left;">
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div style="margin-top: 60px">
                            <label class="table_check_label"> Multipin Plug </label>
                            <div class="form-check-input"></div>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div style="margin-top: 60px">
                            <label class="table_check_label2">Cooler Stand </label>
                            <div
                                style="border: 1px solid #000;  width: 120px; height: 40px; margin: 0 0px 0 12px; float:left;">
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div style="margin-top: 60px ">
                            <strong>I have reccived the CHILLER with above mcationed
                                speccification
                                <div style="border-bottom: 2px solid #000; min-width: 100px; display: inline-block;">
                                </div>
                                to be installed at my
                                chiller is in a proper working condition at the time of
                                Installation.
                            </strong>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h3 style="font-family: none; font-weight: 600; margin-top: 60px;">Owners Signature & Stamp
                        </h3>
                    </td>

                    <td colspan="2">
                        <h3 style="font-family: none; font-weight: 600; margin-top: 60px; float:right;">Signature Of
                            MARS
                            Representative</h3>
                    </td>

                </tr>
            </tbody>
        </table>
        <table class="table-text">
            <thead>
                <tr>
                    <td colspan="3">
                        <div class="generate-chiller-challan">
                            <div class="generate-challan-title">
                                <p style="padding: 0; margin:0% ">MARS INTERNATIONAL INDIA PVT <br> LIMITED</p>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="challan-margin" style="display: flex; font-size:14px">
                            <b>Add :</b>
                            <span>{{ $pdfData['vendor_address'] }}</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div style="display: flex; font-size:14px; margin:10px 0">
                            <b>GSTIN :</b>
                            <span>{{ $pdfData['gst_number'] }}</span>
                        </div>
                    </td>
                </tr>
            </thead>
            <tbody class="table-tow-border">
                <tr>
                    <td colspan="2">
                        <div class="hadding">
                            <span> DELIVERY CHALLAN</span>
                        </div>
                    </td>
                    <td>
                        <div class="duplecate">
                            <span class="duplecate-check"></span>
                            <span class="duplecate-span"> ORIGINAL For Consignee</span>
                        </div>
                        <div class="duplecate">
                            <span class="duplecate-check"></span>
                            <span class="duplecate-span"> DUPLICATE For Transporter</span>
                        </div>
                        <div class="duplecate">
                            <span class="duplecate-check"></span>
                            <span class="duplecate-span"> TRIPLICATE For Consignee</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div style="height:30px;"></div>
                    </td>
                </tr>
                <tr>
                    <td class="hadding" style="padding: 10px;">CONSIGNEE(DETAILS OF RECEIVER)</td>
                    <td colspan="2"><b class="sub-hadding"> Challan No. : {{ $pdfData['challan_number'] }}</b></td>
                </tr>
                <tr>
                    <td>
                        <div class="sub-hadding">
                            <b class="text_center">Name </b>
                            <b class="text_center">:</b>
                            <b style="display:inline-block;">{{ $pdfData['outlet_name'] }}-
                                <br>{{ $pdfData['customer_code'] }}</b>
                        </div>
                    </td>
                    <td colspan="2"><b class="sub-hadding"> Challan Date : {{ $pdfData['date'] }}</b></td>
                </tr>
                <tr>
                    <td>
                        <div class="sub-hadding" style="width: 200px;">
                            <b class="">Address :</b>
                            <b style="padding: 10px 0;">{{ $pdfData['customer_address'] }}</b>
                        </div>
                    </td>
                    <td colspan="2">

                        <b class="sub-hadding">Place Of Supply : {{ $pdfData['state'] }}</b>
                        <hr class="hr_style">
                        <b class="sub-hadding">State : {{ $pdfData['state'] }}</b>
                        <hr class="hr_style">
                        <b class="sub-hadding">State Code :  {{ $pdfData['vendor_gst_state_code'] }}</b>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="sub-hadding">
                            <b class="">Retailer GST/UIN NO. :</b>
                            <b style="padding: 10px 0;">{{ $pdfData['customer_gst_number'] }}</b>
                        </div>
                    </td>
                    <td colspan="2">
                        <div class="sub-hadding">
                            <b class="" style="padding: 10px 0">Transportation Mode :</b>
                            <b style="padding: 10px 0;"></b>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="sub-hadding">
                            <b>State :</b>
                            <b style="padding: 10px 0;">{{ $pdfData['state'] }}</b>
                        </div>

                    </td>
                    <td>
                        <div class="sub-hadding">
                            <b>State Code :</b>
                            <b style="padding: 10px 0;">{{ $pdfData['state'] }}</b>
                        </div>
                    </td>
                    <td>
                        <div class="sub-hadding">
                            <b>Vehicle No. :</b>
                            <b style="padding: 10px 0;"></b>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3" style="padding: 0">
                        <table class="table-text" style="margin: 0; border:none; ">

                            <body class="table-tow-border">
                                <tr>
                                    <td>
                                        <b class="sub-hadding">Sr. No.</b>
                                    </td>
                                    <td>
                                        <b class="sub-hadding">Description Of Goods</b>
                                    </td>
                                    <td>
                                        <b class="sub-hadding">item Code</b>
                                    </td>
                                    <td>
                                        <b class="sub-hadding">HSN Code</b>
                                    </td>
                                    <td>
                                        <b class="sub-hadding">Qty</b>
                                    </td>
                                    <td>
                                        <b class="sub-hadding">UOM</b>
                                    </td>
                                    <td>
                                        <b class="sub-hadding">Rate</b>
                                    </td>
                                    <td>
                                        <b class="sub-hadding">Taxable Value</b>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="sub-hadding text_center">1</span></td>
                                    <td><span class="sub-hadding text_center">{{ $pdfData['chiller_type'] }}</span>
                                    </td>
                                    <td><span class="sub-hadding text_center">CT004</span></td>
                                    <td><span class="sub-hadding text_center">{{ $pdfData['hsn_code'] }}</span></td>
                                    <td><span class="sub-hadding text_center">1</span></td>
                                    <td><span class="sub-hadding text_center">BOX</span></td>
                                    <td><span class="sub-hadding text_center">{{ $pdfData['asset_price'] }}</span></td>
                                    <td><span class="sub-hadding text_center">{{ $pdfData['tax_price'] }}</span></td>
                                </tr>
                                <tr>
                                    <td colspan="6"></td>
                                    <td><b class="sub-hadding text_center">Total :</b></td>
                                    <td><b class="sub-hadding text_center">{{ $pdfData['asset_price'] }}</b></td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <div class="hadding-text">
                                            <b>Total Taxable Value (In Word) :</b>
                                            <b style="padding: 10px 0;"></b>
                                        </div>
                                    </td>
                                    <td colspan="4">
                                        <b class="hadding-text">For Mars International India Pvt. Limited</b>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4" style="height: 100px;">
                                        <b class="hadding-text">{{ $pdfData['asset_price_in_word']??''}}</b>
                                    </td>
                                    <td colspan="4">
                                        <div style="height: 100px; position: relative;">
                                            <b class="hadding-text"
                                                style="position: absolute; top: 70%; left: 30%;">Authorised
                                                Signatory</b>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="8">
                                        <div style="height: 40px;">
                                            <b class="hadding-text" style="float: right;">(E & OE)</b>
                                        </div>
                                    </td>
                                </tr>
                            </body>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    @endforeach
</body>

</html>
