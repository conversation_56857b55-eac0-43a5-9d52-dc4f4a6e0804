<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Maintenance Allocation</h4>
                    <h6>View/Maintenance Allocation</h6>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            <button class="btn btn-secondary">Pending</button>
                            <button class="btn btn-primary mx-2">Approval Status</button>
                            <button class="btn btn-danger">Reject</button>
                        </div>
                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-sm-2">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{ asset('img/icons/search-white.svg') }}"
                                        alt="img"></a>
                            </div>
                            <div class="wordset">
                                <button class="btn btn-primary ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="excel"><i class="fa fa-download me-2"
                                        aria-hidden="true"></i>Download</button>
                            </div>
                        </div>
                    </div>
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit From Date</label>
                                                <div class="ui calendar w-100" id="rangestart">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit From Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit To Date</label>
                                                <div class="ui calendar w-100" id="rangeend">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit To Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>DB Code</label>
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer code</label>
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request No.</label>
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>SO Name</label>
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Distributor Name</label>
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request Status</label>
                                            <select class="form-select select">
                                                <option>Task Pending</option>
                                                <option selected>Task Completed</option>
                                                <option value="?">Pending For ASM Approval</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>ASM Name</label>
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer City</label>
                                            <input type="text" class="form-control" placeholder="Customer City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Route Name</label>
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class=" col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">
                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="{{ asset('img/icons/search-whites.svg') }}"
                                                    alt="img"></button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table  datanew">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Submit Date</th>
                                    <th>Request No</th>
                                    <th>Task Status</th>
                                    <th>DSR name</th>
                                    <th>DSR contact</th>
                                    <th>SO Name</th>
                                    <th>SO contact </th>
                                    <th>ASM Name</th>
                                    <th>Distributor Code</th>
                                    <th>Distributor Name</th>
                                    <th>Retailer Code</th>
                                    <th>Retailer Name</th>
                                    <th>Retailer Address</th>
                                    <th>Retailer contact</th>
                                    <th>Retailer pincode</th>
                                    <th>Route Name</th>
                                    <th>Retailer City</th>
                                    <th>Chiller Type</th>
                                    <th>Asset Number</th>
                                    <th>Asset Barcode</th>
                                    <th>Maintenance Reason</th>
                                    <th>Assigned Organization</th>
                                    <th>Expected Maintainance Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>2023-03-14</td>
                                    <td>ARR10000056</td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Tanay Karmakar</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>Somnath Das</td>
                                    <td>1760345810</td>
                                    <td>Ritika Enterprise</td>
                                    <td>1043992610_C0070</td>
                                    <td>Star Tailor</td>
                                    <td>Purbalai(Near Abc Girls High School)</td>
                                    <td></td>
                                    <td></td>
                                    <td>MADHYAMGRAM </td>
                                    <td>North 24 Parganas</td>
                                    <td>Gen 2</td>
                                    <td>1809073000049</td>
                                    <td>M01047077</td>
                                    <td>Cooling Issue</td>
                                    <td></td>
                                    <td>2023-01-12 16:58:07</td>
                                    <td>
                                        <a class="me-2"
                                            href="{{ route('vendor_allocation.maintenance_allocation_edit') }}">
                                            <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewMaintenanceModal">
                                            <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                        </a>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-lg-start justify-content-sm-start
                        justify-content-md-start">
                            <button class="btn btn-primary">Approve</button>
                            <button class="btn btn-secondary mx-2">Reject</button>
                        </div>
                        <div
                            class="col-lg-6 col-sm-6 col-md-6 my-2 d-flex justify-content-center justify-content-md-end justify-content-sm-end justify-content-lg-end">
                            <nav aria-label="Page navigation example">
                                <ul class="pagination round-pagination  bx-pull-right">
                                    <li class="page-item"><a class="page-link" href="javascript:;">Previous</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;javascript:;">1</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="javascript:;">2</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">3</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <div class="modal fade" id="viewMaintenanceModal" data-bs-keyboard="false"
                        data-bs-backdrop="static" tabindex="-1" aria-labelledby="viewMaintenanceModalLabel"
                        aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewMaintenanceModalLabel">View Maintenance Allocation
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4> Submit Date:</h4>
                                                                    <h6>2023-03-14</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Request No:</h4>
                                                                    <h6>ARR10000056</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Task Status:</h4>
                                                                    <h6>Task Pending for Allocation</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DSR name:</h4>
                                                                    <h6>Tanay Karmakar</h6>
                                                                    <!-- <h6 class="manitorygreen">This Field is required</h6> -->
                                                                </li>
                                                                <li>
                                                                    <h4>DSR contact:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO Name:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SO contact:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ASM Name:</h4>
                                                                    <h6>Somnath Das</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Code:</h4>
                                                                    <h6>1760345810</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Distributor Name:</h4>
                                                                    <h6>Ritika Enterprise</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Retailer Code:</h4>
                                                                    <h6>1043992610_C0070</h6>
                                                                </li>



                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>Retailer Name:</h4>
                                                                    <h6>Star Tailor</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Retailer Address:</h4>
                                                                    <h6>Purbalai(Near Abc Girls High School)</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Retailer contact:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Retailer pincode:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Route Name:</h4>
                                                                    <h6>MADHYAMGRAM</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Retailer City:</h4>
                                                                    <h6>North 24 Parganas</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Chiller Type:</h4>
                                                                    <h6>Gen 2</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Asset Number:</h4>
                                                                    <h6>1809073000049</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Asset Barcode:</h4>
                                                                    <h6>M01047077</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Maintenance Reason:</h4>
                                                                    <h6>Cooling Issue</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>Assigned Organization:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Expected Maintainance Date:</h4>
                                                                    <h6>2023-01-12 16:58:07</h6>
                                                                </li>

                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
