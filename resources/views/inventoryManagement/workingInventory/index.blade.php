<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Current Working Inventory</h4>
                    <h6>Current View/Working Inventory</h6>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="table-top ">
                        <div class="search-set">
                            <form action="" method="GET">
                                <div class="form-group d-block d-md-flex d-lg-flex mb-0">
                                    <div class="d-flex">
                                        <div class="field">
                                            <label>Submit From Date</label>

                                            <div class="ui calendar" id="rangestart">
                                                <div class="ui input left icon">
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit From Date"
                                                        name="from_date" value="{{ request()->query('from_date')??'' }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field mx-2">
                                            <label>Submit To Date</label>
                                            <div class="ui calendar" id="rangeend">
                                                <div class="ui input left icon">
                                                    <!-- <i class="calendar icon"></i> -->
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit To Date" name="to_date"
                                                    value="{{ request()->query('to_date')??'' }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field mx-2">
                                            <label>Chiller Type</label>
                                            <select id="" class="form-select" name="act" style="padding-right: 52px">
                                                <option value="{{ encrypt('all') }}">All</option>
                                                @if (!empty($assetChillerType))
                                                    @foreach ($assetChillerType as $chillerType)
                                                        @php
                                                            $chiller = request()->query('act') ? decrypt(request()->query('act')) : '';
                                                            $selected = $chiller == $chillerType;
                                                        @endphp
                                                        <option value="{{ encrypt($chillerType) }}" {{ $selected ? 'selected' : '' }}>
                                                            {{ $chillerType }}
                                                        </option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                        {{--  <div class=" mx-2 form-group mb-0">
                                            <label>Select User </label>
                                            <select class="form-select select " name="user_type" id="user_type">

                                            </select>
                                        </div>  --}}
                                    </div>
                                    <div class="set-search-btn d-flex">
                                        <button type="submit" class="btn btn-primary py-2" data-bs-toggle="tooltip"
                                            data-bs-placement="top"><i class="fa fa-search" aria-hidden="true"></i>
                                        </button>
                                        <a class="btn btn-primary py-2 mx-2 "
                                            href="{{ route('inventory_managemant.working_inventory', ['user_type' => request()->query('user_type')]) }}">Reset</a>

                                    </div>
                                </div>


                            </form>

                        </div>
                        {{--  <div class="wordset col-auto user-download-btn">
                            <button class=" btn btn-primary " data-bs-toggle="tooltip" data-bs-placement="top"
                                title="excel"><i class="fa fa-download me-2" aria-hidden="true"></i>Download
                            </button>
                        </div>  --}}
                    </div>

                    <div class="table-responsive">
                        <table class="table">
                            <tr>

                                <th>
                                    <label class="checkboxs">
                                        <input type="checkbox" id="select-all" class="approvalCheckbox">
                                        <span class="checkmarks"></span>
                                    </label>
                                </th>
                                {{--  <th>Submit Date</th>  --}}
                                <th>Region</th>
                                <th>AssetDescription</th>
                                <th>AssetType</th>
                                <th>Asset Price</th>
                                <th>ModelName</th>
                                <th>Vendor</th>
                                <th>Quantity</th>
                                <th>WarehouseCode</th>
                                <th>ManufacturedYear</th>
                                <th>Asset Serial Number</th>
                                <th>Asset Barcode</th>
                            </tr>
                            <tbody>
                                @if (!empty($inventoryList) && $inventoryList->count() > 0)
                                    @foreach ($inventoryList as $inventory)

                                        <tr>

                                            <td>
                                                <label class="checkboxs ">
                                                    <input type="checkbox" name="" class="approvalCheckbox"
                                                        value="{{ $inventory->id ?? '' }}" >
                                                    <span class="checkmarks"></span>
                                                </label>
                                            </td>
                                            {{--  <td>{{ $inventory->created_at ??'' }}</td>  --}}
                                            {{--  <td>{{ $inventory->created_at ? date("Y-m-d",strtotime($inventory->created_at)):'' }}</td>  --}}
                                            <td>{{ $inventory->cfa->region ?? '' }}</td>
                                            <td>{{ $inventory->description ?? '' }}</td>
                                            <td>{{ $inventory->asset_type ?? '' }}</td>
                                            <td>{{ $inventory->asset_price ?? '' }}</td>
                                            <td>{{ $inventory->model_name ?? '' }}</td>
                                            <td>{{ $inventory->vendor ?? '' }}</td>
                                            <td>{{ $inventory->quantity ?? '' }}</td>
                                            <td>{{ $inventory->warehouse_code ?? '' }}</td>
                                            <td>{{ $inventory->manufactured_year ?? '' }}</td>
                                            <td>{{ $inventory->serial_number ?? '' }}</td>
                                            <td>{{ $inventory->barcode ?? '' }}</td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="13" style="text-align: center;">No Data available</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">

                        <div class="col-12 my-2">
                            <div class="text-right">
                                @if ($inventoryList)
                                {!! $inventoryList->withQueryString()->links('pagination::bootstrap-5') !!}
                                @endif
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
