<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Current Working Inventory</h4>
                    <h6>Current View/Working Inventory</h6>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="table-top ">
                        <div class="search-set">
                            <form action="" method="GET">
                                <div class="form-group d-block d-md-flex d-lg-flex mb-0">
                                    <div class="d-flex">
                                        <div class="field">
                                            <label>Submit From Date</label>

                                            <div class="ui calendar" id="rangestart">
                                                <div class="ui input left icon">
                                                    <!-- <i class="calendar icon"></i> -->
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit From Date"
                                                        name="from_date" value="{{ date('Y-m-d') }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field mx-2">
                                            <label>Submit To Date</label>
                                            <div class="ui calendar" id="rangeend">
                                                <div class="ui input left icon">
                                                    <!-- <i class="calendar icon"></i> -->
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit To Date" name="to_date"
                                                    value="{{ date('Y-m-d') }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field mx-2">
                                            <label>Chiller Type</label>
                                            <select id="" class="form-select" name="act" style="padding-right: 52px">
                                                <option value="{{ encrypt('all') }}">All</option>
                                                @if (!empty($assetChillerType))
                                                    @foreach ($assetChillerType as $chillerType)
                                                        @php
                                                            $chiller = request()->query('act') ? decrypt(request()->query('act')) : '';
                                                            $selected = $chiller == $chillerType;
                                                        @endphp
                                                        <option value="{{ encrypt($chillerType) }}" {{ $selected ? 'selected' : '' }}>
                                                            {{ $chillerType }}
                                                        </option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                        {{--  <div class=" mx-2 form-group mb-0">
                                            <label>Select User </label>
                                            <select class="form-select select " name="user_type" id="user_type">

                                            </select>
                                        </div>  --}}
                                    </div>
                                    <div class="set-search-btn d-flex">
                                        <button type="submit" class="btn btn-primary py-2" data-bs-toggle="tooltip"
                                            data-bs-placement="top"><i class="fa fa-search" aria-hidden="true"></i>
                                        </button>
                                        <a class="btn btn-primary py-2 mx-2 "
                                            href="{{ route('inventory_managemant.working_inventory', ['user_type' => request()->query('user_type')]) }}">Reset</a>

                                    </div>
                                </div>


                            </form>

                        </div>
                        {{--  <div class="wordset col-auto user-download-btn">
                            <button class=" btn btn-primary " data-bs-toggle="tooltip" data-bs-placement="top"
                                title="excel"><i class="fa fa-download me-2" aria-hidden="true"></i>Download
                            </button>
                        </div>  --}}
                    </div>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Submit Date</th>
                                    <th>Request No</th>
                                    <th>Pullout Asset</th>
                                    <th>Placement Asset</th>
                                    <th>Pullout Asset address</th>
                                    <th>Retailer1 contact</th>
                                    <th>Pincode 1</th>
                                    <th>Placement Asset address</th>
                                    <th>Retaiker2 contact</th>
                                    <th>Pincode 2</th>
                                    <th>Task Status</th>
                                    <th>SO Name</th>
                                    <th>SO contact </th>
                                    <th>ASM Name</th>
                                    <th>ASM contact</th>
                                    <th>Distributor Name</th>
                                    <th>Distributor Code</th>
                                    <th>Route Name</th>
                                    <th>Retailer City</th>
                                    <th>Asset Number</th>
                                    <th>Asset Barcode</th>
                                    <th>Expected Replacement Date</th>
                                    <th>AE Approved Time</th>
                                    <th>Assigned Organization</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (!empty($inventoryList) && $inventoryList->count() > 0)
                                @foreach ($inventoryList as $inventory)
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>{{ $inventory->created_at??'' }}</td>
                                    <td>ARR10000056</td>
                                    <td>60 LTR</td>
                                    <td>220 LTR</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>Task Pending for Allocation</td>
                                    <td>Susona Das</td>
                                    <td></td>
                                    <td>Amartya Kumar</td>
                                    <td></td>
                                    <td>Daksh Enterprises</td>
                                    <td>1749243910</td>
                                    <td>KANKARBAGH</td>
                                    <td>PATNA</td>
                                    <td>10022220600492</td>
                                    <td>M01008592</td>
                                    <td>2023-02-19</td>
                                    <td>2023-01-12 16:58:07</td>
                                    <td>VEKOL2</td>
                                    <td>
                                        <a class="me-2"
                                            href="{{ route('inventory_managemant.working_inventory_edit') }}">
                                            <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                        </a>
                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewAbOutletModal">
                                            <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                        </a>

                                    </td>
                                </tr>
                                @endforeach
                                @else
                                    <tr>
                                        <td colspan="11" style="text-align: center;">No Data available</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">

                        <div class="col-12 my-2">
                            <div class="text-right">
                                @if ($inventoryList)
                                {!! $inventoryList->withQueryString()->links('pagination::bootstrap-5') !!}
                                @endif
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
