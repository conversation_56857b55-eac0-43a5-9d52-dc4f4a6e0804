<x-app-layout>
    @php
        $fromDate = request()->query('from_date');
        $toDate = request()->query('to_date');
        $keyword = request()->query('keyword');
        $assetStock = request()->query('inward_outward');
        $inward_outward_details = request()->query('inward_outward_details');
    @endphp
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Inword Outward</h4>
                    <h6>View/Inword Outward</h6>
                </div>
                <div class="col-md-10 col-sm-10 d-flex align-center justify-content-start page-btn uploader-file">
                    <div class=" mx-3 form-group mb-0" style="width: 200px;">
                        {{--  <label>Inventory Stock </label>  --}}
                        <select class="form-select select " name="inward_outward_details" id="inward_outward_details">
                            <option value="summary" {{ $inward_outward_details == 'summary' ? 'selected' : '' }}>Summary
                            </option>
                            <option value="details" {{ $inward_outward_details == 'details' ? 'selected' : '' }}>Details
                            </option>
                        </select>
                    </div>
                    <script>
                        $("#inward_outward_details").change(function() {
                            let selectedVal = $(this).val();
                            var currentUrl = new URL(window.location.href);
                            currentUrl.searchParams.set('inward_outward_details', selectedVal);
                            window.location.href = currentUrl.toString();
                        });
                    </script>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="table-top ">
                        <div class="search-set">
                            <form action="" method="GET">
                                <input type="hidden" name="inward_outward_details" id=""
                                    value="{{ $inward_outward_details }}">
                                <div class="form-group d-block d-md-flex d-lg-flex mb-0">
                                    <div class="d-flex">
                                        <div class="field">
                                            <label>Submit From Date</label>

                                            <div class="ui calendar" id="rangestart">
                                                <div class="ui input left icon">
                                                    <!-- <i class="calendar icon"></i> -->
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit From Date"
                                                        name="from_date" value="{{ $fromDate ?? '' }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field mx-2">
                                            <label>Submit To Date</label>
                                            <div class="ui calendar" id="rangeend">
                                                <div class="ui input left icon">
                                                    <!-- <i class="calendar icon"></i> -->
                                                    <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                    <input type="text" placeholder="Submit To Date" name="to_date"
                                                        value="{{ $toDate ?? '' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class=" mx-2 form-group mb-0">
                                            <label>Search </label>
                                            <input type="text" placeholder="Search .." name="keyword"
                                                value="{{ $keyword ?? '' }}">
                                        </div>
                                        <div class=" mx-3 form-group mb-0" style="width: 122px;">
                                            <label>Inventory Stock </label>
                                            <select class="form-select select " name="inward_outward"
                                                id="inward_outward">
                                                <option value="all" {{ $assetStock == 'all' ? 'selected' : '' }}>All
                                                </option>
                                                <option value="no" {{ $assetStock == 'no' ? 'selected' : '' }}>
                                                    Inward
                                                </option>
                                                <option value="yes" {{ $assetStock == 'yes' ? 'selected' : '' }}>
                                                    OutWard</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="set-search-btn d-flex">
                                        <button type="submit" class="btn btn-primary py-2" data-bs-toggle="tooltip"
                                            data-bs-placement="top"><i class="fa fa-search" aria-hidden="true"></i>
                                        </button>
                                        <a class="btn btn-primary py-2 mx-2 "
                                            href="{{ route('inventory_managemant.inword_outword') }}">Reset</a>

                                    </div>
                                </div>


                            </form>


                        </div>
                        <div class="wordset col-auto user-download-btn">
                            <a href="{{ route('inventory_managemant.inword_outword_report_download',
                             [
                                'from_date' => $fromDate,
                                'to_date' => $toDate,
                                'keyword' => $keyword,
                                'inward_outward' => $assetStock,
                                'inward_outward_details' => $inward_outward_details
                            ]) }}" class=" btn btn-primary " data-bs-toggle="tooltip" data-bs-placement="top"
                                title="InWard Outward Report"><i class="fa fa-download me-2"
                                    aria-hidden="true"></i>Download
                            </a>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>AssetDescription</th>
                                    <th>AssetType</th>
                                    <th>CFA Code</th>
                                    <th>CFA Name</th>
                                    @if ($inward_outward_details == 'details')
                                        <th>Asset Serial Number</th>
                                    @endif

                                    <th>Opening Inventory</th>
                                    <th>Stock in</th>
                                    <th>Stock Out</th>
                                    <th>Closing Inventory</th>

                                    {{--  <th>Action</th>  --}}
                                </tr>
                            </thead>
                            <tbody>
                                @if (!empty($inwardOutwardInventory) && $inwardOutwardInventory->count() > 0)

                                    @foreach ($inwardOutwardInventory as $inventory)
                                        <tr>
                                            <td>
                                                <label class="checkboxs">
                                                    <input type="checkbox">
                                                    <span class="checkmarks"></span>
                                                </label>
                                            </td>
                                            <td>{{ $inventory->description ?? '' }}</td>
                                            <td>{{ $inventory->asset_type ?? '' }}</td>
                                            <td>{{ $inventory->warehouse_code ?? '' }}</td>
                                            <td>{{ $inventory->cfa->name ?? '' }}</td>
                                            @if ($inward_outward_details == 'details')
                                                <td>{{ $inventory->serial_number ?? '' }}</td>
                                            @endif
                                            <td>{{ $inventory->opening_inventory ?? '' }}</td>
                                            <td>{{ $inventory->stock_in ?? '' }}</td>
                                            <td>{{ $inventory->stock_out ?? '' }}</td>
                                            <td>{{ $inventory->closing_inventory ?? '' }}</td>

                                            {{--  <td>

                                        <a class="me-2" href="#" data-bs-toggle="modal"
                                            data-bs-target="#viewAbOutletModal">
                                            <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                        </a>

                                    </td>  --}}
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="9" style="text-align: center;">No View/Inword Outward available
                                        </td>
                                    </tr>
                                @endif
                                <!-- Grand Totals Row -->
                                <tr>

                                    @php
                                        $colspan = $inward_outward_details == 'details' ? 6 : 5;
                                    @endphp
                                    <td colspan="{{ $colspan }}"><strong>Grand Total</strong></td>
                                    <td><strong>{{ $grandTotals['opening_inventory'] ?? 0 }}</strong></td>
                                    <td><strong>{{ $grandTotals['stock_in'] ?? 0 }}</strong></td>
                                    <td><strong>{{ $grandTotals['stock_out'] ?? 0 }}</strong></td>
                                    <td><strong>{{ $grandTotals['closing_inventory'] ?? 0 }}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">
                        <div class="col-12 my-2">
                            <div class="text-right">
                                {!! $inwardOutwardInventory->withQueryString()->links('pagination::bootstrap-5') !!}
                            </div>
                        </div>
                    </div>
                    <!-- Modal -->
                    <div class="modal fade" id="viewAbOutletModal" tabindex="-1"
                        aria-labelledby="viewAbOutletModalLabel" aria-hidden="true">
                        <div class="modal-dialog  modal-dialog-centered modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewAbOutletModalLabel">View Inward-Outward</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-12 ">
                                            <div class="rounded shadow border p-2">
                                                <div class="row">
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>CustomerCode:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CustomerName:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>AssetNumber:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>AssetBarcode:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ChillerTypeCode:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>ChillerTypeName:</h4>
                                                                    <h6 class="manitorygreen">This Field is required
                                                                    </h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Region:</h4>
                                                                    <h6 class="manitoryblue">lorem50</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>ASMCode:</h4>
                                                                    <h6 class="manitoryblue">lorem50</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>ASMName:</h4>
                                                                    <h6 class="manitoryblue">lorem50</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SOCode:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SOName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CFA:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DistributorCode:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>DistributorName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>DSRCode:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>


                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 col-sm-12">
                                                        <div class="productdetails productdetailnew">
                                                            <ul class="product-bar">
                                                                <li>
                                                                    <h4>DSRName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RouteName:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>CustomerAddress:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>


                                                                <li>
                                                                    <h4>City:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>State:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>VPO(INR)Last3PAverage:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>SOcontact:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>RetailerContact:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                                <li>
                                                                    <h4>Customerclass:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>




                                                                <li>
                                                                    <h4>Customergroup:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>CustomerChannel:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>RequestNo.ofPlacement:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>DateofPlacement:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>


                                                                <li>
                                                                    <h4>Createdby(VendorID):</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>

                                                                <li>
                                                                    <h4>Date&TimeofPlacement:</h4>
                                                                    <h6 class="manitoryblue">Field optional</h6>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer justify-content-center">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Reject</button>
                                                        <button type="button"
                                                            class="btn btn-primary">Approve</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
