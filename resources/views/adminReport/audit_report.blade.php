<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Audit Report</h4>
                    <h6>View/Audit Report</h6>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="table-top ">
                        <div class=" search-set">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>

                            <div
                                class=" col status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">

                                <a class="btn btn-warning py-2"
                                    href="{{ route('admin_report.audit_report', ['status' => 'All']) }}">Reset</a>
                            </div>

                        </div>
                        <div class="wordset ">
                            <button class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top"
                                title="excel">
                                <i class="fa fa-download" aria-hidden="true"></i> Download</button>
                        </div>
                    </div>
                    {{--  Filter  --}}
                    @include('layouts.approval_filter')
                    {{-- end  Filter  --}}
                    <div class="table-responsive">
                        <table class="table ">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Region</th>
                                    <th>RSM Name</th>
                                    <th>ASM Area Code</th>
                                    <th>ASM Name</th>
                                    <th>SO territory</th>
                                    <th>SO name</th>
                                    <th>CFA</th>
                                    <th>Date</th>
                                    <th>Distributor Code</th>
                                    <th>Distributor Name</th>
                                    <th>Customer Code</th>
                                    <th>Customer Name</th>
                                    <th>Customer City Name</th>
                                    <th>Channel</th>
                                    <th>DSR Code</th>
                                    <th>DSR Name</th>
                                    <th>Route name</th>
                                    <th>Classification Name</th>
                                    <th>Customer category name</th>
                                    <th>Asset Audit Status</th>
                                    <th>Asset Number</th>
                                    <th>Chiller Type</th>
                                    <th>Asset Barcode QR Code</th>
                                    <th>Scanned Barcode QR Code</th>
                                    <th>Reason For Not Scan</th>
                                    {{--  <th>Condition</th>  --}}
                                    <th>Customer Address</th>
                                    <th>Your Current Address</th>
                                    <th>Pin Code</th>
                                    <th>Distance to Store</th>
                                    <th>Data Upload Time</th>
                                    <th>Remarks</th>
                                    {{--  <th>Image Link</th>  --}}
                                </tr>
                            </thead>
                            <tbody>
                                @if ($assetAuditList->isNotEmpty())
                                    @foreach ($assetAuditList as $asset)
                                        @php
                                            $outlet = $asset->outlet;
                                            $retailer = $outlet->retailer;
                                            $dsr = $asset->outlet->dsr;
                                            $distributor = $asset->outlet->dsr->distributor;
                                            $cfa = $distributor->cfa;
                                            $so = $asset->outlet->dsr->distributor->so;
                                            $asm = $asset->outlet->dsr->distributor->so->asm;
                                            $rsm = $asset->outlet->dsr->distributor->so->asm->rsm;
                                            $outlet_asset = $asset->asset;
                                            $placement = $asset->placement;
                                            $auditStatus =
                                                $asset->barcode_match_status == 'Matched' ? 'Audited' : 'Not Audited';
                                            // echo "<pre>";print_r($asset->toArray());
                                        @endphp
                                        <tr>
                                            <td>
                                                <label class="checkboxs">
                                                    <input type="checkbox">
                                                    <span class="checkmarks"></span>
                                                </label>
                                            </td>
                                            <td>{{ $rsm->region ?? '' }}</td>
                                            <td>{{ $rsm->name ?? '' }}</td>
                                            <td>{{ $asm->area_code ?? '' }}</td>
                                            <td>{{ $asm->name ?? '' }}</td>
                                            <td>{{ $so->teritory_code ?? '' }}</td>
                                            <td>{{ $so->name ?? '' }}</td>
                                            <td>{{ $cfa->user_id ?? '' }}</td>
                                            <td>{{ $so->created_at ?? '' }}</td>
                                            <td>{{ $distributor->user_id ?? '' }}</td>
                                            <td>{{ $distributor->name ?? '' }}</td>
                                            <td>{{ $retailer->user_id ?? '' }}</td>
                                            <td>{{ $retailer->name ?? '' }}</td>
                                            <td>{{ $retailer->city ?? '' }}</td>
                                            <td>{{ $asm->channel_type ?? '' }}</td>
                                            <td>{{ $dsr->user_id ?? '' }}</td>
                                            <td>{{ $dsr->name ?? '' }}</td>
                                            <td>{{ $outlet->route_name ?? '' }}</td>
                                            <td>{{ $outlet->class_name ?? '' }}</td>
                                            <td>{{ $outlet->category_name ?? '' }}</td>
                                            <td>{{ $auditStatus ?? '' }}</td>
                                            <td>{{ $outlet_asset->asset_barcode ?? '' }}</td>
                                            <td>{{ $outlet_asset->placement->eligible_chiller_type ?? '' }}</td>
                                            <td>{{ $outlet_asset->asset_barcode ?? '' }}</td>
                                            <td>{{ $asset->scanned_barcode ?? '' }}</td>
                                            <td>{{ $asset->barcode_no_scan_reason ?? '' }}</td>
                                            {{--  <td></td>  --}}
                                            <td>{{ $outlet->invoiceaddress ?? '' }}</td>
                                            <td>{{ $asset->current_location }}</td>
                                            <td></td>
                                            <td>{{ $asset->distance }}</td>
                                            <td>{{ $asset->created_at }}</td>
                                            <td>{{ $asset->remarks }}</td>
                                            {{--  <td>
                                        <img class="table-report-img" src="{{ asset('img/brand/retailerPic.avif') }}"
                                            alt="">
                                    </td>  --}}
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="10">
                                            No data found!
                                        </td>

                                    </tr>
                                @endif

                            </tbody>
                        </table>
                    </div>
                    <div class="row btn-approve-reject">
                        <div class="col-12 my-2">
                            {{--  @can('User access')  --}}
                            <div class="text-right">
                                {!! $assetAuditList->withQueryString()->links('pagination::bootstrap-5') !!}
                            </div>
                            {{--  @endcan  --}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
