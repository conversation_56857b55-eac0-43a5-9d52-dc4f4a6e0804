<x-app-layout>
    @php
        $roleCheck = ['SO', 'ASM', 'RSM', 'AE', 'VE', 'VENDOR'];
        $user = Auth::user();
    @endphp
    @inject('taskStatusService', 'App\Services\PlacementRequestService\TaskStatusService');
    <style>
        .fixed-layout-table th,
        .fixed-layout-table td {
            vertical-align: middle;
            /* Aligns content vertically in the middle */
        }

        .chiller-type-header,
        .fixed-layout-table td {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .highlight {
            background-color: yellow;
            /* Highlight color */
            font-weight: bold;
        }
    </style>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Asset Mapping Report</h4>
                    <h6>View/Asset Mapping Report</h6>
                </div>

            </div>
            <div class="card">
                <div class="card-body">
                    <div class=" row table-top ">
                        <div
                            class=" col-auto status-btn d-flex  justify-content-center justify-content-lg-start justify-content-md-start">
                            <button class="btn btn-success mx-1 status_filter" data-status="All">All</button>
                            <a class="btn btn-warning py-2"
                                href="{{ route('approval_center.placement', ['status' => 'All']) }}">Reset</a>
                        </div>

                        <div
                            class=" col search-set justify-content-center justify-content-md-end justify-content-lg-end my-2">
                            {{--  <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>  --}}
                            {{--  <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{asset('img/icons/search-white.svg')}}"
                                        alt="img"></a>
                            </div>  --}}
                            <div class="wordset">

                                <a href="{{ route('approval_center.placement_request_report_download', [
                                    'so_teritory' => request()->query('so_teritory') ?? '',
                                    'asm_teritory' => request()->query('asm_teritory') ?? '',
                                    'db_code' => request()->query('db_code') ?? '',
                                    'distributor_name' => request()->query('distributor_name') ?? '',
                                    'customer_name' => request()->query('customer_name') ?? '',
                                    'customer_city' => request()->query('customer_city') ?? '',
                                    'customer_code' => request()->query('customer_code') ?? '',
                                    'route_name' => request()->query('route_name') ?? '',
                                    'request_no' => request()->query('request_no') ?? '',
                                    'status' => request()->query('status') ?? '',
                                    'request_status' => request()->query('request_status') ?? '',
                                    'FromDate' => request()->query('FromDate') ?? '',
                                    'ToDate' => request()->query('ToDate') ?? '',
                                    'chiller_placed' => 'Yes',
                                ]) }}"
                                    class=" btn btn-primary " data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Placement Report Download">
                                    <i class="fa fa-download me-2" aria-hidden="true"></i>Download

                                </a>
                            </div>
                        </div>
                    </div>
                    {{--  Filter  --}}
                    @include('layouts.approval_filter',['asset_mode'=>'placement'])
                    {{-- end  Filter  --}}

                    {{--  table   --}}
                    @if (\Session::has('success'))
                        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                            <strong>Success!</strong> {!! \Session::get('success') !!}
                        </div>
                    @endif

                    @if (\Session::has('error'))
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            {!! \Session::get('error') !!}
                        </div>
                    @endif

                    <div class="table-responsive">

                        <div id="ajax_message"></div>
                        <table class="table ">
                            <thead>
                                <tr>
                                     <th>Region</th>
                                    <th>Request Number</th>
                                    <th>Submit Date</th>
                                    <th>Request Status</th>
                                    <th>Request Type</th>
                                    <th class="chiller-type-header">Chiller Type</th>
                                    <th>Expected VPO</th>
                                    <th>Customer Code</th>
                                    <th>Customer Name</th>
                                    <th>Contact Number</th>
                                    <th>RTMM Name</th>
                                    <th>ASM Name</th>
                                    <th>ASM Territory</th>
                                    <th>SO Name</th>
                                    <th>SO Territory</th>
                                    <th>Distributor Name</th>
                                    <th>Distributor Code</th>
                                    <th>DSR Name</th>
                                    <th>DSR Code</th>
                                    <th>CFA</th>
                                    <th>Assigned VE</th>
                                    <th>Channel Code</th>
                                    <th>Outlet classification</th>
                                    <th>Customer Category</th>
                                    <th>Route Name</th>
                                    <th>Customer City </th>
                                    {{--  <th>Customer Address</th>  --}}
                                    <th>Address 1</th>
                                    {{--  <th>Address 1</th>  --}}
                                    <th>Address 2</th>
                                    <th>Pin Code</th>
                                    <th>RTMM Approval Time</th>
                                    <th>ASM Approval Time</th>
                                    <th>SO Placement Time</th>

                                    <th>Deployment date</th>
                                    <th>Deployment Status</th>
                                    <th>Asset Serial Number </th>
                                    <th>Asset Barcode </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (is_array($assetPlacementListView) && !empty($assetPlacementListView))

                                    @foreach ($assetPlacementListView as $asset)
                                        <tr data-pending-from="{{ $asset->pending_from ?? '' }}">

                                            <td>{{ $asset->region ?? '' }}</td>

                                            <td>{{ $asset->request_number ?? '' }}</td>
                                            <td>{{ $asset->created_at ?? '' }}</td>

                                            <td>{{ $asset->taskStatus ?? '' }}</td>
                                            <td>{{ $asset->request_type ?? '' }}</td>
                                            <td>{{ $asset->eligible_chiller_type }} </td>


                                            <td>{{ $asset->expected_vpo ?? '' }}</td>
                                            <td>{{ $asset->retailer_user_id ?? '' }}</td>
                                            <td>{{ $asset->retailer_name ?? '' }}</td>
                                            <td>{{ $asset->mobile_number ?? ($asset->retailer_mobile_number ?? '') }}
                                            </td>
                                            <td>{{ $asset->rsm_name ?? '' }}</td>
                                            <td>{{ $asset->asm_name ?? '' }}</td>
                                            <td>{{ $asset->asm_area_code ?? '' }}</td>
                                            <td>{{ $asset->so_name ?? '' }}</td>
                                            <td>{{ $asset->teritory_code ?? '' }}</td>
                                            <td>{{ $asset->db_name ?? '' }}</td>
                                            <td>{{ $asset->db_code ?? '' }}</td>
                                            <td>{{ $asset->dsr_name ?? '' }}</td>
                                            <td>{{ $asset->dsr_code ?? '' }}</td>
                                            <td>{{ $asset->cfa_code ?? '' }}</td>
                                            <td>{{ $asset->assigned_organization ?? '' }}</td>
                                            <td>{{ $asset->channel_code ?? '' }}</td>
                                            <td>{{ $asset->class_name ?? '' }}</td>
                                            <td>{{ $asset->category_name ?? '' }}</td>
                                            <td>{{ $asset->route_name ?? '' }}</td>
                                            <td>{{ $asset->city ?? '' }}</td>
                                            <td>{{ $asset->address_1 ?? '' }}</td>
                                            {{--  outlet address 1  --}}
                                            {{--  <td>{{ $outlet->address_1 ?? '' }}</td>  --}}
                                            {{--  Placement customer address  --}}

                                            <td>{{ $asset->customer_address ?? '' }}</td>
                                            <td>{{ $asset->pin_code ?? '' }}</td>
                                            <td>{{ $asset->rsm_approval_time ?? '' }}</td>
                                            <td>{{ $asset->asm_approval_time ?? '' }}</td>
                                            <td>{{ $asset->created_at ?? '' }}</td>
                                            <td>{{ $asset->deployment_date ?? '' }}</td>
                                            <td>{{ $asset->deploymentStatus ?? '' }}
                                            </td>
                                            <td>{{ $asset->asset_serial_number ?? '' }} </td>
                                            <td>{{ $asset->asset_barcode ?? '' }} </td>
                                            <td>
                                                {{--  <a class="me-2"
                                                    href="{{ route('approval_center.placement_edit', ['id' => 1]) }}">
                                                    <img src="{{ asset('img/icons/edit.svg') }}" alt="img">
                                                </a>  --}}
                                                <a class="me-2 view-placement" href="#" data-bs-toggle="modal"
                                                    data-asset="{{ json_encode([
                                                        'request_number' => e($asset->request_number ?? ''),
                                                        'submit_date' => e($asset->created_at ?? ''),
                                                        'request_status' => e($asset->taskStatus ?? ''),
                                                        'expected_vpo' => e($asset->expected_vpo ?? ''),
                                                        'customer_code' => e($asset->retailer_user_id ?? ''),
                                                        'customer_name' => e($asset->retailer_name ?? ''),
                                                        'asm_name' => e($asset->asm_name ?? ''),
                                                        'so_name' => e($asset->so_name ?? ''),
                                                        'distributor_name' => e($asset->db_name ?? ''),
                                                        'distributor_code' => e($asset->db_code ?? ''),
                                                        'route_name' => e($asset->route_name ?? ''),
                                                        'customer_city' => e($asset->city ?? ''),
                                                        'customer_address' => e($asset->customer_address ?? ''),
                                                        'customer_mobile_number' => e($asset->mobile_number ?? ($asset->retailer_mobile_number ?? '')),
                                                        'rtmm_approval_time' => e($asset->rsm_approval_time ?? ''),
                                                        'remarks' => e($asset->remarks ?? ''),
                                                        'signature' => e($asset->signature ?? ''),
                                                        'deployment_status' => e($asset->deploymentStatus ?? ''),
                                                        'competitor_chiller_photo' => e($asset->competitor_chiller_photo ?? ''),
                                                        'chiller_location_photo' => e($asset->chiller_location_photo ?? ''),
                                                        'address_proof' => e($asset->address_proof ?? ''),
                                                        'retailer_photo' => e($asset->retailer_photo ?? ''),
                                                    ]) }}">
                                                    <img src="{{ asset('img/icons/eye.svg') }}" alt="img">
                                                </a>

                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="10">
                                            No data found!
                                        </td>

                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>

                    {{-- end table   --}}
                    <div class="row btn-approve-reject">

                        <div class="col-12 my-2">
                            {{-- Check if pagination exists --}}
                            @if ($assetPlacementList->lastPage() > 1)
                                <div class="text-right">
                                    {!! $assetPlacementList->withQueryString()->links('pagination::bootstrap-5') !!}
                                    {{--  {{$assetPlacementList->links()}}  --}}
                                </div>
                            @endif

                        </div>

                        <!-- Modal -->
                        <div class="modal fade" id="viewPlacementApprovalModal" data-bs-keyboard="false"
                            data-bs-backdrop="static" tabindex="-1" aria-labelledby="viewPlacementApprovalModalLabel"
                            aria-hidden="true">
                            <div class="modal-dialog  modal-dialog-centered modal-xl">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="viewPlacementApprovalModalLabel">View Approval
                                            Placement
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                            aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        {{--  <form>  --}}
                                        <div class="row">
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="request_number" class="form-label">Request No:</label>
                                                    <input type="text" class="form-control" id="request_number"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="submit_date" class="form-label">Submit Date:</label>
                                                    <input type="text" class="form-control" id="submit_date"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="request_status" class="form-label">Request
                                                        Status:</label>
                                                    <input type="text" class="form-control" id="request_status"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="expected_vpo" class="form-label">Expected VPO:</label>
                                                    <input type="text" class="form-control" id="expected_vpo"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="customer_code" class="form-label">Customer
                                                        Code:</label>
                                                    <input type="text" class="form-control" id="customer_code"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="customer_name" class="form-label">Customer
                                                        Name:</label>
                                                    <input type="text" class="form-control" id="customer_name"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="asm_name" class="form-label">ASM Name:</label>
                                                    <input type="text" class="form-control" id="asm_name"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="so_name" class="form-label">SO Name:</label>
                                                    <input type="text" class="form-control" id="so_name"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="distributor_name" class="form-label">Distributor
                                                        Name:</label>
                                                    <input type="text" class="form-control" id="distributor_name"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="distributor_code" class="form-label">Distributor
                                                        Code:</label>
                                                    <input type="text" class="form-control" id="distributor_code"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="route_name" class="form-label">Route Name:</label>
                                                    <input type="text" class="form-control" id="route_name"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="customer_city" class="form-label">Customer City
                                                        Name:</label>
                                                    <input type="text" class="form-control" id="customer_city"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="customer_address" class="form-label">Customer
                                                        Address:</label>
                                                    <input type="text" class="form-control" id="customer_address"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="customer_mobile_number" class="form-label">Customer
                                                        contact:</label>
                                                    <input type="text" class="form-control"
                                                        id="customer_mobile_number" readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="remarks" class="form-label">Remarks:</label>
                                                    <input type="text" class="form-control manitorygreen"
                                                        id="remarks" readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="rtmm_approval_time" class="form-label">RTMM Approval
                                                        Time:</label>
                                                    <input type="text" class="form-control"
                                                        id="rtmm_approval_time" readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6">
                                                <div class="mb-3">
                                                    <label for="deployment_status" class="form-label">Deployment
                                                        Status:</label>
                                                    <input type="text" class="form-control" id="deployment_status"
                                                        readonly>
                                                </div>
                                            </div>

                                            <div class="col-lg-3 col-sm-6 signature">
                                                <div class="mb-3">
                                                    <label for="signature" class="form-label">Signature:</label>
                                                </div>
                                                <div class="mb-3">
                                                    <img src="" alt="Image" id="signature"
                                                        class="img-fluid" width="80" height="80">
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6 competitor_chiller_photo">
                                                <div class="mb-3">
                                                    <label for="competitor_chiller_photo"
                                                        class="form-label">Competitor chiller photo:</label>
                                                </div>
                                                <div class="mb-3">
                                                    <img src="" alt="Image" id="competitor_chiller_photo"
                                                        class="img-fluid" width="80" height="80">
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6 chiller_location_photo">
                                                <div class="mb-3">
                                                    <label for="chiller_location_photo" class="form-label">Chiller
                                                        location photo:</label>
                                                </div>
                                                <div class="mb-3">
                                                    <img src="" alt="Image" id="chiller_location_photo"
                                                        class="img-fluid" width="80" height="80">
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6 address_proof">
                                                <div class="mb-3">
                                                    <label for="address_proof" class="form-label">Address
                                                        proof:</label>
                                                </div>
                                                <div class="mb-3">
                                                    <img src="" alt="Image" id="address_proof"
                                                        class="img-fluid" width="80" height="80">
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-sm-6 retailer_photo">
                                                <div class="mb-3">
                                                    <label for="retailer_photo"
                                                        class="form-label">Retailer_photo:</label>
                                                </div>
                                                <div class="mb-3">
                                                    <img src="" alt="Image" id="retailer_photo"
                                                        class="img-fluid" width="80" height="80">
                                                </div>
                                            </div>

                                        </div>
                                        {{--  </form>  --}}
                                    </div>
                                    <div class="modal-footer justify-content-end">
                                        <button type="button" class="btn btn-secondary"
                                            data-bs-dismiss="modal">Close</button>

                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script>
            $(document).ready(function() {
                $('.approvalStatusBtn').click(function(e) {

                    $('#ajax_message').html('');
                    $('.validatin-error').remove();
                    var selectedIds = [];
                    $('.approvalCheckbox:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    // Send AJAX request to Laravel route
                    if (selectedIds.length > 0) {
                        showLoader();
                        let status = $(this).attr('data-status');

                        // Prompt for rejection reason if status is reject
                        if (status == 'Rejected') {
                            var rejectionReason = prompt('Please enter the reason for rejection:');
                            if (rejectionReason === null || rejectionReason === "") {
                                // If rejection reason is empty or cancelled, do not proceed
                                hideLoader();
                                return;
                            }
                        }

                        $.ajax({
                            url: "{{ route('approval_center.replacement_approved_reject') }}",
                            method: 'POST',
                            dataType: 'JSON',
                            data: {
                                selectedIds: selectedIds,
                                status: status,
                                rejection_reason: (status == 'Rejected') ? rejectionReason : null
                            },
                            success: function(response) {
                                hideLoader();
                                if (response.success) {
                                    $('#ajax_message').html(success_message(response
                                        .success));
                                    $('html, body').scrollTop(0);
                                    setTimeout(function() {
                                        location.reload();
                                    }, 2000);
                                } else {
                                    $('#ajax_message').html(error_message(response
                                        .error));
                                    $('html, body').scrollTop(0);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.log("headewr", xhr
                                    .responseJSON);
                                hideLoader();
                                $('#ajax_message').html('Error: ' + error_message(xhr
                                    .responseJSON.error));
                                $('html, body').scrollTop(0);
                            }
                        });

                    } else {
                        $('#ajax_message').html(error_message('Please select at least one Placement Request.'));
                        $('html, body').scrollTop(0);
                        setTimeout(function() {
                            $('#ajax_message').html('');
                        }, 2000);
                        return false;

                    }
                });
            });
        </script>
        <script>
            $(".assignedExecutive").click(function() {
                let sIds = [];
                $('.approvalCheckbox:checked').each(function() {
                    sIds.push($(this).val());
                });
                let buttonStatus = $(this).attr('data-status');
                if (sIds.length > 0) {
                    let idsString = sIds.join('#$');
                    let redirectUrl = buttonStatus == 'assignedExecutive' ?
                        "{{ route('vendor_allocation.assigned_executive_allocation') }}?aprID=" +
                        encodeURIComponent(idsString) : "{{ route('admin.download.multiple_challan_pdf') }}?aprID=" +
                        encodeURIComponent(idsString);
                    window.location.href = redirectUrl;
                } else {
                    alert('Please select at least one checkbox.');
                }
            });

            $(".status_filter").click(function() {
                let status = $(this).data("status");
                let urlParams = new URLSearchParams(window.location.search);
                urlParams.set('status', status);
                window.location.search = urlParams.toString();
            });
        </script>
        <script>
            $(document).ready(function() {
                $('.view-placement').on('click', function() {
                    // Retrieve assetData from data-asset attribute
                    var assetData = JSON.parse($(this).attr('data-asset'));
                    // Loop through each key-value pair in assetData
                    $.each(assetData, function(key, value) {
                        if (key === 'signature' || key === 'competitor_chiller_photo' || key ===
                            'chiller_location_photo' ||
                            key === 'address_proof' || key === 'retailer_photo'
                        ) {
                            if (!value || value === null || value == 'NULL') {
                                $('.' + key).hide();
                            } else {
                                $('.' + key).show();
                                $('#' + key).attr('src', value);
                            }
                        } else {
                            $('#' + key).val(value);
                        }

                    });
                    $('#viewPlacementApprovalModal').modal('show');
                });
            });
        </script>
</x-app-layout>
