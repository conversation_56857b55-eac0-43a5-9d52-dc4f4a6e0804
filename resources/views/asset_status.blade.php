<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Status</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container {
            display: flex;
            justify-content: space-between;
            margin: 20px;
        }

        .table-section, .right-section {
            width: 48%;
            border: 1px solid black;
            padding: 20px;
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
            background-color: #fff;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid black;
        }

        th {
            background-color: #f2f2f2;
        }

        .form-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid black;
            background-color: #f9f9f9;
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
            text-align: left;
        }

        .form-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .form-group label {
            flex: 0 0 200px; /* Adjust width of the label */
            padding-right: 10px;
            text-align: left;
        }

        .form-group input {
            flex: 0.5;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 5px;
        }

        .form-group button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .form-group button:hover {
            background-color: #45a049;
        }

        .message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>

    <!-- Form Section -->
    <div class="form-section">
        <form method="POST" action="{{ route('asset.check_asset_status',['_access_token'=>request()->_access_token]) }}">
            @csrf
            <input type="hidden" name="_access_token" value="66e1ee9824a84"> <!-- Your static access token -->

            <div class="form-group">
                <label for="serial_number">Search Asset Serial Number:</label>
                <input type="text" name="serial_number" id="serial_number" placeholder="Enter Serial Number" required>
                <button type="submit">Search</button>
            </div>
        </form>

        @if (session('message'))
            <div class="message {{ session('status') == 'success' ? 'success' : 'error' }}">
                {{ session('message') }}
            </div>
        @endif
    </div>


    @if (!empty($asset))


    <div class="container">
        <!-- Left Table Section -->
        <div class="table-section">
            <table>
                <h1>Asset Status</h1>
                <tbody>
                    <tr>
                        <th>Asset Status </th>
                        @if ($asset->assigned_status == 'yes')
                        <td style="color: red">This asset is already assigned.</td>
                        @else
                        <td style="color: green">Not Assigned</td>
                        @endif
                    </tr>
                    <tr>
                        <th>Asset Type</th>
                        <td>{{ $asset->asset_type }}</td>
                    </tr>
                    <tr>
                        <th>Asset Serial Number</th>
                        <td>{{ $asset->serial_number }}</td>
                    </tr>
                    <tr>
                        <th>Barcode</th>
                        <td>{{ $asset->barcode }}</td>
                    </tr>
                    <tr>
                        <th>Model Name</th>
                        <td>{{ $asset->model_name }}</td>
                    </tr>
                    <tr>
                        <th>Vendor</th>
                        <td>{{ $asset->vendor }}</td>
                    </tr>
                    <tr>
                        <th>Quantity</th>
                        <td>{{ $asset->quantity }}</td>
                    </tr>
                    <tr>
                        <th>Warehouse Code</th>
                        <td>{{ $asset->warehouse_code }}</td>
                    </tr>
                    <tr>
                        <th>Manufactured Year</th>
                        <td>{{ $asset->manufactured_year }}</td>
                    </tr>
                    <tr>
                        <th>Assigned Status</th>
                        <td>{{ $asset->assigned_status }}</td>
                    </tr>
                    <tr>
                        <th>Asset Price</th>
                        <td>{{ $asset->asset_price }}</td>
                    </tr>
                    <tr>
                        <th>Approval Status</th>
                        <td>{{ $asset->asset_approval_status }}</td>
                    </tr>
                    <tr>
                        <th>Approved By</th>
                        <td>{{ $asset->approved_by_user }}</td>
                    </tr>
                    <tr>
                        <th>VPO Target</th>
                        <td>{{ $asset->vpo_target }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Right Side Section -->
        @if (!empty($asset->assetPlacementRequest))
        <div class="right-section">
            @php
            $placement = $asset->assetPlacementRequest;
            @endphp
            <table>
                <h1>Assigned Asset Status</h1>
                <tbody>
                    <tr>
                        <th>Request Submitted</th>
                        <td>{{ $placement->created_at ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>Request Number</th>
                        <td>{{ $placement->request_number ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>Asset Type</th>
                        <td>{{ $placement->eligible_chiller_type ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>Asset Serial Number</th>
                        <td>{{ $placement->asset_serial_number ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>Customer Code</th>
                        <td>{{ $placement->outlet_code ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>AE Code</th>
                        <td>{{ $placement->ae_code ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>RSM Code</th>
                        <td>{{ $placement->rsm_code ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>ASM Code</th>
                        <td>{{ $placement->asm_code ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>SO Code</th>
                        <td>{{ $placement->so_code ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>Vendor</th>
                        <td>{{ $placement->cfa_code ?? '' }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        @endif
    </div>
    @endif
</body>
</html>
