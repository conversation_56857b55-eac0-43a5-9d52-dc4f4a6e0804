<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Placement Report</h4>
                    <h6>View/Placement Report</h6>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="table-top ">
                        <div class=" search-set">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{ asset('img/icons/search-white.svg') }}"
                                        alt="img"></a>
                            </div>

                        </div>
                        <div class="wordset ">
                            <button class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top"
                                title="excel">
                                <i class="fa fa-download" aria-hidden="true"></i> Download</button>
                        </div>
                    </div>
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit From Date</label>
                                                <div class="ui calendar w-100" id="rangestart">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit From Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit To Date</label>
                                                <div class="ui calendar w-100" id="rangeend">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit To Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>DB Code</label>
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer code</label>
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request No.</label>
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>SO Name</label>
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Distributor Name</label>
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request Status</label>
                                            <select class="form-select select">
                                                <option>Task Pending</option>
                                                <option selected>Task Completed</option>
                                                <option value="?">Pending For ASM Approval</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>ASM Name</label>
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer City</label>
                                            <input type="text" class="form-control" placeholder="Customer City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Route Name</label>
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class=" col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">
                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="{{ asset('img/icons/search-whites.svg') }}" alt="img"></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table  datanew">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Region</th>
                                    <th>RSM Name</th>
                                    <th>ASM Area Code</th>
                                    <th>ASM Name</th>
                                    <th>SO TTY.Code</th>
                                    <th>SO Name</th>
                                    <th>Distributor Code</th>
                                    <th>Distributor Name</th>
                                    <th>Retailer code</th>
                                    <th>Retailer Name</th>
                                    <th>Salesman Code</th>
                                    <th>Salesman Name</th>
                                    <th>Route Code</th>
                                    <th>Route Name</th>
                                    <th>City</th>
                                    <th>State</th>
                                    <th>Address</th>
                                    <th>Pin Code</th>
                                    <th>Channel Code</th>
                                    <th>Class Name</th>
                                    <th>Category Name</th>
                                    <th>Submit Date</th>
                                    <th>Request No</th>
                                    <th>Pullout No</th>
                                    <th>Placement No</th>
                                    <th>Request Status</th>
                                    <th>Approval Type</th>
                                    <th>Existing Chiller Types</th>
                                    <th>Existing Chiller Bar code</th>
                                    <th>Existing Chiller serial no.</th>
                                    <th>Applied Chiller Type</th>
                                    <th>Current VPO</th>
                                    <th>Expected VPO</th>
                                    <th>Remarks</th>
                                    <th>Data Upload Time</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>West</td>
                                    <td>Saurabh Sharma</td>
                                    <td>ASM-TT-Gr MUMBAI &amp; Goa</td>
                                    <td>Sandip Sangale</td>
                                    <td>SO-TT-Navi Mumbai</td>
                                    <td>Devender Puri</td>
                                    <td>1758449110</td>
                                    <td>Laxmi Traders</td>
                                    <td>RETN_795590</td>
                                    <td>Scoofy Staishnorys</td>
                                    <td>DSR_000027</td>
                                    <td>Rahul Gaikwad</td>
                                    <td></td>
                                    <td>Belapur - Goan</td>
                                    <td>Mumbai</td>
                                    <td>Maharashtra</td>
                                    <td>sec17</td>
                                    <td></td>
                                    <td>TT</td>
                                    <td>Bronze</td>
                                    <td>Grocery Small</td>
                                    <td>2/17/24</td>
                                    <td>ARR10000116</td>
                                    <td>ARR1000011601-Replacement</td>
                                    <td>ARR1000011602-Replacement</td>
                                    <td>Pending for ASM Approval</td>
                                    <td>Exceptional</td>
                                    <td>70 LTR</td>
                                    <td></td>
                                    <td></td>
                                    <td>70 LTR</td>
                                    <td>0</td>
                                    <td>10000</td>
                                    <td>upgrade</td>
                                    <td>2/17/24 19:29</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class=" btn-approve-reject my-2">
                        <div class="d-flex justify-content-center justify-content-sm-end justify-content-lg-end">
                            <nav aria-label="Page navigation example">
                                <ul class="pagination round-pagination  bx-pull-right">
                                    <li class="page-item"><a class="page-link" href="javascript:;">Previous</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;javascript:;">1</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="javascript:;">2</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">3</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</x-app-layout>
