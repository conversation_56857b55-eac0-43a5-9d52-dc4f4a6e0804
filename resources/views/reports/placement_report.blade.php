<x-app-layout>
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Placement Report</h4>
                    <h6>View/Placement Report</h6>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="table-top ">
                        <div class=" search-set">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="{{ asset('img/icons/filter.svg') }}" alt="img">
                                    <span><img src="{{ asset('img/icons/closes.svg') }}" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="{{ asset('img/icons/search-white.svg') }}"
                                        alt="img"></a>
                            </div>

                        </div>
                        <div class="wordset ">
                            <button class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top"
                                title="excel">
                                <i class="fa fa-download" aria-hidden="true"></i> Download</button>
                        </div>
                    </div>
                    <div class="card" id="filter_inputs">
                        <div class="card-body pb-0">
                            <form action="" method="get">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit From Date</label>
                                                <div class="ui calendar w-100" id="rangestart">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit From Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12 mb-md-4">
                                        <div class="form-group">
                                            <div class="field">
                                                <label>Submit To Date</label>
                                                <div class="ui calendar w-100" id="rangeend">
                                                    <div class="ui input left icon w-100">
                                                        <i class="fa fa-calendar calendar icon" aria-hidden="true"></i>
                                                        <input type="text" placeholder="Submit To Date">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>DB Code</label>
                                            <input type="text" class="form-control" placeholder="DB Code"
                                                name="db_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer code</label>
                                            <input type="text" class="form-control" placeholder="Customer code"
                                                name="customer_code">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request No.</label>
                                            <input type="text" class="form-control" placeholder="Request No."
                                                name="request_no">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>SO Name</label>
                                            <input type="text" class="form-control" placeholder="SO Name"
                                                name="so_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Distributor Name</label>
                                            <input type="text" class="form-control" placeholder="Distributor Name"
                                                name="distributor_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Customer Name"
                                                name="customer_name">
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Request Status</label>
                                            <select class="form-select select">
                                                <option>Task Pending</option>
                                                <option selected>Task Completed</option>
                                                <option value="?">Pending For ASM Approval</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>ASM Name</label>
                                            <input type="text" class="form-control" placeholder="ASM Name"
                                                name="asm_name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Customer City</label>
                                            <input type="text" class="form-control" placeholder="Customer City"
                                                name="customer_city">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="form-group">
                                            <label>Route Name</label>
                                            <input type="text" class="form-control" placeholder="Route Name"
                                                name="route_name">
                                        </div>
                                    </div>
                                    <div class=" col-lg-1 col-sm-6 col-12 ms-auto">
                                        <div class="form-group">
                                            <button class="btn submitbtn btn-filter" type="submit"><img
                                                    src="{{ asset('img/icons/search-whites.svg') }}" alt="img"></button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table  datanew">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>Request No</th>
                                    <th>Expected Date</th>
                                    <th>Asset Placement Status</th>
                                    <th>Completed Date</th>
                                    <th>Customer Code</th>
                                    <th>Customer Name</th>
                                    <th>Customer Address</th>
                                    <th>Pin Code</th>
                                    <th>Route Name</th>
                                    <th>City Name</th>
                                    <th>Channel Code</th>
                                    <th>Channel Name</th>
                                    <th>VPO(INR) Last 3P Average</th>
                                    <th>Distributor Name</th>
                                    <th>Distributor Code</th>
                                    <th>Contact Person</th>
                                    <th>Contact Number</th>
                                    <th>Chiller Type</th>
                                    <th>Asset Number</th>
                                    <th>Asset Barcode</th>
                                    <th>Distance to the store</th>
                                    <th>Can’t get location</th>
                                    <th>Reason for No Deployment</th>
                                    <th>SO Name</th>
                                    <th>SO Code</th>
                                    <th>ASM Name</th>
                                    <th>ASM Code</th>
                                    <th>Region</th>
                                    <th>Eligible Chiller Type</th>
                                    <th>Expected VPO</th>
                                    <th>Applied Chiller Type</th>
                                    <th>Additional Equipment</th>
                                    <th>Competition Chiller Size</th>
                                    <th>Competition Chiller Company</th>
                                    <th>Competition Chiller Photo</th>
                                    <th>Competition Chiller Location</th>
                                    <th>Retailer Proof Photo</th>
                                    <th>Retailer Picture</th>
                                    <th>Digital Sign</th>
                                    <th>Lat of Outlet</th>
                                    <th>Lon of Outlet</th>
                                    <th>Data Upload Time</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label class="checkboxs">
                                            <input type="checkbox">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </td>
                                    <td>APR10012278</td>
                                    <td>2024-01-30</td>
                                    <td>Task Completed</td>
                                    <td>2024-01-31</td>
                                    <td>RETN_255561</td>
                                    <td>Sri Venkatramana Sweets</td>
                                    <td>New 121 New Bus Stand Kumbakonam</td>
                                    <td></td>
                                    <td></td>
                                    <td>Kumbakonam</td>
                                    <td>00450006</td>
                                    <td>Traditional Trade</td>
                                    <td>0.00</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>9843936756</td>
                                    <td>70 LTR</td>
                                    <td>M21209425</td>
                                    <td>41922231102225</td>
                                    <td>0.00</td>
                                    <td>yes</td>
                                    <td></td>
                                    <td>Kaliyaperumal Kumar</td>
                                    <td>SO-TT-Kumbakonam</td>
                                    <td>M Gnanasekaran</td>
                                    <td>ASM-TT-Trichy</td>
                                    <td>South</td>
                                    <td></td>
                                    <td>3000.00</td>
                                    <td>70 LTR</td>
                                    <td>Stands</td>
                                    <td>90 LTR</td>
                                    <td>Mondelez</td>

                                    <td>
                                        <a data-bs-toggle="modal" data-bs-target="#modalImgView">
                                        <img class="table-report-img" onclick="viewImages(this)"
                                            src="{{ asset('img/brand/refrigerator-with-lots-food_1308-105555.jpg') }}"
                                            alt="">
                                            </a>
                                    </td>
                                    <td>Hot Zone</td>
                                    <td>
                                        <a data-bs-toggle="modal" data-bs-target="#modalImgView">
                                        <img class="table-report-img" onclick="viewImages(this)" src="{{ asset('img/brand/retailerPhoto.jpg') }}"
                                            alt="">
                                            </a>
                                        </td>
                                    <td>
                                        <a data-bs-toggle="modal" data-bs-target="#modalImgView">
                                        <img class="table-report-img" onclick="viewImages(this)" src="{{ asset('img/brand/retailerPic.avif') }}"
                                            alt="">
                                            </a>
                                        </td>
                                    <td>
                                        <a data-bs-toggle="modal" data-bs-target="#modalImgView">
                                        <img class="table-report-img" onclick="viewImages(this)" src="{{ asset('img/brand/sign2.avif') }}" alt="">
                                        </a>
                                    </td>
                                    <td>
                                        <a data-bs-toggle="modal" data-bs-target="#modalImgView">
                                            <img class="table-report-img" onclick="viewImages(this)" src="{{ asset('img/brand/outlet2.jpg') }}"
                                                alt="">
                                        </a>

                                    </td>
                                    <td></td>
                                    <td>2024-01-31 18:10:26</td>
                                    

                            </tbody>
                        </table>
                    </div>
                    <div class=" btn-approve-reject my-2">
                        <div class="d-flex justify-content-center justify-content-sm-end justify-content-lg-end">
                            <nav aria-label="Page navigation example">
                                <ul class="pagination round-pagination  bx-pull-right">
                                    <li class="page-item"><a class="page-link" href="javascript:;">Previous</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;javascript:;">1</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="javascript:;">2</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">3</a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="javascript:;">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

 <!-- Modal -->
 <div class="modal fade" id="modalImgView" tabindex="-1" aria-labelledby="modalImgView" aria-hidden="true"
 data-bs-backdrop="static">
 <div class="modal-dialog modal-dialog-centered">
     <div class="modal-content">
         <div class="modal-header">
             <h5 class="modal-title">View Images</h5>
             <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
         </div>
         <div class="modal-body">
             <div class="modal-img-parent-div">
                 <img class="modal-report-img" src="" id="modal_id_img_view" img-fluid alt="">
             </div>
         </div>
     </div>
 </div>
</div>
</x-app-layout>
